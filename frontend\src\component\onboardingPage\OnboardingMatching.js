import React, { useEffect, useState } from 'react';
import {OnboardingHeaderPart} from '../../header/OnboardingHeader';
import { useNavigate } from 'react-router-dom';
import Swal from 'sweetalert2';
import { FiMapPin } from 'react-icons/fi';
import { AiOutlineLoading } from 'react-icons/ai';
import { FaPen } from 'react-icons/fa';

const OnboardingCampaignMatching = () => {
    const navigate = useNavigate();
    //
    const [isLoading, setIsLoading] = useState(false);
    const roboman_api = process.env.REACT_APP_ROBOMAN_API;
    //
     
    const WelcomeString = () => {
        // This part is for the welcome string section

        const [showPopup, setShowPopup] = useState(false);
        const [location, setLocation] = useState(localStorage.getItem('location') || '');

        const handleConfirmLocation = () => {
            localStorage.setItem('location', location); // Save the new location to localStorage
            setShowPopup(false); // Close the popup
            window.location.reload(); // Reload the page
        };

        const handleClosePopup = () => {
          setShowPopup(false);
          setLocation(localStorage.getItem('location'))
        }

        return (
        <div>
            <h1 className="text-4xl font-bold text-center mb-3">
            Campaign Information
            </h1>
            <div className="flex items-center justify-center text-lg">
                <FiMapPin className="text-blue-500 mr-2" />
                Location: {location}
                <button
                    onClick={() => setShowPopup(true)}
                    className="ml-2 text-gray-600 hover:text-blue-500"
                >
                    <FaPen size={16} />
                </button>
            </div>

            {/* Popup for Changing Location */}
            {showPopup && (
                <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
                    <div className="bg-white p-6 rounded-lg shadow-xl w-96">
                        <h2 className="text-xl font-bold mb-4">Change Location</h2>
                        <input
                            type="text"
                            value={location}
                            onChange={(e) => setLocation(e.target.value)}
                            placeholder="Enter new location"
                            className="w-full px-3 py-2 border rounded mb-4"
                        />
                        <div className="flex justify-end">
                            <button
                                className="bg-gray-300 text-black px-4 py-2 rounded mr-2"
                                onClick={handleClosePopup}
                            >
                                Cancel
                            </button>
                            <button
                                className="bg-blue-500 text-white px-4 py-2 rounded"
                                onClick={handleConfirmLocation}
                            >
                                Confirm
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
        );
    };

    const TargetIndustry = () => {
      // This part is for the target industry section
      // Check if userData is available before rendering

        // if (!userData) return null;
        const [industries, setIndustries] = useState(
          JSON.parse(localStorage.getItem('industry')) || []
        );
        const [showPopup, setShowPopup] = useState(false);

        const handleAddIndustry = (e) => {
          if (e.key === "Enter" && e.target.value.trim() !== "") {
            const newIndustry = e.target.value.trim(); // Capture the input value
            setIndustries((prev) => [...prev, newIndustry]); // Add the new industry to the state
            e.target.value = ""; // Clear the input field
          }
        }

        const handleRemoveIndustry = (index) => {
          setIndustries((prev) => prev.filter((_, i) => i !== index));
        }

        const handleIndustryConfirm = () => {
          localStorage.setItem('industry', JSON.stringify(industries));
          setShowPopup(false);
          window.location.reload();
        }

        const handleClosePopup = () => {
          setShowPopup(false);
          setIndustries(JSON.parse(localStorage.getItem('industry')))
        }

        return (
          <div className="p-3 w-full h-full bg-white flex flex-col space-y-4 rounded-xl shadow-md items-center">
              {/* Title with Edit Button */}
              <div className="flex items-center justify-center text-lg font-bold">
                  TARGET INDUSTRY
                  <button
                      onClick={() => setShowPopup(true)}
                      className="ml-2 text-gray-600 hover:text-blue-500"
                  >
                      <FaPen size={16} />
                  </button>
              </div>
  
              {/* Industry List */}
              <div className="p-2 w-[90%] h-[280px] bg-white rounded-lg">
                  <div className="full flex flex-wrap gap-2 overflow-auto">
                      {industries.map((item, index) => (
                          <div
                              key={index}
                              className="bg-gray-100 px-3 py-2 rounded-lg shadow-md whitespace-nowrap h-[30px] flex items-center"
                          >
                              <strong>{item}</strong>
                          </div>
                      ))}
                      {industries.length === 0 && (
                          <div className="text-gray-500">No target industries specified</div>
                      )}
                  </div>
              </div>
              {/* Popup for Editing Industries */}
              {showPopup && (
                  <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
                      <div className="bg-white p-6 rounded-lg shadow-xl min-w-[500px] max-w-[900px]">
                          <h2 className="text-xl font-bold mb-4">Edit Target Industries</h2>
                          <div className="flex flex-wrap gap-2 mb-4">
                              {industries.map((item, index) => (
                                  <div
                                      key={index}
                                      className="bg-gray-100 px-3 py-2 rounded-lg shadow-md whitespace-nowrap h-[30px] flex items-center"
                                  >
                                      <strong>{item}</strong>
                                      <button
                                          className="ml-2 text-red-500 hover:text-red-700"
                                          onClick={() => handleRemoveIndustry(index)}
                                      >
                                          X
                                      </button>
                                  </div>
                              ))}
                          </div>
                          <input
                              type="text"
                              placeholder="Type and press Enter"
                              className="w-full px-3 py-2 border rounded mb-4"
                              onKeyDown={handleAddIndustry}
                          />
                          <div className="flex justify-end">
                              <button
                                  className="bg-gray-300 text-black px-4 py-2 rounded mr-2"
                                  onClick={handleClosePopup}
                              >
                                  Cancel
                              </button>
                              <button
                                  className="bg-blue-500 text-white px-4 py-2 rounded"
                                  onClick={handleIndustryConfirm}
                              >
                                  Search
                              </button>
                          </div>
                      </div>
                  </div>
              )}
          </div>
        );
      };
    
    const ContentBox = ({ company }) => {
      // This part is for the content box in the target client section

      // Check if the company data is "empty"
      const isEmpty = !company.company_name && !company.industry && !company.company_contact.rep_name;
    
      return (
        <div className="p-4 w-[400px] bg-gray-100 rounded-lg">
          {isEmpty ? (
            // If it's an empty box, show nothing
            <div className="h-full w-full bg-transparent"></div>
          ) : (
            <>
              <h1 className="text-blue-800 text-xl font-bold mb-2">{company.company_name || "N/A"}</h1>
              <ul className="list-disc pl-5">
                <li className="text-md mb-1">Industry: {company.industry || "N/A"}</li>
                <li className="text-md mb-1">
                  LinkedIn:{" "}
                  <a
                    href={company.company_contact?.company_linkedin || "#"}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-500 underline"
                  >
                    {company.company_linkedin || "N/A"}
                  </a>
                </li>
                <li className="text-md mb-1">
                  Representative: {company.rep_name || "N/A"}
                </li>
                <li className="text-md mb-1">
                  Email:{" "}
                  <a
                    href={`mailto:${company.rep_email || ""}`}
                    className="text-blue-500 underline"
                  >
                    {company.rep_email || "N/A"}
                  </a>
                </li>
              </ul>
            </>
          )}
        </div>
      );
    };    

    const TargetClient = () => {
      // This part show the target prospect for the campaign, by founding the matching company

      const [result, setResult] = useState([]);
      const [currentPage, setCurrentPage] = useState(1);
      const itemsPerPage = 6; // 2 columns * 3 rows

      const generateBlankObject = () => ({
        // Helper function to generate a single blank object
        company_name: "",
        industry: "",
        company_contact: {
          company_linkedin: "",
          rep_name: "",
          rep_email: "",
        },
      });

      const findMatchingData = async () => {
        // Fetch matching company data from the API
        try {
          const response = await fetch(`${roboman_api}/campaign/companies/match/v2`, {
            method: "POST",
            headers: {
              accept: "application/json",
              Authorization: localStorage.getItem("access_token"),
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              industry: JSON.parse(localStorage.getItem("industry")),
              location: localStorage.getItem("location"),
              industry_similarity_threshold: 0.69,
              location_similarity_threshold: 0.69,
              search_size: 200
            }),
          });

          if (!response.ok) {
            Swal.fire({
              icon: "error",
              title: "Oops...",
              text: "Failed to find matching company with this campaign! Please try agian",
            });
            setResult(Array(6).fill(generateBlankObject())); // Show 6 blank boxes if the fetch fails
            return;
          }

          const data = await response.json();
          // If the result is null or empty, show 6 default boxes
          if (!data || data.length === 0) {
            console.log("Not found matching data received from API");
          }
          setResult(data?.length > 0 ? data : Array(6).fill(generateBlankObject()));
        } catch (error) {
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "Something went wrong while finding matching company with this campaign!",
          });
          setResult(Array(6).fill(generateBlankObject())); // Show 6 blank boxes in case of error
        }
      };

      useEffect(() => {

        findMatchingData();
      }, []);

      // Fill missing items to match a multiple of 6
      const padResult = (data) => {
        const paddingNeeded = itemsPerPage - (data.length % itemsPerPage || itemsPerPage);
        return [...data, ...Array(paddingNeeded).fill(generateBlankObject())];
      };

      const paddedResult = padResult(result);

      // Paginate the padded result
      const indexOfLastItem = currentPage * itemsPerPage;
      const indexOfFirstItem = indexOfLastItem - itemsPerPage;
      const currentItems = paddedResult.slice(indexOfFirstItem, indexOfLastItem);

      const totalPages = Math.ceil(paddedResult.length / itemsPerPage);

      const handleNextPage = () => {
        if (currentPage < totalPages) {
          setCurrentPage((prev) => prev + 1);
        }
      };

      const handlePrevPage = () => {
        if (currentPage > 1) {
          setCurrentPage((prev) => prev - 1);
        }
      };

      return (
        <div className="p-3 bg-white flex flex-col space-y-4 rounded-xl h-full items-center shadow-md ">
          {/* Title */}
          <div className="text-center text-lg font-bold">TARGET CLIENTS</div>

          {/* Display current items with spacing */}
          <div
            className="w-full grid gap-4 p-2 grid-cols-1 sm:grid-cols-2 lg:grid-cols-2"
            style={{ gridAutoRows: "minmax(0, 1fr)" }}
          >
            {currentItems.length === 0 ? (
              // Show spinner when currentItems is empty
              <div className="flex justify-center items-center w-full h-[200px]">
                <AiOutlineLoading className="animate-spin text-blue-500 text-4xl" />
              </div>
            ) : (
              currentItems.map((company, index) => (
                <div key={index} className="flex justify-center">
                  <ContentBox company={company} />
                </div>
              ))
            )}
          </div>

          {/* Pagination */}
          <div className="flex justify-between w-full mt-4">
            <button
              onClick={handlePrevPage}
              disabled={currentPage === 1}
              className="px-4 py-2 bg-gray-300 rounded disabled:opacity-50"
            >
              Prev
            </button>
            <div className="flex items-center">
              Page {currentPage} of {totalPages}
            </div>
            <button
              onClick={handleNextPage}
              disabled={currentPage === totalPages}
              className="px-4 py-2 bg-gray-300 rounded disabled:opacity-50"
            >
              Next
            </button>
          </div>
        </div>
      );
    };


    const ProfilesPart = () => {
        // This part is for the profiles section
        return (
          <div className="w-full max-w-[1300px] flex flex-col lg:flex-row space-y-4 lg:space-y-0 lg:space-x-4">
            <div className="w-full lg:w-1/3 min-h-[400px]">
              <TargetIndustry />
            </div>
            <div className="w-full lg:w-2/3 min-h-[400px]">
              <TargetClient />
            </div>
          </div>
        );
    };

    const UserInfomationBoard = () => {
        // This part is for the user information board
        return (
            <div className="flex-grow flex flex-col items-center space-y-8 py-4 px-4 md:px-8">
                {/* First Part */}
                <WelcomeString />
                {/* Second Part */}
                <ProfilesPart />
            </div>
        );
    };

    const Footer = () => {
        // This part is for the footer section
        return (
        <div className='py-4 bg-white flex justify-end shadow-2xl'>
            <div className="mb-3 mt-3">
                <button 
                className="bg-[#223F9E] text-white font-semibold py-2 px-10 mr-[50px] rounded-full"
                onClick={handleNextClick}
                >
                Next
                </button>
            </div>
        </div>
        )
    }


    const handleNextClick = () => {
        // This function for Next button in the footer
        navigate('/reviewinfo');
    };

    const handleChangeLocation = () => {

    }

    const handleChangeTargetIndustry = () => {
        // This function for the change target industry button
    }

    return (

    <div>
    {isLoading && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
        <div className="bg-white p-6 rounded shadow-lg">
            <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            <span className="text-lg font-medium">Loading, please wait...</span>
            </div>
        </div>
        </div>
    )}
    {/* Render user data or other components here */}
        <div className="flex flex-col min-h-screen w-full bg-gray-100">
            {/* This part is for the header section */}
            <OnboardingHeaderPart />
            {/* This part is for the main/body section */}
            <UserInfomationBoard />
            {/* Footer is placed here to ensure it stays at the bottom */}
            <Footer />
        </div>
    </div>   

  );
};

export default OnboardingCampaignMatching;
