from database.database import P<PERSON>T<PERSON>ES_DATABASEURL, engine, SessionFactory
from database.models import User, SystemRole, role_permission
from core.security import hash_password
from utils.migrate import run_migration
from sqlalchemy_utils import database_exists, create_database
from loguru import logger
import pandas as pd
import os


def init_data() -> None:
    session = SessionFactory()
    # Tables should be created with Alembic migrations
    init_data_path = os.path.join(os.path.dirname(__file__), "database/system_data")
    files = [
        "system_role.csv",
        "email_confirmation_status.csv",
    ]
    for file in files:
        file_path = os.path.join(init_data_path, file)
        df = pd.read_csv(file_path, sep=",")
        logger.info(f"{file}  load successed")
        table_name = file.replace(".csv", "")
        df.to_sql(table_name, engine, if_exists="append", index=False)

    session.commit()
    session.close()

def init_user() -> None:
    session = SessionFactory()
    hashed_password = hash_password('12345678')
    new_user = User(
        email = '<EMAIL>',
        password = hashed_password.decode("utf-8"),
    )

    user_role_id = session.query(
        SystemRole
    ).filter(
        SystemRole.role_id == 2
    ).first()
    
    new_user.system_role.append(user_role_id)
    session.add(new_user)
    session.commit()
    session.close()


if __name__=='__main__':
    if not database_exists(POSTGRES_DATABASEURL):
        create_database(POSTGRES_DATABASEURL)
    run_migration()
    # try:
    #     logger.info('Init role data......')
    #     init_data()
    #     logger.success('Done!')

    #     logger.info('Init admin user...')
    #     init_user()
    #     logger.success('Done!')
    # except Exception as e:
    #     logger.error(f'{e}')
    #     pass