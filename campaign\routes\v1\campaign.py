from database.database import get_session
from message_bus.rpc_client import RPC<PERSON><PERSON>
from utils.elasticsearch_utils.get_elasticsearch import get_elastic
from utils.elasticsearch_utils.elastic_query import string_matching_search, semantic_search, search_primary_rep, search_matching_companies, test_string_matching_search, hybrid_search
from utils.unipile_utils.unipile_client import EmailClient, LinkedInClient
from message_bus.task_publisher import publish_task
from shared_state import app_state
from core.config import settings
from schema.campaign_schema import (CampaignCreate, 
                                    CampaignDetails, CampaignContentUpdate, 
                                    CampaignSendLinkedInMessage, CampaignDetailsUpdate,
                                    TestScheduleJob, CompanyMatching, CampaignContact)
from schema.company_schema import (CompanyEmailUpdate, CompanyAddManual, 
                                   CompanyLinkedInMsgUpdate)
from schema.rep_schema import (RepUpdate)

from schema.webhooks_schema import (WebhooksEmailTracking, WebhooksNewEmail)

from schema.outreach_schema import (<PERSON>ailGeneration, EmailSend,
                                    CreateDraftEmail,EmailOutreach,
                                    UpdateDraftEmail, SendEmailRequest, 
                                    RespondEmail)

from database.models import (Campaign, Company, 
                             Representative, SentEmail, 
                             ReceivedEmail, Draft,
                             EmailConfirmationStatus, SenderEmail,
                             HumanticProfile)

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import update, delete, func, distinct
from sqlalchemy.future import select
from sqlalchemy.orm import aliased
from fastapi import APIRouter, Header, Depends, Query
from fastapi import HTTPException, status, Request
from fastapi import status, File, UploadFile
from sqlalchemy.future import select
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk, BulkIndexError
from typing import List
import uuid
import pandas as pd
from io import StringIO
import requests
from loguru import logger
import asyncio
import json

router = APIRouter(
    prefix = "/api",
    tags=['Campaign Management'],
    responses={404: {'description': 'Not found'}},
)

@router.get("/campaign/elasticsearch-healthcheck")
async def elasticsearch_healthcheck(es: Elasticsearch = Depends(get_elastic)):
    if(es.ping()):
        return {
            "status" : "ok"
        }
    else:
        raise HTTPException(
            status_code=503,
            detail="Elasticsearch service not available!"
        )

#region CAMPAIGN
@router.post("/campaign/new", status_code=status.HTTP_200_OK)
async def create_new_campaign(
    campaign_create: CampaignCreate,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session),
    es: Elasticsearch = Depends(get_elastic)
):
    #get user info
    rpc_client = RPCClient()
    rpc = await rpc_client.connect()
    function_name = "get_user_info"
    response = await rpc.call(service="user.*", function_name=function_name, params={"user_id": request_user_id})
    response_body = json.loads(response)
    result = response_body.get("result")
    user_nick_name = None
    calendly_link = None
    if result != "error":
        user_nick_name = result["user_info"]["nick_name"]
    else:
        logger.warning("Failed to retrieve user info")

    campaign_info_data = {
        "core_service": campaign_create.core_service,
        "unique_selling_proposition": campaign_create.unique_selling_proposition,
        "target_audience": campaign_create.target_audience,
        "problem_solved": campaign_create.problem_solved,
        "key_benefits": campaign_create.key_benefits,
        "primary_goal_of_outreach_campaign": campaign_create.primary_goal_of_outreach_campaign,
        "ideal_client": campaign_create.ideal_client,
        "success_measurement": campaign_create.success_measurement,
        "industry": campaign_create.industry,
        "location": campaign_create.location,
        "must_have_info": campaign_create.must_have_info
    }

    campaign_to_create = Campaign(
        campaign_name=campaign_create.campaign_name,
        campaign_info=campaign_info_data,
        email_format = campaign_create.email_format,
        linkedin_msg_format = campaign_create.linkedin_msg_format,
        user_id=request_user_id,
        campaign_contact = {
            "user_nick_name": user_nick_name,
            "calendly_link": calendly_link
        }
    )
    try:
        db.add(campaign_to_create)
        await db.commit()

        return {'status': 'success'}
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/campaign/companies/match/test")
async def test_matching_companies(
    uploaded_files: UploadFile = File(...),
    es: Elasticsearch = Depends(get_elastic)
):
    
    # Check if file is a JSON file
    if not uploaded_files.filename.endswith('.json'):
        raise HTTPException(status_code=415, detail="File must be a JSON")

    # Read file contents
    contents = await uploaded_files.read()

    try:
        # Convert bytes to dict
        data = json.loads(contents)
 
        search_results = test_string_matching_search(es, data)

        companies = []
        for company in search_results:
            companies.append({
                'company_name': company['Company Name'],
                'industry': company['Company Industry'],
                'company_linkedin': company['Company Linkedin Url'],
                'location_locality': company['Company Location Locality'],
                'location_region': company['Company Location Region'],
                'location_country': company['Company Location Country'],
                'location_continent': company['Company Location Continent']
            })

        return companies
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON format")       
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/campaign/companies/match/string-matching")
async def string_matching_companies(
    company_matching: CompanyMatching, 
    db: AsyncSession = Depends(get_session),
    es: Elasticsearch = Depends(get_elastic)
):
    try:
        search_results = string_matching_search(
            es, 
            industry_list=company_matching.industry,
            location=company_matching.location,
            search_size=100
            )

        companies = []
        for company in search_results:
            rep_name = company['Full name'].title()
            rep_emails = company['Emails']
            companies.append({
                'company_name': company['Company Name'],
                'industry': company['Company Industry'],
                'company_linkedin': company['Company Linkedin Url'],
                'location_locality': company['Company Location Locality'],
                'location_region': company['Company Location Region'],
                'location_country': company['Company Location Country'],
                'location_continent': company['Company Location Continent']
            })

        return companies
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/campaign/companies/match/hybrid")
async def hybrid_matching_companies(
    company_matching: CompanyMatching, 
    db: AsyncSession = Depends(get_session),
    es: Elasticsearch = Depends(get_elastic)
):
    try:

        rpc = await RPCClient().connect()
        function_name = "get_text_embedding"
        texts_to_embed = company_matching.industry + [company_matching.location]
        texts_to_embed = [text_.lower() for text_ in texts_to_embed]

        response = await rpc.call(service="aiengine.*", function_name=function_name, params={"text_list": texts_to_embed})
        response_body = json.loads(response)
        result = response_body.get("result")

        if result == "error":
            raise Exception(
                "Error from RPC server: " + response_body.get("detail")
            )

        industry_list_embedding = result["embeddings_list"][:-1]
        location_embedding = result["embeddings_list"][-1]

        search_results = hybrid_search(
            es_client=es, 
            industry_list=company_matching.industry,
            location=company_matching.location,
            location_embedding=location_embedding,
            search_size=company_matching.search_size,
            location_similarity_threshold=company_matching.location_similarity_threshold,
            existing_company_urls=[]
        )

        companies = []
        for company in search_results:
            rep_name = company['Full name'].title()
            rep_emails = company['Emails']
            companies.append({
                'company_name': company['Company Name'],
                'industry': company['Company Industry'],
                'company_linkedin': company['Company Linkedin Url'],
                'location_locality': company['Company Location Locality'],
                'location_region': company['Company Location Region'],
                'location_country': company['Company Location Country'],
                'location_continent': company['Company Location Continent']

            })
        return companies

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )    


@router.post("/campaign/companies/match/semantic")
async def semantic_matching_companies(
    company_matching: CompanyMatching, 
    db: AsyncSession = Depends(get_session),
    es: Elasticsearch = Depends(get_elastic)
):
    try:

        rpc = await RPCClient().connect()
        function_name = "get_text_embedding"
        texts_to_embed = company_matching.industry + [company_matching.location]
        texts_to_embed = [text_.lower() for text_ in texts_to_embed]

        response = await rpc.call(service="aiengine.*", function_name=function_name, params={"text_list": texts_to_embed})
        response_body = json.loads(response)
        result = response_body.get("result")

        if result == "error":
            raise Exception(
                "Error from RPC server: " + response_body.get("detail")
            )

        industry_list_embedding = result["embeddings_list"][:-1]
        location_embedding = result["embeddings_list"][-1]

        search_results = semantic_search(
            es_client=es, 
            location=company_matching.location,
            industry_list_embedding=industry_list_embedding, 
            location_embedding=location_embedding,
            search_size=company_matching.search_size,
            industry_similarity_threshold=company_matching.industry_similarity_threshold,
            location_similarity_threshold=company_matching.location_similarity_threshold,
            industry_weight=company_matching.industry_weight,
            location_weight=company_matching.location_weight,
            existing_company_urls=[]
        )

        companies = []
        for company in search_results:
            rep_name = company['Full name'].title()
            rep_emails = company['Emails']
            companies.append({
                'company_name': company['Company Name'],
                'industry': company['Company Industry'],
                'company_linkedin': company['Company Linkedin Url'],
                'location_locality': company['Company Location Locality'],
                'location_region': company['Company Location Region'],
                'location_country': company['Company Location Country'],
                'location_continent': company['Company Location Continent']

            })
        return companies

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )    

@router.post("/campaign/companies/match/v2")
async def get_matching_companies_v2(
    company_matching: CompanyMatching, 
    db: AsyncSession = Depends(get_session),
    es: Elasticsearch = Depends(get_elastic)
):
    try:
        search_results = await search_matching_companies(
            es, 
            company_matching.industry, 
            company_matching.location, 
            search_size=company_matching.search_size,
            industry_similarity_threshold=company_matching.industry_similarity_threshold,
            location_similarity_threshold=company_matching.location_similarity_threshold,
            industry_weight=company_matching.industry_weight,
            location_weight=company_matching.location_weight
        )

        companies = []
        for company in search_results:
            rep_name = company['Full name'].title()
            rep_emails = company['Emails']
            company_location = company['Company Location Name'] + ', ' + company['Company Location Continent']
            companies.append({
                'company_name': company['Company Name'],
                'industry': company['Company Industry'],
                'company_linkedin': company['Company Linkedin Url'],
                'company_location': company_location,
                'rep_name': rep_name,
                'rep_email' : rep_emails.split(',')[-1].strip() if rep_emails else None,
                'rep_linkedin_address': company['LinkedIn Url']
            })
        return companies
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )


@router.get("/campaign/personal", status_code=status.HTTP_200_OK)
async def get_personal_campaign(
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    all_campaigns = await db.execute(
        select(
            Campaign
        ).filter(
            Campaign.user_id==request_user_id
        ).order_by(
            Campaign.created_at.desc()
        )
    )
    all_campaigns = all_campaigns.scalars().all()

    campaign_list = []
    for campaign in all_campaigns:
        campaign_id = campaign.campaign_id
        campaign_dict = campaign.__dict__

        query = select(func.count(Company.company_id)).filter(Company.campaign_id==campaign_id)
        result = await db.execute(query)
        prospects_count = result.scalar()

        query = select(func.count(Company.company_id)).filter(Company.campaign_id==campaign_id, Company.email_confirmation_status_id.in_([4,5,6]))
        result = await db.execute(query)
        sent_count = result.scalar()

        query = select(func.count(Company.company_id)).filter(Company.campaign_id==campaign_id, Company.email_confirmation_status_id.in_([5,6]))
        result = await db.execute(query)
        opened_count = result.scalar()        

        query = select(func.count(Company.company_id)).filter(Company.campaign_id==campaign_id, Company.email_confirmation_status_id==6)
        result = await db.execute(query)
        replied_count = result.scalar()    

        stats = {
            "prospects_count": prospects_count,
            "sent": {
                "count": sent_count,
                "rate": 0.0 if (prospects_count==0) else sent_count/prospects_count
            },
            "opened": {
                "count": opened_count,
                "rate": 0.0 if sent_count==0 else opened_count/sent_count
            },
            "replied": {
                "count": replied_count,
                "rate": 0.0 if sent_count==0 else replied_count/sent_count
            },
            "interested": {
                "count": 0,
                "rate": 0.0            
            }
        }
        campaign_dict["stats"] = stats
        campaign_list.append(campaign_dict)

    return campaign_list


@router.get("/campaign/{campaign_id}", status_code=status.HTTP_200_OK)
async def get_campaign(
    campaign_id: uuid.UUID,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)           
):
    query = select(Campaign).where(Campaign.campaign_id==campaign_id)
    results = await db.execute(query)
    campaign = results.scalars().first()

    return campaign 


@router.get("/campaign/status/")
async def get_user_campaign_status(
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)    
):
    query = select(Campaign.campaign_id).where(Campaign.user_id==request_user_id)
    results = await db.execute(query)
    campaign_id_list = results.scalars().all()

    query = select(func.count(Company.company_id)).filter(Company.campaign_id.in_(campaign_id_list))
    results = await db.execute(query)
    num_companies = results.scalar()

    query = select(func.count(Company.company_id)).filter(Company.campaign_id.in_(campaign_id_list), Company.email_confirmation_status_id==1)
    result = await db.execute(query)
    not_started = result.scalar()

    query = select(func.count(Company.company_id)).filter(Company.campaign_id.in_(campaign_id_list), Company.email_confirmation_status_id==2)
    result = await db.execute(query)
    waiting_to_review = result.scalar()    

    query = select(func.count(Company.company_id)).filter(Company.campaign_id.in_(campaign_id_list), Company.email_confirmation_status_id==3)
    result = await db.execute(query)
    reviewed = result.scalar()  

    query = select(func.count(Company.company_id)).filter(Company.campaign_id.in_(campaign_id_list), Company.email_confirmation_status_id.in_([4,5,6]))
    result = await db.execute(query)
    sent = result.scalar()
    
    query = select(func.count(Company.company_id)).filter(Company.campaign_id.in_(campaign_id_list), Company.email_confirmation_status_id.in_([5,6]))
    result = await db.execute(query)
    opened = result.scalar()    

    query = select(func.count(Company.company_id)).filter(Company.campaign_id.in_(campaign_id_list), Company.email_confirmation_status_id==6)
    result = await db.execute(query)
    replied = result.scalar()    

    return {
        'num_of_campaigns': len(campaign_id_list),
        'num_of_companies': num_companies,
        'email_status_count': {
            'not started': not_started,
            'waiting to review': waiting_to_review,
            'reviewed': reviewed,
            'sent': sent,
            'opened': opened,
            'replied': replied,
        }
    }

@router.get("/campaign/status/{campaign_id}")
async def get_single_campaign_status(
    campaign_id: uuid.UUID,
    db: AsyncSession = Depends(get_session) 
):
    query = select(Company.company_id).where(Company.campaign_id==campaign_id)
    results = await db.execute(query)
    company_list = results.scalars().all()
    num_companies = len(company_list)

    query = select(func.count(Company.company_id)).filter(Company.company_id.in_(company_list), Company.email_confirmation_status_id==1)
    result = await db.execute(query)
    not_started = result.scalar()

    query = select(func.count(Company.company_id)).filter(Company.company_id.in_(company_list), Company.email_confirmation_status_id==2)
    result = await db.execute(query)
    waiting_to_review = result.scalar()    

    query = select(func.count(Company.company_id)).filter(Company.company_id.in_(company_list), Company.email_confirmation_status_id==3)
    result = await db.execute(query)
    reviewed = result.scalar()  

    query = select(func.count(Company.company_id)).filter(Company.company_id.in_(company_list), Company.email_confirmation_status_id.in_([4,5,6]))
    result = await db.execute(query)
    sent = result.scalar()
    
    query = select(func.count(Company.company_id)).filter(Company.company_id.in_(company_list), Company.email_confirmation_status_id.in_([5,6]))
    result = await db.execute(query)
    opened = result.scalar()    

    query = select(func.count(Company.company_id)).filter(Company.company_id.in_(company_list), Company.email_confirmation_status_id==6)
    result = await db.execute(query)
    replied = result.scalar()      

    return {
        'num_of_companies': num_companies,
        'email_status_count': {
            'not started': not_started,
            'waiting to review': waiting_to_review,
            'reviewed': reviewed,
            'sent': sent,
            'opened': opened,
            'replied': replied,
        }
    }

@router.put("/campaign/{campaign_id}/update-content-format")
async def update_content_format(
    campaign_id: uuid.UUID,
    campaign_content_update: CampaignContentUpdate,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)    
):
    try:    
        query = select(Campaign).filter(Campaign.campaign_id==campaign_id, Campaign.user_id==request_user_id)
        results = await db.execute(query)
        campaign_to_update = results.scalars().first()
        if campaign_to_update:
            campaign_name = campaign_to_update.campaign_name
            data = {key: value for key, value in campaign_content_update.dict().items() if value is not None}
            stmt = (
                update(Campaign)
                .filter(Campaign.campaign_id==campaign_id)
                .values(**data)
                .execution_options(synchronize_session="fetch")
            )   
            await db.execute(stmt)
            await db.commit()
            return {
                'status': 'updated successfully',
                'detail' : {
                    'campaign': f"{campaign_name}:{campaign_id}"                    
                }
            }            
        else:
            raise HTTPException(
                status_code=404,
                detail='campaign not found'
            ) 
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )    

@router.put("/campaign/{campaign_id}/update-details")
async def update_campaign_details(
    campaign_id : uuid.UUID,
    campaign_update : CampaignDetailsUpdate,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)    
):
    try:
        query = select(Campaign).where(Campaign.campaign_id == campaign_id)
        results = await db.execute(query)
        campaign = results.scalars().first()
        if campaign == None:
            raise HTTPException(
                status_code=404,
                detail=f"campaign {campaign_id} not found"
            )        
        
        campaign_info = {
            "campaign_name": campaign.campaign_name if campaign_update.campaign_name == None else campaign_update.campaign_name,
            "core_service": campaign.campaign_info['core_service'] if campaign_update.core_service == None else campaign_update.core_service,
            "unique_selling_proposition": campaign.campaign_info['unique_selling_proposition'] if campaign_update.unique_selling_proposition == None else campaign_update.unique_selling_proposition,
            "target_audience": campaign.campaign_info['target_audience'] if campaign_update.target_audience == None else campaign_update.target_audience,
            "problem_solved": campaign.campaign_info['problem_solved'] if campaign_update.problem_solved == None else campaign_update.problem_solved,
            "key_benefits": campaign.campaign_info['key_benefits'] if campaign_update.key_benefits == None else campaign_update.key_benefits,
            "primary_goal_of_outreach_campaign": campaign.campaign_info['primary_goal_of_outreach_campaign'] if campaign_update.primary_goal_of_outreach_campaign == None else campaign_update.primary_goal_of_outreach_campaign,
            "ideal_client": campaign.campaign_info['ideal_client'] if campaign_update.ideal_client == None else campaign_update.ideal_client,
            "success_measurement": campaign.campaign_info['success_measurement'] if campaign_update.success_measurement == None else campaign_update.success_measurement,
            "industry": campaign.campaign_info['industry'] if campaign_update.industry == None else campaign_update.industry,
            "location": campaign.campaign_info['location'] if campaign_update.location == None else campaign_update.location,
            "must_have_info": campaign.campaign_info['must_have_info'] if campaign_update.must_have_info == None else campaign_update.must_have_info
        }

        email_format = campaign.email_format if campaign_update.email_format == None else campaign_update.email_format
        linkedin_msg_format = campaign.linkedin_msg_format if campaign_update.linkedin_msg_format == None else campaign_update.linkedin_msg_format

        stmt = (
            update(Campaign)
            .filter(Campaign.campaign_id==campaign_id)
            .values(campaign_info=campaign_info, email_format=email_format, linkedin_msg_format=linkedin_msg_format)
            .execution_options(synchronize_session="fetch")
        )          
        await db.execute(stmt)
        await db.commit()
        return {
            'status': 'updated successfully',
            'detail' : {
                'campaign': f"{campaign.campaign_name}:{campaign_id}"                    
            }
        }          
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.put("/campaign/{campaign_id}/update-calendly")
async def update_campaign_contact(
    campaign_id: uuid.UUID,
    campaign_contact: CampaignContact,
    request_user_id: str = Header(None),    
    db: AsyncSession = Depends(get_session)
):
    try:
        query = select(Campaign).filter(Campaign.campaign_id==campaign_id)
        results = await db.execute(query)
        campaign = results.scalars().first()

        current_campaign_contact = campaign.campaign_contact
        current_campaign_contact["calendly_link"] = campaign_contact.calendly_link

        stmt = update(Campaign).filter(Campaign.campaign_id==campaign_id).values(campaign_contact=current_campaign_contact)
        await db.execute(stmt)
        await db.commit()
        return {
            "status": "success"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.delete("/campaign/{campaign_id}/delete")
async def delete_campaign_by_id(
    campaign_id: uuid.UUID,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    try:    
        query = select(Campaign).filter(Campaign.campaign_id==campaign_id, Campaign.user_id==request_user_id)
        results = await db.execute(query)
        campaign_to_delete = results.scalars().first()
        if campaign_to_delete:
            campaign_name = campaign_to_delete.campaign_name
            stmt = delete(Campaign).where(Campaign.campaign_id==campaign_id)
            await db.execute(stmt)
            await db.commit()
            return {
                'status': 'deleted successfully',
                'detail' : {
                    'campaign': f"{campaign_name}:{campaign_id}"                    
                }
            }            
        else:
            raise HTTPException(
                status_code=404,
                detail='campaign not found'
            ) 
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )
#endregion   

    
@router.post("/campaign/elastic-search/ingest")
async def ingest_companies(
    uploaded_files: UploadFile = File(...),
    db: AsyncSession = Depends(get_session),
    es: Elasticsearch = Depends(get_elastic),
):
    if not uploaded_files.filename.endswith('.csv'):
        return {"error": "File must be a CSV"}
    
    try:
        # Read the contents of the file
        contents = await uploaded_files.read()
        # Decode bytes to string
        str_file = contents.decode()
        # Create a StringIO object for pandas to read
        csv_file = StringIO(str_file)

        # df = pd.read_csv(csv_file, dtype=str, encoding='utf-8', keep_default_na=False)
        for chunk in pd.read_csv(csv_file, dtype=str, encoding='utf-8', keep_default_na=False, chunksize=2000, on_bad_lines="skip"):
            df_json_str = chunk.to_json(orient='records')
            await publish_task(task_type="ingest_data", message_body={"df_json_str": df_json_str}, connection_pool=app_state["rabbitmq_conn_pool"])

        return {
            "status": "success"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str
        )       

@router.get("/campaign/elastic-search/records/{page_size}/{from_}")
async def get_es_records(
    page_size: int,
    from_: int,
    es: Elasticsearch = Depends(get_elastic)
):
    try:
        query={
            "query": {"match_all": {}},
            "_source": {
                "excludes": [
                    "Company Industry embedding",
                    "Company Location Name embedding",
                    "Company Location Continent embedding"
                ]
            }            
        }
        search_result = es.search(index=settings.ES_INDEX_NAME, body=query, from_=from_, size=page_size).body['hits']['hits']
        result = [item['_source'] for item in search_result]  
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.delete("/campaign/elastic-search/remove-index")
async def remove_es_index(
    es: Elasticsearch = Depends(get_elastic)
):
    try:
        response = es.indices.delete(index=settings.ES_INDEX_NAME)
        return {
            'status': 'success',
            'detail': f"Index '{settings.ES_INDEX_NAME}' deleted successfully."
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/admin/senders/add")
async def add_sender(
    uploaded_files: UploadFile = File(...),    
    db: AsyncSession = Depends(get_session)
):
    if not uploaded_files.filename.endswith('.csv'):
        raise HTTPException(status_code=415, detail="File must be a CSV") 
    
    try:
        # Read the contents of the file
        contents = await uploaded_files.read()
        # Decode bytes to string
        str_file = contents.decode()
        # Create a StringIO object for pandas to read
        csv_file = StringIO(str_file)          

        senders_to_add = []
        df = pd.read_csv(csv_file, dtype=str, encoding='utf-8', keep_default_na=False)
        for i in range(df.shape[0]):
            sender_address = df.loc[i]["email"]
            sender_first_name = df.loc[i]["first_name"]
            sender_last_name = df.loc[i]["last_name"]
            sender_imap_host = df.loc[i]["imap_host"]
            sender_imap_port = df.loc[i]["imap_port"]
            sender_imap_username = df.loc[i]["imap_username"]
            sender_imap_password = df.loc[i]["imap_password"]
            sender_smtp_host = df.loc[i]["smtp_host"]
            sender_smtp_port = df.loc[i]["smtp_port"]
            sender_smtp_username = df.loc[i]["smtp_username"]
            sender_smtp_password = df.loc[i]["smtp_password"]
            
            logger.info(f"adding sender {sender_address}")

            query = select(SenderEmail).filter(SenderEmail.sender_address==sender_address)
            results = await db.execute(query)
            existing_sender = results.scalars().first()
            if existing_sender:
                continue

            email_client = EmailClient()
            response = email_client.connect_imap_account(
                imap_user=sender_imap_username,
                smtp_user=sender_smtp_username,
                imap_password=sender_imap_password, 
                smtp_password=sender_smtp_password, 
                imap_host=sender_imap_host, 
                smtp_host=sender_smtp_host, 
                imap_port=int(sender_imap_port), 
                smtp_port=int(sender_smtp_port)
            )
            if response.status_code in [200,201]:
                new_sender = SenderEmail(
                    sender_address = sender_address,
                    sender_first_name = sender_first_name,
                    sender_last_name = sender_last_name,
                    imap_host = sender_imap_host,
                    imap_port = int(sender_imap_port),
                    imap_username = sender_imap_username,
                    imap_password = sender_imap_password,
                    smtp_host = sender_smtp_host,
                    smtp_port = int(sender_smtp_port),
                    smtp_username = sender_smtp_username,
                    smtp_password = sender_smtp_password,
                    unipile_account_id = response.json()["account_id"]
                )
                senders_to_add.append(new_sender)
            else:
                logger.error(f"failed to connect to imap account {sender_address}\n {response.text}")

        db.add_all(senders_to_add)
        await db.commit()
        return {
            "status": "success"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.get("/admin/senders")
async def get_email_senders(
    db: AsyncSession = Depends(get_session)
):
    try:
        query = select(SenderEmail)
        results = await db.execute(query)
        senders = results.scalars().all()
        senders_list = []
        for sender in senders:
            senders_list.append({
                "sender_id": sender.sender_id,
                "sender_address": sender.sender_address,
                "sender_first_name": sender.sender_first_name,
                "sender_last_name": sender.sender_last_name,
                "imap_host": sender.imap_host,
                "imap_port": sender.imap_port,
                "smtp_host": sender.smtp_host,
                "smtp_port": sender.smtp_port,
                "unipile_account_id": sender.unipile_account_id,
                "remaining_emails": sender.remaining_emails
            })
        return senders_list

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.delete("/admin/senders/{sender_id}/delete")
async def delete_email_sender(
    sender_id: uuid.UUID,
    db: AsyncSession = Depends(get_session)
):
    try:
        query = select(SenderEmail).filter(SenderEmail.sender_id==sender_id)
        results = await db.execute(query)
        sender_to_delete = results.scalars().first()
        if sender_to_delete:
            sender_address = sender_to_delete.sender_address
            stmt = delete(SenderEmail).where(SenderEmail.sender_id==sender_id)
            await db.execute(stmt)
            await db.commit()

            email_client = EmailClient()
            response = email_client.delete_account(sender_to_delete.unipile_account_id)
            if response.status_code not in [200,201]:
                logger.error(f"failed to delete account {sender_address} from unipile\n{response.text}")

            return {
                'status': 'deleted successfully',
                'detail': {
                    'sender': f"{sender_address}:{sender_id}"
                }
            }
        else:
            raise HTTPException(
                status_code=404,
                detail='sender not found'
            )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

#endregion

campaign_routes = router
