from __future__ import annotations

import uuid
from sqlalchemy import String, Boolean, Column, ForeignKey, TIMESTAMP, Float, SmallInteger, Enum, CheckConstraint, Date, DateTime
from database.database import Base
from sqlalchemy.dialects.postgresql import UUID, ARRAY, TEXT, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func


class SystemPrompt(Base):
    __tablename__ = 'system_prompts'
    # Column mappings
    prompt_id = Column(
        UUID(as_uuid=True), primary_key=True, nullable=False,
        default=uuid.uuid4, index=True,
    )

    chosen_position = Column(String, unique=True, nullable=True)

    prompt_content = Column(String, nullable=False)
    temperature = Column(Float, nullable=False)
    presence_penalty = Column(Float, nullable=False)
    frequency_penalty = Column(Float, nullable=False)
    is_disabled = Column(Boolean, server_default='False')

    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False, server_default=func.now(),
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False, server_default=func.now(),
    )


class Conversation(Base):
    __tablename__ = 'conversations'

    conversation_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True,
    )

    conversation_name = Column(String(255), nullable=False, default="User Conversation")

    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False, server_default=func.now(),
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False, server_default=func.now(),
    )

    user_id = Column(
        UUID(as_uuid=True),
        nullable=False,
    )

    # Relationship
    messages = relationship('ConversationMessage', back_populates='conversation')


class ConversationMessage(Base):
    __tablename__ = 'conversation_messages'

    chat_message_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True,
    )
    message_data = Column(JSONB)
    user_id = Column(
        UUID(as_uuid=True),
        nullable=False,
    )

    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False, server_default=func.now(),
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False, server_default=func.now(),
    )

    # Foreign Keys
    conversation_id = Column(
        UUID(as_uuid=True), ForeignKey(
            'conversations.conversation_id',
        ), nullable=False,
    )

    # Relationship
    conversation = relationship('Conversation', back_populates="messages")


class Campaign(Base):
    __tablename__ = 'campaigns'

    campaign_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True,
    )

    campaign_name = Column(String(255), nullable=False, default="User Campaign")

    campaign_info = Column(JSONB)

    campaign_contact = Column(JSONB)

    email_format = Column(String, default="Write an email of 300 words")

    linkedin_msg_format = Column(String, default="Write a message of 100 words")

    campaign_auto_outreach = Column(Boolean, nullable=True, default=False)

    prompt_name = Column(String, nullable=True, default=None)

    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False, server_default=func.now(),
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False, 
        server_default=func.now(),
        onupdate=func.current_timestamp()
    )

    user_id = Column(
        UUID(as_uuid=True),
        nullable=False,
    )

class Company(Base):
    __tablename__ = "companies"

    company_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True,        
    )

    campaign_id = Column(
        UUID(as_uuid=True),
        ForeignKey('campaigns.campaign_id', ondelete="CASCADE"),
        nullable=False,

    )    
    company_name = Column(String(255), nullable=False, default=None)

    company_email = Column(String(255), nullable=True, default=None)

    company_linkedin = Column(String(255), nullable=True, default=None)

    industry = Column(String, nullable=False, default=None)

    content_subject = Column(String(255), default=None)

    content = Column(String, default=None)

    linkedin_message = Column(String, default=None)

    outreach_progress = Column(SmallInteger, nullable=True, default=None)

    linkedin_outreach_progress = Column(SmallInteger, nullable=True, default=None)

    # auto_email_outreach = Column(Boolean, nullable=True, default=False)

    # auto_linkedin_outreach = Column(Boolean, nullable=True, default=False)    

    auto_outreach = Column(Boolean, nullable=True, default=False)

    credit_spent = Column(Boolean, nullable=True, default=False)

    last_date_sent_email = Column(
        Date,
        nullable=True,
        default=None
    )

    last_date_sent_linkedin = Column(
        Date,
        nullable=True,
        default=None
    )

    last_time_sent_email = Column(
        DateTime,
        nullable=True,
        default=None
    )

    last_time_sent_linkedin = Column(
        DateTime,
        nullable=True,
        default=None
    )

    email_confirmation_status_id = Column(
        SmallInteger,
        ForeignKey('email_confirmation_status.status_id'),
        nullable=False,
        default=1
    )    

    linkedin_message_status_id = Column(
        SmallInteger,
        ForeignKey('linkedin_message_status.status_id'),
        nullable=False,
        default=1
    )

    email_confirmation_status = relationship("EmailConfirmationStatus", back_populates="companies")
    
    linkedin_message_status = relationship("LinkedInMessageStatus", back_populates="companies")

    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.current_timestamp()
    )


class EmailConfirmationStatus(Base):
    __tablename__ = 'email_confirmation_status'

    status_id = Column(
        SmallInteger,
        primary_key=True,
        index=True,
        autoincrement=True
    )

    status_value = Column(
        String(32), 
        unique=True
    )

    companies = relationship("Company", back_populates="email_confirmation_status")

class LinkedInMessageStatus(Base):
    __tablename__ = 'linkedin_message_status'

    status_id = Column(
        SmallInteger,
        primary_key=True,
        index=True,
        autoincrement=True
    )

    status_value = Column(
        String(32), 
        unique=True
    )

    companies = relationship("Company", back_populates="linkedin_message_status")

class Representative(Base):
    __tablename__ = 'representatives'

    rep_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True,         
    )

    rep_name = Column(
        String(255), nullable=False
    )

    rep_email = Column(
        String(255), nullable=True, default=None
    )

    rep_linkedin_address = Column(
        String(255), default=None, nullable=True,
        index=True
    )

    rep_linkedin_urn = Column(
        String(255), default=None        
    )

    # gender = Column(
    #     String(10), default=None
    # )

    user_description = Column(
        String(255), default=None
    )

    work_history = Column(
        JSONB, default=None
    )

    user_description = Column(
        String(255), default=None
    )

    work_history = Column(
        JSONB, default=None
    )

    profile_image = Column(
        String(255), default=None
    )

    hiring_behavioural_factors = Column(
        JSONB, default=None
    )

    hiring_behavioural_factors = Column(
        JSONB, default=None
    )

    location = Column(
        String(255), default=None
    )

    skills = Column(
        ARRAY(String), default=None
    )

    followers = Column(
        SmallInteger, default=None
    )

    prographics = Column(
        JSONB, default=None
    )

    education = Column(
        JSONB, default=None
    )

    email_personalization = Column(
        JSONB, default=None
    )

    cold_calling_advice = Column(
        JSONB, default=None
    )

    communication_advice = Column(
        JSONB, default=None
    )

    personality_analysis = Column(
        JSONB, default=None
    )

    sales_profile_url = Column(
        String(255), default=None
    )

    primary_contact = Column(
        Boolean, default=False
    )

    analysis_status = Column(
        String, default='NOT_STARTED'
    )

    analysis_confidence = Column(
        JSONB, default=None
    )

    company_id = Column(
        UUID(as_uuid=True),
        ForeignKey('companies.company_id', ondelete="CASCADE"),
        nullable=False,
    )

    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.current_timestamp()
    )    

class HumanticProfile(Base):
    __tablename__ = 'humantic_profiles'

    profile_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True,  
    )

    linkedin_username = Column(
        String(255), nullable=False, index=True, unique=True
    )

    linkedin_address = Column(
        String(255), nullable=False, index=True
    )

    results = Column(
        JSONB, nullable=False
    )

    meta_data = Column(
        JSONB, nullable=False
    )

    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )

    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.current_timestamp()
    )

class Draft(Base):
    __tablename__ = 'drafts'

    draft_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True,  
    )

    draft_type = Column(SmallInteger, nullable=False, default=0)

    company_id = Column(
        UUID(as_uuid=True),
        ForeignKey('companies.company_id', ondelete="CASCADE"),
        index=True,
        nullable=False,
    )

    from_address = Column(
        String, nullable=True, default=None
    )

    from_name = Column(
        String, nullable=True, default=None
    )

    to_address = Column(
        String, nullable=True, default=None ,index=True
    )

    to_name = Column(
        String, nullable=False
    )

    subject = Column(
        String, nullable=False
    )

    body = Column(
        String, nullable=True, default=None        
    )

    body_plain = Column(
        String, nullable=True, default=None
    )

    email_sent = Column(
        Boolean, nullable=False, default=False
    )

    to_linkedin_address = Column(
        String, nullable=True, default=None, index=True
    )

    linkedin_sent = Column(
        Boolean, nullable=False, default=False
    )

    day_to_send = Column(
        SmallInteger, nullable=True, default=None
    )

    created_at = Column(
        TIMESTAMP(timezone=True), 
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        TIMESTAMP(timezone=True), 
        nullable=False,
        server_default=func.now(),
        onupdate=func.current_timestamp()
    )      

class SentEmail(Base):
    __tablename__ = 'sent_emails'
    
    internal_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True,  
    )

    account_id = Column(String(255), nullable=True, default=None)

    unipile_id = Column(
        String(255), default=None, index=True, nullable=True
    )

    draft_type = Column(SmallInteger, nullable=True, default=None)

    subject = Column(
        String, nullable=False
    )

    body = Column(
        String, nullable=True, default=None        
    )

    body_plain = Column(
        String, nullable=True, default=None
    )

    from_address = Column(
        String, nullable=False, index=True
    )

    from_info = Column(
        JSONB, nullable=True, default=None
    )

    to_addresses = Column(
        ARRAY(String), nullable=True, default=None
    )

    to_info = Column(
        JSONB, nullable=True, default=None
    )

    email_opened = Column(
        Boolean, nullable=False ,default=False
    )

    email_replied = Column(
        Boolean, nullable=True, default=False
    )

    email_link_clicked = Column(
        Boolean, nullable=False, default=False
    )

    message_id = Column(
        String(255), nullable=True, default=None
    )

    tracking_id = Column(
        String(255), default=None, index=True
    )

    reply_to_message_id = Column(
        String(255), nullable=True, default=None
    )

    reply_to_email_id = Column(
        String(255), nullable=True, default=None        
    )

    company_id = Column(
        UUID(as_uuid=True),
        ForeignKey('companies.company_id', ondelete="CASCADE"),
        index=True,
        nullable=False,
    )

    created_at = Column(
        TIMESTAMP(timezone=True), 
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        TIMESTAMP(timezone=True), 
        nullable=False,
        server_default=func.now(),
        onupdate=func.current_timestamp()
    )      

class ReceivedEmail(Base):
    __tablename__ = 'received_emails'

    internal_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True,  
    )

    unipile_id = Column(
        String(255), nullable=False, index=True
    )

    account_id = Column(String(255), nullable=True, default=None)

    sent_email_internal_id = Column(
        UUID(as_uuid=True),
        ForeignKey('sent_emails.internal_id', ondelete="CASCADE"),
        index=True,
        nullable=False,
    )

    sent_email_unipile_id = Column(
        String(255),
        index=True,
        default=None
    ) 

    subject = Column(
        String, nullable=False
    )

    body = Column(
        String, default=None        
    )

    body_plain = Column(
        String, default=None
    )

    from_address = Column(
        String, nullable=False, index=True
    )

    from_info = Column(
        JSONB, default=None
    )

    to_addresses = Column(
        ARRAY(String), nullable=True, default=None
    )

    to_info = Column(
        JSONB, default=None
    )

    message_id = Column(
        String(255), default=None
    )

    tracking_id = Column(
        String(255), default=None, index=True
    )

    reply_to_message_id = Column(
        String(255), default=None
    )

    reply_to_email_id = Column(
        String(255), default=None        
    )   

    company_id = Column(
        UUID(as_uuid=True),
        index=True,
        nullable=True,
        default=None
    )

    sentiment = Column(String, nullable=True, default=None)

    emotions = Column(ARRAY(String), nullable=True, default=None)

    key_takeaways = Column(String, nullable=True, default=None)

    suggested_response = Column(JSONB, nullable=True, default=None)

    explanation = Column(String, nullable=True, default=None)

    created_at = Column(
        TIMESTAMP(timezone=True), 
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        TIMESTAMP(timezone=True), 
        nullable=False,
        server_default=func.now(),
        onupdate=func.current_timestamp()
    )        

class SenderEmail(Base):
    __tablename__ = 'sender_emails'

    sender_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True
    )

    sender_address = Column(
        String, nullable=False, index=True
    )

    sender_first_name = Column(
        String, nullable=False
    )

    sender_last_name = Column(
        String, nullable=False
    )    

    imap_host = Column(
        String, nullable=False
    )

    imap_port = Column(
        SmallInteger, nullable=False
    )

    imap_username = Column(
        String, nullable=False
    )

    imap_password = Column(
        String, nullable=False
    )

    smtp_host = Column(
        String, nullable=False
    )

    smtp_port = Column(
        SmallInteger, nullable=False
    )

    smtp_username = Column(
        String, nullable=False
    )

    smtp_password = Column(
        String, nullable=False
    )

    is_active = Column(Boolean, default=True)

    unipile_account_id = Column(
        String(255), nullable=False, index=True
    )

    remaining_emails = Column(
        SmallInteger, nullable=False, default=25
    )

    created_at = Column(
        TIMESTAMP(timezone=True), 
        nullable=False,
        server_default=func.now()
    )

    updated_at = Column(
        TIMESTAMP(timezone=True), 
        nullable=False,
        server_default=func.now(),
        onupdate=func.current_timestamp()
    )

    __table_args__ = (
        CheckConstraint("remaining_emails >= 0", name="check_remaining_emails_not_negative"),
    )


class LinkedInConnection(Base):
    __tablename__ = 'linkedin_connections'

    provider_id = Column(
        String(255), 
        primary_key=True,
        nullable=False, 
        index=True
    )
    
    connected_provider_id = Column(
        String(255), 
        primary_key=True,
        nullable=False, 
        index=True
    )

    created_at = Column(
        TIMESTAMP(timezone=True), 
        nullable=False,
        server_default=func.now()
    )

    updated_at = Column(
        TIMESTAMP(timezone=True), 
        nullable=False,
        server_default=func.now(),
        onupdate=func.current_timestamp()
    )


class SentMessage(Base): 
    __tablename__ = 'sent_messages'
    internal_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True
    )

    message_content = Column(
        String, nullable=False
    )

    draft_type = Column(SmallInteger, nullable=True, default=None)

    unipile_id = Column(
        String(255), nullable=True, default=None
    )

    company_id = Column(
        UUID(as_uuid=True),
        ForeignKey('companies.company_id', ondelete="CASCADE"),
        index=True,
        nullable=False,
    )

    chat_id = Column(
        String(255), nullable=True, default=None
    )

    account_id = Column(
        String(255), nullable=False
    )

    sender = Column(
        JSONB, nullable=True, default=None
    )

    sender_username = Column(
        String(255), nullable=True, default=None
    )

    sender_provider_id = Column(
        String(255), nullable=True, default=None
    )

    recipient = Column(
        JSONB, nullable=True, default=None
    )

    recipient_username = Column(
        String(255), nullable=True, default=None
    )

    recipient_provider_id = Column(
        String(255), nullable=True, default=None
    )

    attendees = Column(
        JSONB, nullable=True, default=None
    )

    message_read = Column(
        Boolean, nullable=False, default=False
    )

    timestamp = Column(
        String(255), nullable=True, default=None
    )

    created_at = Column(
        TIMESTAMP(timezone=True), 
        nullable=False,
        server_default=func.now()
    )

    updated_at = Column(
        TIMESTAMP(timezone=True), 
        nullable=False,
        server_default=func.now(),
        onupdate=func.current_timestamp()
    )

class ReceivedMessage(Base): 
    __tablename__ = 'received_messages'
    internal_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True
    )

    unipile_id = Column(
        String(255), nullable=True, default=None
    )

    company_id = Column(
        UUID(as_uuid=True),
        ForeignKey('companies.company_id', ondelete="CASCADE"),
        index=True,
        nullable=False,
    )

    message_content = Column(
        String, nullable=False
    )

    chat_id = Column(
        String(255), nullable=True, default=None
    )

    account_id = Column(
        String(255), nullable=False
    )
    
    sender = Column(
        JSONB, nullable=True, default=None
    )

    sender_username = Column(
        String(255), nullable=True, default=None
    )

    sender_provider_id = Column(
        String(255), nullable=True, default=None
    )

    recipient = Column(
        JSONB, nullable=True, default=None
    )

    recipient_username = Column(
        String(255), nullable=True, default=None
    )

    recipient_provider_id = Column(
        String(255), nullable=True, default=None
    )

    attendees = Column(
        JSONB, nullable=True, default=None
    )

    message_read = Column(
        Boolean, nullable=False, default=False
    )

    suggested_response = Column(JSONB, nullable=True, default=None)

    sentiment = Column(String, nullable=True, default=None)

    emotions = Column(ARRAY(String), nullable=True, default=None)

    explanation = Column(String, nullable=True, default=None)

    key_takeaways = Column(String, nullable=True, default=None)

    timestamp = Column(
        String(255), nullable=True, default=None
    )

    created_at = Column(
        TIMESTAMP(timezone=True), 
        nullable=False,
        server_default=func.now()
    )

    updated_at = Column(
        TIMESTAMP(timezone=True), 
        nullable=False,
        server_default=func.now(),
        onupdate=func.current_timestamp()
    )