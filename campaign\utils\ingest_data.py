import pandas as pd
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk, BulkIndexError
from pathlib import Path
from core.config import settings
# Connect to Elasticsearch

DATA_PATH = str(Path(__file__).parent.parent) + "/database/system_data/USA_1.csv"

def ingest_data() -> None:
    #local development
    # es = Elasticsearch(f'http://{settings.ES_HOST}:9200', basic_auth=(settings.ES_USERNAME, settings.ES_PASSWORD))
    #uat
    es = Elasticsearch(f'https://{settings.ES_HOST}:9200', basic_auth=(settings.ES_USERNAME, settings.ES_PASSWORD), verify_certs=False)
    df = pd.read_csv(DATA_PATH, low_memory=False)

    # Create an Elasticsearch index if it doesn't exist
    if not es.indices.exists(index=settings.ES_INDEX_NAME):
        es.indices.create(index=settings.ES_INDEX_NAME)

    # Prepare the data for bulk indexing
    def doc_generator(df):
        for index, document in df.iterrows():
            yield {
                "_index": settings.ES_INDEX_NAME,
                "_id": str(index),  # Optionally specify a unique ID
                "_source": document.to_dict(),
            }

    # Index the data with error handling
    try:
        bulk(es, doc_generator(df), raise_on_error=False)
        print(f"INDEXED {len(df)} DOCUMENTS INTO '{settings.ES_INDEX_NAME}'")
    except BulkIndexError as e:
        # Log the errors and continue
        print(f"Some documents failed to index: {len(e.errors)} errors")
        for error in e.errors:
            print(error)
    finally:
        es.close()
