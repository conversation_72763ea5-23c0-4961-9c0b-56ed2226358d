from __future__ import annotations

import uuid
from enum import Enum
from typing import Union
from typing import Optional
from pydantic import BaseModel,constr, EmailStr
from dataclasses import dataclass


class ConfirmationStatus(str, Enum):
    CONFIRMED  = 1
    EMAIL_SENT = 2
    OVERDUE = 3


# Use to sign JWT
@dataclass
class SignUser:
    user_id: uuid.UUID
    role: int


# Account
class AccountBase(BaseModel):
    user_id: uuid.UUID


class AccountCreate(AccountBase):
    first_name: str
    last_name: str
    gender: Optional[str] = None
    avatar: Optional[str] = None
    role_id: Union[int, None] = 1
    is_active: bool = True

    class Config:
        orm_mode = True

class UserUpdate(BaseModel):
    user_type: Optional[str] = None
    nick_name: Optional[str] = None
    company_type: Optional[str] = None
    billing_plan: Optional[str] = None
    linkedin_address: Optional[str] = None    
    class Config:
        orm_mode = True

class LinkedInConnect(BaseModel):
    unipile_account_id: Optional[str] = None
    
class UnipileCallback(BaseModel):
    status: str
    account_id: str
    name: str

class UnipileStatusUpdate(BaseModel):
    AccountStatus: dict

class UnipileCreateAuthUrl(BaseModel):
    success_redirect_url: str
    failure_redirect_url: str

class AdminAccountUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    gender: Optional[str] = None
    avatar: Optional[str] = None
    is_active: Optional[bool] = None

    class Config:
        orm_mode = True


class UserResponse(BaseModel):
    email: EmailStr
    user_info: Optional[dict] = None
    user_linkedin_info: Optional[dict] = None
    unipile_linkedin_id: Optional[str] = None
    linkedin_connection_status: Optional[str] = None

    class Config:
        orm_mode = True


# Login Data
class LoginDataBase(BaseModel):
    email: EmailStr


class LoginDataCreate(LoginDataBase):
    password: str
    
    class Config:
        orm_mode = True


class LoginDataUpdate(LoginDataBase):
    usr_id: uuid.UUID
    email: Optional[EmailStr] = None
    password: Optional[str] = None
    verified: Optional[bool] = None
    
    class Config:
        orm_mode = True

# Case practices: Do not return password
class LoginDataResponse(BaseModel):
    status: str
    message: str

    class Config:
        orm_mode = True


class UserPasswordReset(BaseModel):
    user_id: str
    password: str

    class Config:
        orm_mode = True

class Login(BaseModel):
    email: EmailStr
    password: constr(min_length=8)

    class Config:
        orm_mode = True

class UserPasswordUpdate(BaseModel):
    new_password: str
    confirm_password: str
    
    class Config:
        orm_mode = True