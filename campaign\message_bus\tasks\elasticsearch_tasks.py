import asyncio
import pandas as pd
import numpy as np
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk, BulkIndexError
from pathlib import Path
from core.config import settings
from loguru import logger
import json
from io import String<PERSON>
from message_bus.rpc_client import RPC<PERSON>lient
# Connect to Elasticsearch

async def ingest_data(message):
    await asyncio.sleep(1)

    try:
        #local development
        # es = Elasticsearch(f'http://{settings.ES_HOST}:9200', basic_auth=(settings.ES_USERNAME, settings.ES_PASSWORD), request_timeout=30, retry_on_timeout=True, max_retries=5)
        #uat
        es = Elasticsearch(f'https://{settings.ES_HOST}:9200', basic_auth=(settings.ES_USERNAME, settings.ES_PASSWORD), verify_certs=False, request_timeout=30, retry_on_timeout=True, max_retries=5)
        try:        
            json_string = message.body.decode()
            message_body = json.loads(json_string)        

            df_json_str = message_body["df_json_str"]
            embedding_dim = 384
            df = pd.read_json(StringIO(df_json_str))

            properties = {}
            for column in df.columns:
                if column != "Company Linkedin Url":
                    properties[column] = {"type": "text"}
                else:
                    properties[column] = {"type": "keyword"}
            embedding_properties = {
                "Company Industry embedding": {"type": "dense_vector", "dims": embedding_dim},
                "Company Location Name embedding": {"type": "dense_vector", "dims": embedding_dim},
                "Company Location Continent embedding": {"type": "dense_vector", "dims": embedding_dim},
            }      
            properties.update(embedding_properties)

            company_industry_list = df["Company Industry"].unique().tolist()
            company_location_name_list = df["Company Location Name"].unique().tolist()
            company_location_continent_list = df["Company Location Continent"].unique().tolist()

            #call rpc function embed_company_info
            rpc = await RPCClient().connect()
            function_name = "embed_company_info"
            company_info = {
                "company_industry_list": company_industry_list,
                "company_location_name_list": company_location_name_list,
                "company_location_continent_list": company_location_continent_list              
            }
            properties.update(embedding_properties)

            params = {
                "company_info": company_info
            }
            response = await rpc.call(service="aiengine.*", function_name=function_name, params=params)
            response_body = json.loads(response)
            result = response_body.get("result")
            if result == "error":
                raise Exception(
                    "Error from RPC server: " + response_body.get("detail")
                )

            df["Company Industry embedding"] = df["Company Industry"].map(result["company_industry_list_embedding"])
            df["Company Location Name embedding"] = df["Company Location Name"].map(result["company_location_name_list_embedding"])            
            df["Company Location Continent embedding"] = df["Company Location Continent"].map(result["company_location_continent_list_embedding"])         
            
            index_settings = {
                "settings": {
                    "number_of_shards": 4,
                    "number_of_replicas": 0
                },
                "mappings": {
                    "properties": properties
                }
            }        
            if not es.indices.exists(index=settings.ES_INDEX_NAME):
                es.indices.create(index=settings.ES_INDEX_NAME, body=index_settings)

            df = df.fillna('')

            unique_field = "Company Linkedin Url"

            # Prepare the data for bulk indexing
            def doc_generator(df):
                for index, document in df.iterrows():
                    unique_field_value = document.get(unique_field)
                    yield {
                        "_index": settings.ES_INDEX_NAME,
                         "_id": unique_field_value,
                        "_source": document.to_dict(),
                    }

                # Read CSV using pandas
                
            df_filled = df.fillna('')
            success_count, failed_count = bulk(es, doc_generator(df_filled), 
                                               raise_on_error=False, 
                                               stats_only=True, 
                                               chunk_size=100)
            total = success_count + failed_count
            logger.info(f"[CONSUMER] indexed {success_count}/{total} documents into '{settings.ES_INDEX_NAME}'")

            
        except Exception as e:
            raise e
        finally:
            es.close()

    except Exception as e:
        logger.error("[CONSUMER] failed to process ingest_data task: " + str(e))


# async def ingest_data_test(message):
#     await asyncio.sleep(1)

#     TEST_INDEX = "test-index"

#     try:
#         #local development
#         # es = Elasticsearch(f'http://{settings.ES_HOST}:9200', basic_auth=(settings.ES_USERNAME, settings.ES_PASSWORD), request_timeout=30, retry_on_timeout=True, max_retries=5)
#         #uat
#         es = Elasticsearch(f'https://{settings.ES_HOST}:9200', basic_auth=(settings.ES_USERNAME, settings.ES_PASSWORD), verify_certs=False, request_timeout=30, retry_on_timeout=True, max_retries=5)
#         try:        
#             json_string = message.body.decode()
#             message_body = json.loads(json_string)        

#             df_json_str = message_body["df_json_str"]
#             embedding_dim = message_body["embedding_dim"]
#             df = pd.read_json(StringIO(df_json_str))

#             # logger.info(df.shape)


#             properties = {}
#             for column in df.columns:
#                 if column != "Company Linkedin Url":
#                     properties[column] = {"type": "text"}
#                 else:
#                     properties[column] = {"type": "keyword"}
#             # embedding_dim = 768    
#             embedding_properties = {
#                 "Company Industry embedding": {"type": "dense_vector", "dims": embedding_dim},
#                 "Company Location Name embedding": {"type": "dense_vector", "dims": embedding_dim},
#                 "Company Location Continent embedding": {"type": "dense_vector", "dims": embedding_dim},
#             }      
#             properties.update(embedding_properties)

#             company_industry_list = df["Company Industry"].unique().tolist()
#             company_location_name_list = df["Company Location Name"].unique().tolist()
#             company_location_continent_list = df["Company Location Continent"].unique().tolist()

#             #call rpc function embed_company_info
#             rpc = await RPCClient().connect()
#             function_name = "embed_company_info_test"
#             company_info = {
#                 "company_industry_list": company_industry_list,
#                 "company_location_name_list": company_location_name_list,
#                 "company_location_continent_list": company_location_continent_list              
#             }
#             properties.update(embedding_properties)

#             params = {
#                 "company_info": company_info,
#                 "embedding_dim": embedding_dim
#             }
#             response = await rpc.call(service="aiengine.*", function_name=function_name, params=params)
#             response_body = json.loads(response)
#             result = response_body.get("result")
#             if result == "error":
#                 raise Exception(
#                     "Error from RPC server: " + response_body.get("detail")
#                 )

#             df["Company Industry embedding"] = df["Company Industry"].map(result["company_industry_list_embedding"])
#             df["Company Location Name embedding"] = df["Company Location Name"].map(result["company_location_name_list_embedding"])            
#             df["Company Location Continent embedding"] = df["Company Location Continent"].map(result["company_location_continent_list_embedding"])         
            
#             index_settings = {
#                 "settings": {
#                     "number_of_shards": 6,
#                     "number_of_replicas": 1
#                 },
#                 "mappings": {
#                     "properties": properties
#                 }
#             }        
#             if not es.indices.exists(index=TEST_INDEX):
#                 es.indices.create(index=TEST_INDEX, body=index_settings)

#             df = df.fillna('')

#             unique_field = "Company Linkedin Url"

#             # Prepare the data for bulk indexing
#             def doc_generator(df):
#                 for index, document in df.iterrows():
#                     unique_field_value = document.get(unique_field)
#                     yield {
#                         "_index": TEST_INDEX,
#                          "_id": unique_field_value,
#                         "_source": document.to_dict(),
#                     }

#                 # Read CSV using pandas
                
#             df_filled = df.fillna('')
#             success_count, failed_count = bulk(es, doc_generator(df_filled), 
#                                                raise_on_error=False, 
#                                                stats_only=True, 
#                                                chunk_size=100)
#             total = success_count + failed_count
#             logger.info(f"[CONSUMER] indexed {success_count}/{total} documents into '{settings.ES_INDEX_NAME}'")

            
#         except Exception as e:
#             raise e
#         finally:
#             es.close()

#     except Exception as e:
#         logger.error("[CONSUMER] failed to process ingest_data task: " + str(e))                