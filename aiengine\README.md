<h1 style="font-size: 36px; font-family: <PERSON><PERSON>, sans-serif; text-align: center;">
  ROBOMAN - AI Engine Service
</h1>

Welcome to the *AI engine* documentation of *ROBOMAN* project. This document provides details on code structures and API references just for AI engine

# Table of Contents

1. [Introduction](#introduction)

    - 1.1. [Overview](#overview)

    - 1.2. [Goals](#goals)

2. [Features](#features)

    - 2.1. [Chat Assistant](#chat-assistant)


3. [Code Structure](#code-structure)

4. [API Reference](#api-reference)

5. [Contribution](#contribution)

# Introduction

## Overview

This service is built based on *Llama-Index*, a powerful tool designed to facilitate efficient data indexing and retrieval. By leveraging the capabilities of Llama-Index, our service ensures fast and accurate access to information, making it an essential component for data-driven applications.

For more detailed information about *Llama-Index* and its features, you can explore the *Llama-Index* documentation here.

## Goals

The primary goal is to build a comprehensive service that encapsulates all AI-related logic, providing a seamless and user-friendly interface for integration. This service aims to:

- **Simplify Integration**: Offer an easy-to-use framework that allows for the straightforward integration of any state-of-the-art Deep Learning (DL) model or Large Language Model (LLM), whether accessed via an API or deployed locally.
- **Enhance Flexibility**: Ensure that the service is adaptable to various AI models and can be easily updated or extended to incorporate new advancements in the field.
- **Streamline Operations**: Provide a centralized platform that manages all AI processes, reducing the complexity and overhead associated with integrating multiple AI components.
- **Improve Accessibility**: Make advanced AI capabilities accessible to a broader audience, including developers with varying levels of expertise in AI and machine learning.

By achieving these goals, the service will serve as a robust foundation for developing intelligent applications that leverage cutting-edge AI technologies.

# Features

## Chat Assistant

### Key Features

- **User-Friendly Interface**
- **Step-by-Step Guidance**
- **Customizable Campaigns**
- **Real-Time Support**

### Onboarding Process

1. **Sign Up**: Create an account and verify your email.
2. **Profile Setup**: Enter business details and preferences.
3. **Integration**: Connect your tools and watch a quick tutorial.

### Creating a Marketing Campaign

1. **Set Goals**: Define objectives and target audience.
2. **Design Campaign**: Create content, select visuals, and craft messages.
3. **Execute**: Choose platforms, schedule, and allocate budget.
4. **Monitor**: Track performance and optimize with real-time feedback.

### Support

- **Help Center**: Access articles and FAQs.
- **Live Chat**: Get immediate assistance.
- **Community Forum**: Share insights with other users.

# Code Structure

<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Structure</title>
    <style>
        pre {
            font-family: monospace;
        }
    </style>
</head>
<body>
    <pre>
.
│   .DS_Store
│   .env
│   alembic.ini
│   create_db.py
│   Dockerfile
│   main.py
│   README.md
│   requirements.txt
│
├───agent
│   │   __init__.py
│   │
│   └───tools
│           campaign_create.py
│           email_postprocess.py
│           __init__.py
│
├───core
│       config.py
│       __init__.py
│
├───crud
│       crud_base.py
│       __init__.py
│
├───database
│       database.py
│       models.py
│       __init__.py
│
├───llama_config
│       aiservice.py
│       __init__.py
│
├───migrations
│   │   env.py
│   │   README
│   │   script.py.mako
│   │
│   └───versions
│           __init__.py
│
├───routes
│   └───v1
│           llm.py
│           nlp_engine.py
│
├───schema
│       ai_config_schema.py
│       input_message_schema.py
│       system_prompt_schema.py
│
└───utils
    │   amazon_storage.py
    │   history.py
    │   migrate.py
    │   __init__.py
    │
    └───tools
            humantic.py
            streaming.py
            stt.py
            summary.py
            __init__.py
    </pre>
</body>
</html>

# Contribution

Kindly refer to the contribution section in the overview documentation [here](../README.md#contribution).

