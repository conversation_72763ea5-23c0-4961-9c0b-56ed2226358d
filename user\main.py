from message_bus.user_info_server import user_rpc_server
from utils.migrate import run_migration
from routes.v2.system.user import user_routes
from routes.v2.system.user_credits import user_credits_routes
from routes.v2.system.stripe_payment import stripe_payment_routes
from routes.v2.system.stripe_products import stripe_products_routes
from routes.v2.auth.auth import auth_routes
from core.config import settings
from message_bus.rpc_server import start_rpc_server

from fastapi import FastAPI
import uvicorn
import asyncio
from contextlib import asynccontextmanager


@asynccontextmanager
async def lifespan(app: FastAPI):
    print("Starting task consumers...")
    rpc_server = asyncio.create_task(start_rpc_server())
    yield
    rpc_server.cancel()

    try:
        await rpc_server
    except asyncio.CancelledError:
        pass

app = FastAPI(lifespan=lifespan)

# Add routes
app.include_router(auth_routes)
app.include_router(user_routes)
app.include_router(user_credits_routes)
app.include_router(stripe_payment_routes)
app.include_router(stripe_products_routes)


@app.get('/api/health')
async def health_check():
    return {'status': 'ok'}


# @app.on_event("startup")
# async def startup_event():
#     asyncio.create_task(user_rpc_server())


if __name__ == "__main__":
    port = int(settings.SERVER_PORT)
    app_module = "main:app"
    run_migration()
    uvicorn.run(app_module, host="0.0.0.0", port=port, reload=True)

#nothing, just a comment
