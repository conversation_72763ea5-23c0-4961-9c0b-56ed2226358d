from pydantic import BaseModel, <PERSON>
from typing import List, Optional
import uuid


class GenerateDrafts(BaseModel):
    from_name: str
    to_name: str
    core_service: str
    key_benefits: list[str]
    problem_solved: str
    must_have_info: Optional[str]
    output_format: Optional[str]
    personality_analysis: Optional[dict]
    communication_advice: Optional[dict]
    email_personalization: Optional[dict]
    industry: str
    company_name: str
    prompt_name: Optional[str]

    class Config:
        orm_mode = True

class EmailDraft(BaseModel):
    """
    Represents a single email draft with a subject and HTML-formatted body.

    Attributes:
        subject (str): The subject line of the email.
        body (str): The body content of the email, formatted in valid HTML.
    """
    subject: str = Field(..., description="Subject of the email")
    body: str = Field(..., description="Body of the email in HTML format")

    class Config:
        orm_mode = True


class EmailList(BaseModel):
    """
    Represents a collection of email drafts.

    Attributes:
        email_list (List[EmailDraft]): A list containing one or more email drafts.
    """
    email_list: List[EmailDraft] = Field(..., description="List of emails")

    class Config:
        orm_mode = True