import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import whiteLogo from '../assets/img/roboman-logo.png';
import profileImage from '../assets/profile.png';
import Swal from 'sweetalert2';
import { FaCoins } from 'react-icons/fa';


export const newCampaign = (navigate) => {


  const keysToKeep = [
    "access_token",
    "isLoggedIn",
    "havedata",
    "nickname",
    "linkedinUrl",
    "email",
    "userType",
    "lnk_act",
    "page3_audio_data"

  ]; // Specify the keys you want to keep

  // Collect all keys into an array
  const allKeys = Object.keys(localStorage);

  // Iterate over all keys
  allKeys.forEach((key) => {
    // If the key is not in the keysToKeep array, remove it
    if (!keysToKeep.includes(key)) {
      localStorage.removeItem(key);
    }
  });

  // Explicitly remove specific items if needed
  localStorage.removeItem("conversation_id");
  const haveData = JSON.parse(localStorage.getItem('havedata'));
  const haveLinkedIN = JSON.parse(localStorage.getItem('lnk_act'));
  if (haveData || haveLinkedIN) {
    navigate('/campinitselect');
  } else {
    navigate('/onboarding-input');
  }
};

const HeaderMainPage = () => {
  const navigate = useNavigate();
  const [showCampaignMenu, setShowCampaignMenu] = useState(false);
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const [showUserAccountPopup, setShowUserAccountPopup] = useState(false);
  const campaignMenuRef = useRef(null);
  const profileMenuRef = useRef(null);
  const roboman_api = process.env.REACT_APP_ROBOMAN_API;
  // const [linkedinPassword, setLinkedinPassword] = useState('password');
  // const [showPassword, setShowPassword] = useState(false);
  // const [loginEmail, setLoginEmail] = useState('')

  const commonButtonStyle = "bg-transparent py-2 px-4 text-white cursor-pointer text-lg font-semibold hover:bg-blue-700 transition-colors duration-200 rounded-full";

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (campaignMenuRef.current && !campaignMenuRef.current.contains(event.target)) {
        setShowCampaignMenu(false);
      }
      if (profileMenuRef.current && !profileMenuRef.current.contains(event.target)) {
        setShowProfileMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    localStorage.clear();
    navigate('/login');
  };

  const Dashboard = () => {
    navigate('/dashboard');
    // Implementation to be added later
  };

  const myCampaign = () => {
    localStorage.removeItem("campaign_id");
    navigate('/my-campaign');

  };

  const toggleCampaignMenu = () => {
    setShowCampaignMenu(!showCampaignMenu);
    setShowProfileMenu(false);
  };

  const toggleProfileMenu = () => {
    setShowProfileMenu(!showProfileMenu);
    setShowCampaignMenu(false);
  };

  const [isNavOpen, setIsNavOpen] = useState(false);
  const navRef = useRef(null);
  const toggleNav = () => {
    setIsNavOpen(!isNavOpen);
  };

  const handleClickOutside = (event) => {
    if (navRef.current && !navRef.current.contains(event.target)) {
      setIsNavOpen(false);
    }
  };

  useEffect(() => {
    if (isNavOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isNavOpen]);

  // User Account Popup Component
  const UserAccountPopup = ({ onClose }) => {

    const roboman_api = process.env.REACT_APP_ROBOMAN_API;
    const access_token = localStorage.getItem("access_token")
    const [getData, setGetData] = useState(false)
    // Predefined options for dropdowns
    const USER_TYPES = [
      'Business',
      'Individual'
    ];

    const COMPANY_TYPES = [
      'Partnership',
      'Limited Liability Partnership (LLP)',
      'Private Limited Company (Ltd)',
      'Public Limited Company (PLC)',
      'Umbrella Company'
    ];

    // State to manage form fields
    const [userData, setUserData] = useState({
      nickname: '',
      email: '',
      userType: '',
      linkedinUrl: '',
      companyType: '',
      calendly: '',
    });

    const fetchUserDataFromAPI = async (access_token) => {
      try {
        const response = await fetch(`${roboman_api}/users/me`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: access_token,
          },
        });

        if (!response.ok) {
          Swal.fire({
            icon: "error",
            title: "Oops...",
            text: "This account does not have any information. Let's create new campaign and start to fill in your information.",
            confirmButtonText: "Close",
          }).then(() => {
            onClose();
          });
          return null;
        }
        const data = await response.json();
        setUserData({
          nickname: data.user_info.nick_name,
          email: data.email,
          userType: data.user_info.user_type,
          linkedinUrl: data.user_info.linkedin_address,
          companyType: data.user_info.company_type,
          lnk_info: data.user_linkedin_info,
          unipile_id: data.unipile_linkedin_id,
          linkedin_connection_status: data.linkedin_connection_status
        });
        return data;
      } catch (error) {
        console.error('Error fetching user data:', error);
        Swal.fire({
          icon: "error",
          title: "Oops...",
          text: "The information of user cannot be retrieved. Please try again.",
          confirmButtonText: "Close",
        }).then(() => {
          onClose();
        });
        return null;
      }
    };

    useEffect(() => {
      if (getData === false) {
        fetchUserDataFromAPI(access_token);
        setGetData(true)
      }

    }, []);

    // Handle input changes
    const handleInputChange = (field, value) => {
      setUserData(prev => ({
        ...prev,
        [field]: value
      }));
    };

    // Handle form submission
    const handleSubmit = async () => {
      try {
        // Prepare request payload
        const payload = {
          nick_name: userData.nickname,
          user_type: userData.userType,
          billing_plan: "None",
          company_type: userData.companyType,
          linkedin_address: userData.linkedinUrl
        };

        // API call
        const response = await fetch(`${roboman_api}/users/me/update-info`, {
          method: 'PUT',
          headers: {
            'accept': 'application/json',
            'Authorization': localStorage.getItem('access_token'),
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(payload)
        });

        const result = await response.json();

        if (result.status === 'success') {
          // Show success alert
          await Swal.fire({
            icon: 'success',
            title: 'Update Successful!',
            text: 'Your account information has been updated.',
            confirmButtonColor: '#3085d6',
            confirmButtonText: 'OK'
          });

          // Update local storage
          Object.entries(userData).forEach(([key, value]) => {
            localStorage.setItem(key, value);
          });

          // Close popup
          onClose();
        } else {
          // Show error alert
          await Swal.fire({
            icon: 'error',
            title: 'Update Failed',
            text: result.message || 'There was an issue updating your account. Please try again.',
            confirmButtonColor: '#d33',
            confirmButtonText: 'Try Again'
          });
          onClose();
        }
      } catch (error) {
        // Show error alert for network or unexpected errors
        await Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'An unexpected error occurred. Please try again later.',
          confirmButtonColor: '#d33',
          confirmButtonText: 'OK'
        });
        console.error('Update error:', error);
      }
    };

    const authorizeLinkedInConnection = async (linkedinConnectionStatus) => {
      const access_token = localStorage.getItem('access_token');
      const headers = {
        'accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': access_token, // Replace with the actual token
      };

      try {
        let endpoint = `${roboman_api}/users/unipile/linkedin/auth-url`; // Default endpoint

        if (linkedinConnectionStatus === "DISCONNECTED") {
          endpoint = `${roboman_api}/users/unipile/linkedin/reconnect-auth-url`; // Reconnect endpoint
          console.log("Reconnecting LinkedIn connection..."); // Debugging log
        } else {
          console.log("Connecting LinkedIn for the first time..."); // Debugging log
        }

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: headers,
          body: JSON.stringify({
            success_redirect_url: "https://outreach.roboman.ai/dashboard",
            failure_redirect_url: "https://outreach.roboman.ai/dashboard",
          }),
        });

        if (response.ok) {
          const data = await response.json();
          if (data.object === 'HostedAuthUrl' && data.url) {
            // Redirect to the URL provided in the response
            window.location.href = data.url;
          } else {
            // Navigate to "/dashboard" if response does not contain the expected data
            // console.log("Unexpected response format:", data);
            navigate('/dashboard');
            onClose();
          }
        } else {
          // Handle non-OK responses
          navigate('/dashboard');
          onClose();
        }
      } catch (error) {
        console.error('Error during authorization:', error);
        navigate('/dashboard'); // Navigate to fallback page in case of error
      }
    };

    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-md p-6 text-black">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold text-black">User Account Information</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              ✖
            </button>
          </div>

          <div className="space-y-4">
            {/* User Name */}
            <div>
              <label className="block text-sm font-medium text-black">User Name</label>
              <input
                type="text"
                value={userData.nickname}
                onChange={(e) => handleInputChange('nickname', e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-black"
              />
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-black">Email</label>
              <input
                type="email"
                value={userData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-black"
                disabled
              />
            </div>

            {/* User Type Dropdown */}
            <div>
              <label className="block text-sm font-medium text-black">User Type</label>
              <select
                value={userData.userType}
                onChange={(e) => handleInputChange('userType', e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-black"
              >
                {/* <option value="">Select User Type</option> */}
                {USER_TYPES.map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            {/* Calendly */}
            <div>
              <label className="block text-sm font-medium text-black">Calendly</label>
              <input
                type="text"
                value={userData.calendly}
                onChange={(e) => handleInputChange('calendly', e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-black"
              />
            </div>

            {/* LinkedIn */}
            <div>
              <label className="block text-sm font-medium text-black">LinkedIn</label>
              <input
                type="text"
                value={userData.linkedinUrl}
                onChange={(e) => handleInputChange('linkedinUrl', e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-black"
              />
            </div>

            {/* Company Type Dropdown */}
            <div>
              <label className="block text-sm font-medium text-black">Company Type</label>
              <select
                value={userData.companyType}
                onChange={(e) => handleInputChange('companyType', e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-black"
              >
                {/* <option value="">Select Company Type</option> */}
                {COMPANY_TYPES.map((type) => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>

            {/* LinkedIn Connection Status */}
            <div className="mt-4">
              <label className="block text-sm font-medium text-black">LinkedIn Connection Status</label>
              <div className="flex items-center justify-between mt-1 space-x-4">
                {/* Connection Status */}
                <div
                  className={`p-2 rounded-md font-medium text-center ${userData.linkedin_connection_status === "CONNECTED"
                    ? "bg-green-100 text-green-800 border border-green-300"
                    : "bg-red-100 text-red-800 border border-red-300"
                    }`}
                  style={{ flex: "1 1 0", height: "40px" }} // Ensure equal size
                >
                  {userData.linkedin_connection_status === "CONNECTED"
                    ? "Connected"
                    : userData.linkedin_connection_status === "DISCONNECTED"
                      ? "Disconnected"
                      : "Not Connected"}
                </div>

                {/* Connect Button */}
                <button
                  onClick={() => authorizeLinkedInConnection(userData.linkedin_connection_status)} // Replace with your actual connection logic
                  disabled={userData.linkedin_connection_status === "CONNECTED"} // Disable if status is "CONNECTED"
                  className={`rounded-md font-medium ${userData.linkedin_connection_status === "CONNECTED"
                    ? "bg-gray-400 text-gray-600 cursor-not-allowed" // Gray color for disabled state
                    : "bg-blue-500 text-white hover:bg-blue-600" // Blue color for enabled state
                    }`}
                  style={{ flex: "1 1 0", height: "40px" }} // Ensure equal size
                >
                  {userData.linkedin_connection_status === "DISCONNECTED"
                    ? "Reconnect"
                    : (userData.linkedin_connection_status === "CONNECTED")
                      ? "Connected"
                      : "Connect"}
                </button>
              </div>
            </div>
          </div>



          <div className="flex justify-end space-x-2 mt-6">
            <button
              onClick={handleSubmit}
              className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
            >
              Save Changes
            </button>

            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-black hover:bg-gray-50"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    );
  };

  // Open user account popup
  const openUserAccountPopup = () => {
    setShowUserAccountPopup(true);
    setShowProfileMenu(false);
  };

  const BillingPage = () => {
    navigate('/billing');
    localStorage.setItem('showbillpage', true);
  }

  const [userCredit, setUserCredit] = useState(null);
  const [showCreditDetails, setShowCreditDetails] = useState(false);
  const fetchUserCredits = async () => {
    try {
      const response = await fetch(`${roboman_api}/users/credits`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': localStorage.getItem('access_token'),
        },
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('Failed to fetch credit');
      } else {
        console.log('Get user credits');
        setUserCredit(data)
      }
    } catch (error) {
      console.error('Network or parsing error:', error);
    }
  };

  // Run fetchUserCredits on component mount
  useEffect(() => {
    fetchUserCredits();
  }, []);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };


  return (
    <header className="w-full h-20 flex justify-between items-center px-4 md:px-12 bg-[#223F9E] text-white">
      <div className="flex-none w-[100px] md:w-[150px] flex justify-start items-center">
        <img src={whiteLogo} alt="White Logo" className="w-[80px] md:w-[130px] h-[40px] md:h-[50px] object-contain" />
      </div>

      <button className="md:hidden text-2xl focus:outline-none" onClick={toggleNav}>
        ☰
      </button>

      <nav className="hidden md:flex flex-1 justify-center items-center space-x-5">
        <button onClick={Dashboard} className={commonButtonStyle}>Dashboard</button>
        <div className="relative" ref={campaignMenuRef}>
          <button onClick={toggleCampaignMenu} className={commonButtonStyle}>
            Campaign <span className="ml-1">▼</span>
          </button>
          {showCampaignMenu && (
            <div className="absolute top-full left-0 bg-white border border-gray-200 rounded-md shadow-lg z-10 mt-1 min-w-[170px]">
              <button onClick={() => newCampaign(navigate)} className="block w-full h-[50px] py-2 px-4 text-left hover:bg-gray-100 text-gray-700 rounded-t-md font-semibold">New Campaign</button>
              <button onClick={myCampaign} className="block w-full h-[50px] py-2 px-4 text-left hover:bg-gray-100 text-gray-700 rounded-b-md font-semibold">List of Campaign</button>
            </div>
          )}
        </div>
        <button onClick={BillingPage} className={commonButtonStyle}>Billing</button>
      </nav>

      {/* Mobile Menu */}
      {isNavOpen && (
        <div ref={navRef} className="absolute top-10 left-0 w-full bg-[#223F9E] text-white md:hidden">
          <div className="flex flex-col items-center space-y-2 py-4">
            <button onClick={Dashboard} className={commonButtonStyle}>Dashboard</button>
            <button onClick={toggleCampaignMenu} className={commonButtonStyle}>
              Campaign <span className="ml-1">▼</span>
            </button>
            {showCampaignMenu && (
              <div className="bg-white border border-gray-200 rounded-md shadow-lg z-10 mt-10 min-w-[170px]">
                <button onClick={() => newCampaign(navigate)} className="block w-full py-2 px-4 text-left hover:bg-gray-100 text-gray-700 font-semibold">New Campaign</button>
                <button onClick={myCampaign} className="block w-full py-2 px-4 text-left hover:bg-gray-100 text-gray-700 font-semibold">List of Campaign</button>
              </div>
            )}
            <button onClick={BillingPage} className={commonButtonStyle}>Billing</button>
          </div>
        </div>
      )}



      <div className="flex-none w-[300px] md:w-[150px] flex justify-end items-center relative" ref={profileMenuRef}>

        {/* Credit Display */}
        <div
          className="flex items-center bg-blue-600 text-white text-sm font-semibold py-1 px-3 rounded-full mr-4 w-[100px] justify-between relative"
          onMouseEnter={() => setShowCreditDetails(true)} // Show tooltip on hover
          onMouseLeave={() => setShowCreditDetails(false)} // Hide tooltip when not hovering
        >
          <span>
            {userCredit ? userCredit.current_credits : "Not Available"}
          </span>
          <FaCoins className="ml-2 text-yellow-400" />

          {/* Tooltip for Credit Details */}
          {showCreditDetails && userCredit && (
            <div className="absolute top-full mt-2 left-1/2 transform -translate-x-1/2 bg-white text-black text-sm rounded-lg shadow-lg p-4 z-10 w-[250px]">
              <div className="grid grid-cols-2 gap-y-2">
                {/* Current Credit */}
                <div className="font-semibold">Current Credit:</div>
                <div className="flex items-center">
                  {userCredit.current_credits}
                  <FaCoins className="ml-1 text-yellow-400" />
                </div>

                {/* Used Credit */}
                <div className="font-semibold">Used Credit:</div>
                <div className="flex items-center">
                  {userCredit.used_credits}
                  <FaCoins className="ml-1 text-yellow-400" />
                </div>

                {/* Bought Credit */}
                <div className="font-semibold">Bought Credit:</div>
                <div className="flex items-center">
                  {userCredit.bought_credits}
                  <FaCoins className="ml-1 text-yellow-400" />
                </div>

                {/* Update Status */}
                <div className="font-semibold">Update Status:</div>
                <div>{formatDate(userCredit.updated_at)}</div>
              </div>
            </div>
          )}

        </div>

        <div className="flex items-center cursor-pointer" onClick={toggleProfileMenu}>
          <img src={profileImage} alt="Profile" className="w-8 md:w-10 h-8 md:h-10 rounded-full object-cover mr-1" />
          <span className="text-xs ml-1">▼</span>
        </div>
        {showProfileMenu && (
          <div className="absolute top-full right-0 bg-white border border-gray-200 rounded-md shadow-lg z-10 mt-1">
            <button className="block w-[150px] md:w-[180px] h-[40px] md:h-[50px] py-2 px-4 text-left hover:bg-gray-100 text-gray-700 rounded-b-md font-semibold" onClick={openUserAccountPopup}>User Account</button>
            <button className="block w-[150px] md:w-[180px] h-[40px] md:h-[50px] py-2 px-4 text-left hover:bg-gray-100 text-gray-700 rounded-b-md font-semibold" onClick={handleLogout}>Logout</button>
          </div>
        )}

        {/* User Account Popup */}
        {showUserAccountPopup && (
          <UserAccountPopup
            onClose={() => setShowUserAccountPopup(false)}
          />
        )}

      </div>
    </header>
  );
};

export default HeaderMainPage;
