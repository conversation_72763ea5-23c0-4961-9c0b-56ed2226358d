from __future__ import annotations

from core.network import streaming, create_request_data
from core.config import settings
from database.models import Conversation, ConversationMessage
from schema.conversation_chatmessage_schema import ConversationResponse, ChatMessageResponse, ChatMessageCreate
from routes.v1.system_error import status
from database.database import get_session
from crud.crud_conversation import crud_conv

import uuid
from typing import List, Union
from fastapi.responses import StreamingResponse
from fastapi import Request
from fastapi import HTTPException, status
from fastapi import APIRouter, Depends, Header
from sqlalchemy.future import select
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession


router = APIRouter(
    prefix = "/api",
    tags=['Chat Management'],
    responses={404: {'description': 'Not found'}},
)


# Conversation
@router.get("/conversation-list", status_code=status.HTTP_200_OK)
async def get_conversation_list_by_user_id(
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
) -> Union[List[ConversationResponse], List]:
    logger.info(f'user_request: {request_user_id}')

    try:
        result = await crud_conv.get_right_access_conversation(db=db, usr_id=request_user_id)
    except Exception as e:
        logger.error(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Something went wrong. Please refresh the page or log out!",
        )

    return result


@router.post("/conversation/new", status_code=status.HTTP_201_CREATED)
async def create_conversation(
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
) -> ConversationResponse:

    try:
        new_conversation = Conversation(
            user_id=request_user_id,
        )
        db.add(new_conversation)
        await db.commit()

        return ConversationResponse(
            conversation_id=new_conversation.conversation_id,
            conversation_name=new_conversation.conversation_name,
            created_at=new_conversation.created_at,
            updated_at=new_conversation.updated_at,
        )
    except Exception as e:
        logger.error(e)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{e}",
        )


@router.get("/conversation/{conversation_id}", status_code=status.HTTP_200_OK)
async def get_history_by_conversation_id(
    conversation_id: uuid.UUID,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
) -> Union[List[ChatMessageResponse], List]:
    logger.info(f'user_request: {request_user_id}')

    conv_messages = await db.execute(
        select(
            ConversationMessage
        ).filter(
            ConversationMessage.conversation_id == conversation_id,
        ).order_by(
            ConversationMessage.created_at.asc()
        )
    )
    conv_messages = conv_messages.scalars().all()
    
    return conv_messages


@router.post("/conversation/message", status_code=status.HTTP_201_CREATED)
async def send_message(
    message_data: ChatMessageCreate,
    request: Request,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    input_data = await create_request_data(
        db=db,
        message_data=message_data,
    )

    # Response
    response = StreamingResponse(
        streaming(
            user_id = request_user_id,
            url = settings.AI_ENGINE_URL,
            method = str(request.method).lower(),
            data = input_data,
        ),
        media_type='text/event-stream',
    )

    return response


###############################################################################
@router.post("/conversation/history", status_code=status.HTTP_200_OK)
async def save_conversation_history(
    message_recieve: dict,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session),
):
    message_data = message_recieve.get('message_data')

    message_to_add = ConversationMessage(
        message_data = message_data,
        conversation_id = message_recieve.get("conversation_id"),
        user_id = message_recieve.get("user_id")
    )
    db.add(message_to_add)
    await db.commit()

    return {'status': 'success'}


chat_routes = router
