from config import settings
from core import route, file_route
from schema.input_data_schema import (
    ModelUpdate,
)
from schema.user_schema import (
    Login, LoginDataCreate,
    UserUpdate, UserPasswordUpdate,
    UnipileCallback,
    UnipileCreateAuthUrl,
    UnipileStatusUpdate
)
from schema.chat_schema import  ChatMessageCreate
from schema.message_schema import Email
from schema.input_data_schema import (TextCampaign, CampaignCreate, 
                                      SendEmailRequest, AudioCreate, 
                                      CompanyEmailUpdate, CompanyAddManual, 
                                      CompanyLinkedInMsgUpdate, CampaignDetails,
                                      CampaignContentUpdate, CampaignSendLinkedInMessage, CampaignSendLinkedInInvitation,
                                      CampaignDetailsUpdate, GenerateEmail,
                                      WebhooksNewEmail, EmailGeneration,
                                      WebhooksEmailTracking, EmailSend,
                                      TestScheduleJob, EmailOutreach,
                                      UpdateDraftEmail, GenerateResponse,
                                      EnablePrompt, RespondEmail, CompanyMatching,
                                      SendLinkedInRequest, WebhooksNewMessage,
                                      HtmlToText, RespondMessage, SimilarityCheck,
                                      CampaignContact)
from schema.campaign_schemas.outreach import StartAutoOutreach
from schema.user_schemas.credit_schemas import UsedCreditCreate
from schema.user_schemas.stripe_payment_schema import CheckoutSessionCreate, CheckoutSessionObject, EventObject
from schema.user_schemas.stripe_product_schema import StripeProductCreate, StripePriceCreate, StripePriceUpdate, StripeProductUpdate

import uuid
import uvicorn
from fastapi.middleware.cors import CORSMiddleware
from fastapi import Security, FastAPI, status, Request, Response, File, UploadFile
from fastapi.security.api_key import APIKeyHeader
# from pyngrok import ngrok
import requests
from loguru import logger

app = FastAPI()
api_key_header = APIKeyHeader(name='Authorization')

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=['*'],
    allow_headers=['*'],
)
"""
This file is used to publish API from services

Route decorator is high-level wrapper of FastAPI routers

You can have many APIs in your code. However, user can only use APIs publish in this file
"""


maintenance_status = False

@app.get('/api/health', tags=['App Management'])
async def health_check():
    return {'status': 'ok'}


@app.post("/api/maintenance", tags=['App Management'])
async def set_state(status: bool):
    global maintenance_status
    maintenance_status = status
    return {"status": "success"}


@app.get("/api/maintenance", tags=['App Management'])
async def get_state():
    return {"maintenance": maintenance_status}


#################### AUTHENTICATION #####################
#region AUTHENTICATION
@route(
    request_method=app.post,
    path='/api/signup',
    status_code=status.HTTP_201_CREATED,
    payload_key='email_password',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=False,
    tags=["Authentication"],
)
async def create_user(
    email_password: LoginDataCreate,
    request: Request,
    response: Response,
):
    pass


@route(
    request_method=app.post,
    path='/api/login',
    status_code=status.HTTP_200_OK,
    payload_key='email_password',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=False,
    post_processing_func='auth.sign_jwt',
    tags=["Authentication"],
)
async def login(
    email_password: Login,
    request: Request,
    response: Response
):
    pass


@route(
    request_method=app.post,
    path='/api/users/email/forgot_password',
    status_code=status.HTTP_200_OK,
    payload_key= 'email_verify',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=False,
    tags=["Authentication"],
)

async def send_email_forgot_password(
    email_verify: Email,
    request: Request,
    response: Response,
):
    pass


@route(
    request_method=app.put,
    path='/api/users/password/change/{forgot_token}',
    status_code=status.HTTP_200_OK,
    payload_key='pwd_update',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    tags=["Authentication"],
)
async def change_password_by_verify_token(
    forgot_token: str,
    pwd_update: UserPasswordUpdate,
    request: Request,
    response: Response,
):
    pass

@route(
    request_method=app.post,
    path='/api/users/unipile/linkedin/auth-url',
    status_code=status.HTTP_201_CREATED,
    payload_key="unipile_auth_url",
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    tags=["Authentication"],
)
async def generate_linkedin_auth_url(
    unipile_auth_url: UnipileCreateAuthUrl,
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    pass

@route(
    request_method=app.post,
    path='/api/users/unipile/linkedin/reconnect-auth-url',
    status_code=status.HTTP_201_CREATED,
    payload_key="unipile_auth_url",
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    tags=["Authentication"],
)
async def generate_reconnect_linkedin_auth_url(
    unipile_auth_url: UnipileCreateAuthUrl,
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    pass

# @route(
#     request_method=app.post,
#     path='/api/users/unipile/email/auth-url',
#     status_code=status.HTTP_201_CREATED,
#     payload_key="unipile_auth_url",
#     service_url=settings.USER_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_default_user',
#     service_header_generator='auth.generate_request_header',
#     response_model=None,
#     tags=["Authentication"],
# )
# async def generate_email_auth_url(
#     unipile_auth_url: UnipileCreateAuthUrl,
#     request: Request,
#     response: Response,
#     token = Security(api_key_header)
# ):
#     pass

#endregion 



# @route(
#     request_method=app.post,
#     path='/api/users/unipile/email/auth-url',
#     status_code=status.HTTP_201_CREATED,
#     payload_key="unipile_auth_url",
#     service_url=settings.USER_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_default_user',
#     service_header_generator='auth.generate_request_header',
#     response_model=None,
#     tags=["Authentication"],
# )
# async def generate_email_auth_url(
#     unipile_auth_url: UnipileCreateAuthUrl,
#     request: Request,
#     response: Response,
#     token = Security(api_key_header)
# ):
#     pass

#endregion 




#################### PERSONAL #####################
#region PERSONAL
@route(
    request_method=app.post,
    path='/api/users/unipile/linkedin/connect',
    status_code=status.HTTP_201_CREATED,
    payload_key="unipile_callback",
    service_url=settings.USER_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    tags=["Personal"],
)
async def create_linkedin_connection(
    unipile_callback: UnipileCallback,
    request: Request,
    response: Response
):
    pass

@route(
    request_method=app.post,
    path='/api/users/unipile/linkedin/reconnect',
    status_code=status.HTTP_201_CREATED,
    payload_key="unipile_callback",
    service_url=settings.USER_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    tags=["Personal"],
)
async def reconnect_linkedin_connection(
    unipile_callback: UnipileCallback,
    request: Request,
    response: Response
):
    pass

@route(
    request_method=app.post,
    path='/api/users/unipile/linkedin/status-update',
    status_code=status.HTTP_201_CREATED,
    payload_key="unipile_status_update",
    service_url=settings.USER_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    tags=["Personal"],
)
async def update_linkedin_connection_status(
    unipile_status_update: UnipileStatusUpdate,
    request: Request,
    response: Response
):
    pass


@route(
    request_method=app.put,
    path='/api/users/unipile/linkedin/disconnect',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    tags=["Personal"],
)
async def remove_linkedin_connection(
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    pass

@route(
    request_method=app.put,
    path='/api/users/me/password/change',
    status_code=status.HTTP_200_OK,
    payload_key='pwd_update',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    tags=["Personal"],
)
async def change_password(
    pwd_update: UserPasswordUpdate,
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    pass


@route(
    request_method=app.get,
    path='/api/users/me',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model='schema.user_schema.UserResponse',
    tags=["Personal"],
)
async def get_personal_information(
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    pass


@route(
    request_method=app.put,
    path='/api/users/me/update-info',
    status_code=status.HTTP_200_OK,
    payload_key="update_info",
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    tags=["Personal"],
)
async def update_personal_information(
    update_info: UserUpdate,
    request: Request, response: Response,
    token = Security(api_key_header)
):
    pass

#endregion








#################### ADMIN #####################
#region ADMIN



#region ELASTICSEARCH
@route(
    request_method=app.get,
    path='/api/campaign/elastic-search/records/{page_size}/{from_}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Elasticsearch Management"],
)
async def get_es_records(
    page_size: int,
    from_: int,
    request: Request, 
    response: Response,
    token=Security(api_key_header)
):
    pass

@file_route(
    request_method=app.post,
    path="/api/campaign/elastic-search/ingest",
    status_code=status.HTTP_200_OK,
    payload_key="uploaded_files",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Elasticsearch Management"],        
)
async def ingest_companies(
    request: Request,
    response: Response,
    uploaded_files: UploadFile = File(...),
    token=Security(api_key_header)    
):
    pass


@route(
    request_method=app.delete,
    path="/api/campaign/elastic-search/remove-index",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Elasticsearch Management"]        
)
async def remove_es_index(
    request: Request,
    response: Response,
    token=Security(api_key_header)        
):
    pass
#endregion




#region EMAIL SENDERS
@file_route(
    request_method=app.post,
    path="/api/admin/senders/add",
    status_code=status.HTTP_200_OK,
    payload_key="uploaded_files",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Senders"],        
)
async def add_email_sender(
    request: Request,
    response: Response,
    uploaded_files: UploadFile = File(...),
    token=Security(api_key_header)    
):
    pass

#get email senders
@route(
    request_method=app.get,
    path="/api/admin/senders",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Senders"]
)
async def get_email_senders(
    request: Request,
    response: Response,
    token=Security(api_key_header)    
):
    pass

@route(
    request_method=app.delete,
    path="/api/admin/senders/{sender_id}/delete",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Senders"]
)
async def delete_email_sender(
    sender_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)    
):
    pass

#endregion

@file_route(
    request_method=app.post,
    path="/api/nlp/faqs/upload",
    status_code=status.HTTP_200_OK,
    payload_key="uploaded_files",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Admin"],        
)
async def upload_faqs(
    request: Request,
    response: Response,
    uploaded_files: UploadFile = File(...),
    token=Security(api_key_header)    
):
    pass

@route(
    request_method=app.get,
    path='/api/nlp/prompts/email-gen',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    tags=["Admin"],
)
async def get_email_gen_prompts(
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    pass

@route(
    request_method=app.put,
    path='/api/nlp/prompts/email-gen/enable',
    status_code=status.HTTP_200_OK,
    payload_key='enable_prompt',
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    tags=["Admin"],
)
async def enable_email_gen_prompt(
    enable_prompt: EnablePrompt,
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    pass



@route(
    request_method=app.get,
    path='/api/users',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    tags=["Admin"],
)
async def get_total_users(
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    pass


# @route(
#     request_method=app.get,
#     path='/api/users/{user_id}',
#     status_code=status.HTTP_200_OK,
#     payload_key=None,
#     service_url=settings.USER_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_admin_user',
#     service_header_generator='auth.generate_request_header',
#     response_model='schema.user_schema.UserResponse',
#     response_list=False,
#     tags=["Admin"],
# )
# async def get_user(
#     user_id: uuid.UUID, 
#     request: Request, 
#     response: Response, 
#     token = Security(api_key_header)
# ):
#     pass


# @route(
#     request_method=app.put,
#     path='/api/users/{user_id}',
#     status_code=status.HTTP_200_OK,
#     payload_key='user',
#     service_url=settings.USER_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_admin_user',
#     service_header_generator='auth.generate_request_header',
#     response_model='schema.user_schema.AccountResponse',
#     tags=["Admin"],
# )
# async def update_user(
#     user_id: uuid.UUID,
#     user: AdminAccountUpdate,
#     request: Request,
#     response: Response,
#     token = Security(api_key_header)
# ):
#     pass


# @route(
#     request_method=app.delete,
#     path='/api/users/{user_id}',
#     status_code=status.HTTP_200_OK,
#     payload_key=None,
#     service_url=settings.USER_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_admin_user',
#     service_header_generator='auth.generate_request_header',
#     tags=["Admin"],
# )
# async def delete_user(
#     user_id: uuid.UUID,
#     request: Request, 
#     response: Response,
#     token = Security(api_key_header)
# ):
#     pass


# @route(
#     request_method=app.put,
#     path='/api/users/role/{user_id}',
#     status_code=status.HTTP_200_OK,
#     payload_key='role_update',
#     service_url=settings.USER_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_admin_user',
#     service_header_generator='auth.generate_request_header',
#     tags=["Admin"],
# )

# async def update_user_role(
#     user_id: uuid.UUID,
#     role_update: RoleUpdate,
#     request: Request, 
#     response: Response,
#     token = Security(api_key_header)
# ):
#     pass


# @route(
#     request_method=app.post,
#     path='/api/users/role/create',
#     status_code=status.HTTP_200_OK,
#     payload_key='user_create',
#     service_url=settings.USER_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_admin_user',
#     service_header_generator='auth.generate_request_header',
#     tags=["Admin"],
# )

# async def create_user_role(
#     user_create: RoleCreate,
#     request: Request, 
#     response: Response,
#     token = Security(api_key_header)
# ):
#     pass


# @route(
#     request_method=app.put,
#     path='/api/users/admin/password/change',
#     status_code=status.HTTP_200_OK,
#     payload_key='pwd_update',
#     service_url=settings.USER_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_admin_user',
#     service_header_generator='auth.generate_request_header',
#     response_model=None,
#     tags=["Admin"],
# )
# async def admin_change_password(
#     pwd_update: UserPasswordReset,
#     request: Request,
#     response: Response,
#     token = Security(api_key_header)
# ):
#     pass

#endregion



#endregion




#################### CONVERSATION #####################
#region CONVERSATION
@route(
    request_method=app.post,
    path='/api/conversation/new',
    status_code=status.HTTP_201_CREATED,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model='schema.chat_schema.ConversationResponse',
    tags=["Conversation"],
)
async def create_conversation(
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    pass


@route(
    request_method=app.get,
    path='/api/conversation-list',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model='schema.chat_schema.ConversationResponse',
    response_list=True,
    tags=["Conversation"],
)
async def get_conversation_list_by_user_id(
    request: Request,
    response: Response,
    token = Security(api_key_header),
):
    pass


@route(
    request_method=app.get,
    path='/api/conversation/{conversation_id}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_list=True,
    tags=["Conversation"],
)
async def get_history_by_conversation_id(
    conversation_id: uuid.UUID,
    request: Request,
    response: Response,
    token = Security(api_key_header)
):
    pass


@route(
    request_method=app.post,
    path='/api/conversation/message',
    status_code=status.HTTP_201_CREATED,
    payload_key="message_data",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_stream=True,
    tags=["Conversation"],
)
async def send_message(
    message_data: ChatMessageCreate,
    request: Request,
    response: Response,
    token = Security(api_key_header),
):
    pass


@route(
    request_method=app.post,
    path='/api/openai/text-to-speech',
    status_code=status.HTTP_201_CREATED,
    payload_key="message_data",
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_stream=False,
    tags=["Conversation"],
)
async def get_audio(
    message_data: AudioCreate,
    request: Request,
    response: Response,
    token = Security(api_key_header),
):
    pass

@route(
    request_method=app.post,
    path='/api/campaign/initiation',
    status_code=status.HTTP_200_OK,
    payload_key="input_data",
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_stream=False,
    tags=["Campaign Management"],
)
async def campaign_initiation(
    input_data: TextCampaign,
    request: Request,
    response: Response,
    token = Security(api_key_header),
):
    pass

#endregion




#################### CAMPAIGN MANAGEMENT #####################
#region CAMPAIGN
@route(
    request_method=app.post,
    path='/api/campaign/webhooks/test',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=None,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def webhook(
    request: Request,
    response: Response,
):
    pass
@route(
    request_method=app.post,
    path='/api/llm/campaign/parser',
    status_code=status.HTTP_200_OK,
    payload_key='request_data',
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def parse_campaign_information(
    request_data: TextCampaign,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.post,
    path='/api/campaign/new',
    status_code=status.HTTP_200_OK,
    payload_key='campaign_create',
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def create_new_campaign(
    campaign_create: CampaignCreate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.get,
    path='/api/campaign/personal',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def get_personal_campaign(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.get,
    path='/api/campaign/{campaign_id}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def get_campaign(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)    

):
    pass

#START CAMPAIGN                              
@route(
    request_method=app.get,
    path="/api/campaign/status/",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def get_user_campaign_status(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.get,
    path="/api/campaign/status/{campaign_id}",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def get_single_campaign_status(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.put,
    path="/api/campaign/{campaign_id}/update-content-format",
    status_code=status.HTTP_200_OK,
    payload_key='campaign_content_update',
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def update_content_format(
    campaign_id: uuid.UUID,
    campaign_content_update: CampaignContentUpdate,    
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.put,
    path="/api/campaign/{campaign_id}/update-details",
    status_code=status.HTTP_200_OK,
    payload_key='campaign_update',
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def update_campaign_details(
    campaign_id: uuid.UUID,
    campaign_update: CampaignDetailsUpdate,    
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.put,
    path="/api/campaign/{campaign_id}/update-calendly",
    status_code=status.HTTP_200_OK,
    payload_key='campaign_contact',
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def update_campaign_contact(
    campaign_id: uuid.UUID,
    campaign_contact: CampaignContact,    
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.delete,
    path="/api/campaign/{campaign_id}/delete",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def delete_campaign_by_id(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@file_route(
    request_method=app.post,
    path="/api/campaign/companies/match/test",
    status_code=status.HTTP_200_OK,
    payload_key="uploaded_files",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Test Endpoints"],        
)
async def test_matching_companies(
    request: Request,
    response: Response,
    uploaded_files: UploadFile = File(...),
    token=Security(api_key_header)    
):
    pass

@route(
    request_method=app.post,
    path="/api/campaign/companies/match/string-matching",
    status_code=status.HTTP_200_OK,
    payload_key='company_matching',
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Test Endpoints"],
)
async def string_matching_companies(
    company_matching: CompanyMatching,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.post,
    path="/api/campaign/companies/match/hybrid",
    status_code=status.HTTP_200_OK,
    payload_key='company_matching',
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Test Endpoints"],
)
async def hybrid_matching_companies(
    company_matching: CompanyMatching,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.post,
    path="/api/campaign/companies/match/semantic",
    status_code=status.HTTP_200_OK,
    payload_key='company_matching',
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Test Endpoints"],
)
async def semantic_matching_companies(
    company_matching: CompanyMatching,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.post,
    path="/api/campaign/companies/match/v2",
    status_code=status.HTTP_200_OK,
    payload_key='company_matching',
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign Management"],
)
async def get_matching_companies_v2(
    company_matching: CompanyMatching,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

#endregion




#################### EMAIL OUTREACH #####################
#region EMAIL OUTREACH
@route(
    request_method=app.post,
    path='/api/campaign/emails/generate',
    status_code=status.HTTP_200_OK,
    payload_key="email_generation",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def generate_email(
    email_generation: EmailGeneration,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.put,
    path='/api/campaign/emails/draft/update',
    status_code=status.HTTP_200_OK,
    payload_key="update_draft_payload",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def update_draft_email(
    update_draft_payload: UpdateDraftEmail,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.get,
    path="/api/campaign/emails/draft/{company_id}",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def get_draft_emails(
    company_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.post,
    path='/api/campaign/emails/send',
    status_code=status.HTTP_200_OK,
    payload_key="email_request",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def send_email(
    email_request: SendEmailRequest,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.post,
    path='/api/campaign/{campaign_id}/outreach/auto',
    status_code=status.HTTP_200_OK,
    payload_key="email_outreach",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def start_auto_outreach(
    campaign_id: uuid.UUID,
    email_outreach: StartAutoOutreach,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.put,
    path='/api/campaign/{campaign_id}/outreach/stop-auto',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def stop_auto_outreach(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.get,
    path="/api/campaign/{campaign_id}/outreach/get-auto-status",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def get_auto_outreach_status(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass



@route(
    request_method=app.post,
    path="/api/campaign/emails/reply-mails/respond",
    status_code=status.HTTP_200_OK,
    payload_key="respond_email",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def respond_reply_email(
    respond_email: RespondEmail,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.get,
    path="/api/campaign/{campaign_id}/emails/sent-mails",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def get_sent_emails(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.get,
    path="/api/campaign/companies/{company_id}/emails/reply-mails",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def get_reply_emails(
    company_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.get,
    path="/api/campaign/companies/{company_id}/emails/conversation",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def get_email_conversation(
    company_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.delete,
    path="/api/campaign/emails/{sent_email_id}/delete",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def delete_sent_email(
    sent_email_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.get,
    path="/api/campaign/{campaign_id}/emails/outreach-stats",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Email Outreach"],
)
async def email_outreach_statistic(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

#endregion




#region LINKEDIN OUTREACH
@route(
    request_method=app.post,
    path='/api/campaign/linkedin/messages/send',
    status_code=status.HTTP_200_OK,
    payload_key="linkedin_message",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Linkedin Outreach"],
)
async def send_linked_message(
    linkedin_message: SendLinkedInRequest,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.get,
    path="/api/campaign/{campaign_id}/linkedin/message/sent-messages",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Linkedin Outreach"],
)
async def get_sent_messages(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.get,
    path="/api/campaign/companies/{company_id}/linkedin/reply-messages",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Linkedin Outreach"],
)
async def get_reply_messages(
    company_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.get,
    path="/api/campaign/companies/{company_id}/linkedin/messages/chat",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Linkedin Outreach"],
)
async def get_chat_messages(
    company_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.post,
    path='/api/campaign/linkedin/message/respond',
    status_code=status.HTTP_200_OK,
    payload_key="respond_message",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Linkedin Outreach"],
)
async def respond_linkedin_message(
    respond_message: RespondMessage,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


#endregion


#################### CAMPAIGN-COMPANY MANAGEMENT #####################
#region CAMPAIGN-COMPANY
@route(
    request_method=app.get,
    path="/api/campaign/{campaign_id}/companies/count",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def get_company_count(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.get,
    path="/api/campaign/{campaign_id}/companies/{page}/{page_size}",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def get_companies_by_campaign_id(
    campaign_id: uuid.UUID,
    page: int,
    page_size: int,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.put,
    path="/api/campaign/companies/update-email-status",
    status_code=status.HTTP_200_OK,
    payload_key="company_email_update",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def update_company_email(   
    company_email_update: CompanyEmailUpdate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.put,
    path="/api/campaign/companies/update-linkedin-msg-status",
    status_code=status.HTTP_200_OK,
    payload_key="company_linkedin_update",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def update_company_linkedin_msg(
    company_linkedin_update: CompanyLinkedInMsgUpdate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.post,
    path='/api/campaign/{campaign_id}/companies/add',
    status_code=status.HTTP_200_OK,
    payload_key='company_add',
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def add_company_manual(
    campaign_id: uuid.UUID,
    company_add : CompanyAddManual,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.post,
    path='/api/campaign/{campaign_id}/companies/add-multiple/{search_size}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def add_companies(
    campaign_id: uuid.UUID,
    search_size: int,    
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@file_route(
    request_method=app.post,
    path="/api/campaign/{campaign_id}/companies/add/file",
    status_code=status.HTTP_200_OK,
    payload_key="uploaded_files",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],        
)
async def ingest_companies(
    campaign_id: uuid.UUID,
    request: Request,
    response: Response,
    uploaded_files: UploadFile = File(...),
    token=Security(api_key_header)    
):
    pass


@route(
    request_method=app.delete,
    path="/api/campaign/companies/{company_id}/delete",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def delete_company_by_id(
    company_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.get,
    path="/api/campaign/companies/{company_id}/representative/",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def get_company_representative(
    company_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.put,
    path="/api/campaign/companies/{company_id}/representatives/{rep_id}/humantic/update",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def update_humantic_profile(
    company_id: uuid.UUID,
    rep_id: uuid.UUID,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.get,
    path="/api/campaign/humantic/profiles",
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder="auth.decode_jwt",
    service_authorization_checker="auth.is_default_user",
    service_header_generator="auth.generate_request_header",
    response_model=None,
    response_stream=False,
    tags=["Campaign-Company Management"],
)
async def get_humantic_cached_profiles(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

#endregion




#################### WEBHOOKS #####################
#region WEBHOOKS
@route(
    request_method=app.post,
    path='/api/campaign/emails/webhooks/sent',
    status_code=status.HTTP_200_OK,
    payload_key="webhooks_new_email",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Webhooks"],
)
async def webhooks_sent_email(
    webhooks_new_email: WebhooksNewEmail,
    request: Request,
    response: Response
):
    pass

@route(
    request_method=app.post,
    path='/api/campaign/emails/webhooks/sent/v2',
    status_code=status.HTTP_200_OK,
    payload_key="webhooks_new_email",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Webhooks"],
)
async def webhooks_sent_email_v2(
    webhooks_new_email: WebhooksNewEmail,
    request: Request,
    response: Response
):
    pass

@route(
    request_method=app.post,
    path='/api/campaign/emails/webhooks/received',
    status_code=status.HTTP_200_OK,
    payload_key="webhooks_new_email",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Webhooks"],
)
async def webhooks_received_email(
    webhooks_new_email: WebhooksNewEmail,
    request: Request,
    response: Response
):
    pass

@route(
    request_method=app.post,
    path='/api/campaign/emails/webhooks/tracking',
    status_code=status.HTTP_200_OK,
    payload_key="webhooks_email_tracking",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Webhooks"],
)
async def webhooks_track_email(
    webhooks_email_tracking: WebhooksEmailTracking,
    request: Request,
    response: Response
):
    pass

@route(
    request_method=app.post,
    path='/api/campaign/linkedin/webhooks/message/new',
    status_code=status.HTTP_200_OK,
    payload_key="webhooks_new_message",
    service_url=settings.CAMPAIGN_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Webhooks"],
)
async def webhooks_new_linkedin_message(
    webhooks_new_message: WebhooksNewMessage,
    request: Request,
    response: Response
):
    pass


#endregion




#region TEST ENDPOINT
# @route(
#     request_method=app.post,
#     path='/api/test',
#     status_code=status.HTTP_200_OK,
#     payload_key="test_payload",
#     service_url=settings.CAMPAIGN_SERVICE_URL,
#     authentication_required=False,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_default_user',
#     service_header_generator='auth.generate_request_header',
#     response_model=None,
#     response_stream=False,
#     tags=["Test Endpoints"],
# )
# async def schedule_job(
#     test_payload: TestScheduleJob,
#     request: Request,
#     response: Response
# ):
#     pass

# @route(
#     request_method=app.post,
#     path='/api/nlp/response/generate',
#     status_code=status.HTTP_200_OK,
#     payload_key='input_str',
#     service_url=settings.AI_SERVICE_URL,
#     authentication_required=False,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_default_user',
#     service_header_generator='auth.generate_request_header',
#     response_model=None,
#     response_stream=False,
#     tags=["Test Endpoints"],
# )
# async def generate_response(
#     input_str: GenerateResponse,
#     request: Request,
#     response: Response,
#     token=Security(api_key_header)
# ):
#     pass

# @route(
#     request_method=app.post,
#     path='/api/campaign/emails/generate/v2',
#     status_code=status.HTTP_200_OK,
#     payload_key="email_generation",
#     service_url=settings.CAMPAIGN_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_default_user',
#     service_header_generator='auth.generate_request_header',
#     response_model=None,
#     response_stream=False,
#     tags=["Test Endpoints"],
# )
# async def generate_email_v2(
#     email_generation: EmailGeneration,
#     request: Request,
#     response: Response,
#     token=Security(api_key_header)
# ):
#     pass

# @route(
#     request_method=app.post,
#     path='/api/nlp/text-embedding',
#     status_code=status.HTTP_200_OK,
#     payload_key=None,
#     service_url=settings.AI_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_default_user',
#     service_header_generator='auth.generate_request_header',
#     response_model=None,
#     response_stream=False,
#     tags=["Test Endpoints"],
# )
# async def get_text_embedding(
#     request: Request,
#     response: Response,
#     token=Security(api_key_header)
# ):
#     pass


# @route(
#     request_method=app.post,
#     path='/api/nlp/html-to-plain-text',
#     status_code=status.HTTP_200_OK,
#     payload_key="html_content",
#     service_url=settings.AI_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder='auth.decode_jwt',
#     service_authorization_checker='auth.is_default_user',
#     service_header_generator='auth.generate_request_header',
#     response_model=None,
#     response_stream=False,
#     tags=["Test Endpoints"],
# )
# async def html_to_plain_text(
#     html_content: HtmlToText,
#     request: Request,
#     response: Response,
#     token=Security(api_key_header)
# ):
#     pass

@route(
    request_method=app.post,
    path='/api/nlp/similarity',
    status_code=status.HTTP_200_OK,
    payload_key="similarity_check",
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Test Endpoints"]    
)
async def get_similarity(
    similarity_check: SimilarityCheck,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


#endregion




#region ELASTICSEARCH TEST
# @file_route(
#     request_method=app.post,
#     path="/api/elasticsearch-test/ingest/{embedding_dim}",
#     status_code=status.HTTP_200_OK,
#     payload_key="uploaded_files",
#     service_url=settings.CAMPAIGN_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder="auth.decode_jwt",
#     service_authorization_checker="auth.is_default_user",
#     service_header_generator="auth.generate_request_header",
#     response_model=None,
#     response_stream=False,
#     tags=["ElasticSearch Test"],        
# )
# async def ingest_companies_test(
#     embedding_dim: int,
#     request: Request,
#     response: Response,
#     uploaded_files: UploadFile = File(...),
#     token=Security(api_key_header)    
# ):
#     pass

# @file_route(
#     request_method=app.post,
#     path="/api/elasticsearch-test/match/test",
#     status_code=status.HTTP_200_OK,
#     payload_key="uploaded_files",
#     service_url=settings.CAMPAIGN_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder="auth.decode_jwt",
#     service_authorization_checker="auth.is_default_user",
#     service_header_generator="auth.generate_request_header",
#     response_model=None,
#     response_stream=False,
#     tags=["ElasticSearch Test"],        
# )
# async def test_matching_companies(
#     request: Request,
#     response: Response,
#     uploaded_files: UploadFile = File(...),
#     token=Security(api_key_header)    
# ):
#     pass


# @route(
#     request_method=app.post,
#     path="/api/elasticsearch-test/match/string-matching",
#     status_code=status.HTTP_200_OK,
#     payload_key='company_matching',
#     service_url=settings.CAMPAIGN_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder="auth.decode_jwt",
#     service_authorization_checker="auth.is_default_user",
#     service_header_generator="auth.generate_request_header",
#     response_model=None,
#     response_stream=False,
#     tags=["ElasticSearch Test"],
# )
# async def string_matching_companies(
#     company_matching: CompanyMatching,
#     request: Request,
#     response: Response,
#     token=Security(api_key_header)
# ):
#     pass


# @route(
#     request_method=app.post,
#     path="/api/elasticsearch-test/match/hybrid/{embedding_dim}",
#     status_code=status.HTTP_200_OK,
#     payload_key='company_matching',
#     service_url=settings.CAMPAIGN_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder="auth.decode_jwt",
#     service_authorization_checker="auth.is_default_user",
#     service_header_generator="auth.generate_request_header",
#     response_model=None,
#     response_stream=False,
#     tags=["ElasticSearch Test"],
# )
# async def hybrid_matching_companies(
#     embedding_dim: int,
#     company_matching: CompanyMatching,
#     request: Request,
#     response: Response,
#     token=Security(api_key_header)
# ):
#     pass


# @route(
#     request_method=app.post,
#     path="/api/elasticsearch-test/match/semantic/{embedding_dim}",
#     status_code=status.HTTP_200_OK,
#     payload_key='company_matching',
#     service_url=settings.CAMPAIGN_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder="auth.decode_jwt",
#     service_authorization_checker="auth.is_default_user",
#     service_header_generator="auth.generate_request_header",
#     response_model=None,
#     response_stream=False,
#     tags=["ElasticSearch Test"],
# )
# async def semantic_matching_companies(
#     embedding_dim: int,
#     company_matching: CompanyMatching,
#     request: Request,
#     response: Response,
#     token=Security(api_key_header)
# ):
#     pass


# @route(
#     request_method=app.post,
#     path="/api/elasticsearch-test/match/{embedding_dim}",
#     status_code=status.HTTP_200_OK,
#     payload_key='company_matching',
#     service_url=settings.CAMPAIGN_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder="auth.decode_jwt",
#     service_authorization_checker="auth.is_default_user",
#     service_header_generator="auth.generate_request_header",
#     response_model=None,
#     response_stream=False,
#     tags=["ElasticSearch Test"],
# )
# async def get_matching_companies(
#     embedding_dim: int,
#     company_matching: CompanyMatching,
#     request: Request,
#     response: Response,
#     token=Security(api_key_header)
# ):
#     pass

# @route(
#     request_method=app.delete,
#     path="/api/elasticsearch-test/remove-index",
#     status_code=status.HTTP_200_OK,
#     payload_key=None,
#     service_url=settings.CAMPAIGN_SERVICE_URL,
#     authentication_required=True,
#     post_processing_func=None,
#     authentication_token_decoder="auth.decode_jwt",
#     service_authorization_checker="auth.is_default_user",
#     service_header_generator="auth.generate_request_header",
#     response_model=None,
#     response_stream=False,
#     tags=["Elasticsearch Management"]        
# )
# async def remove_es_index(
#     request: Request,
#     response: Response,
#     token=Security(api_key_header)        
# ):
#     pass

#endregion




#region MODEL
#################### MODEL MANAGEMENT #####################
@route(
    request_method=app.post,
    path='/api/model/new',
    status_code=status.HTTP_200_OK,
    payload_key='model_infor',
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Model Management"],
)
async def create_model(
    model_infor: ModelUpdate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.get,
    path='/api/model',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Model Management"],
)
async def get_all_model(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.get,
    path='/api/model/{model_id}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Model Management"],
)
async def get_model(
    model_id: int,
    request: Request, 
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.put,
    path='/api/model/edit',
    status_code=status.HTTP_200_OK,
    payload_key='model_infor',
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Model Management"],
)
async def update_model(
    model_infor: ModelUpdate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


@route(
    request_method=app.delete,
    path='/api/model/{model_id}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.AI_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Model Management"],
)
async def delete_model(
    model_id: int,
    request: Request, 
    response: Response,
    token=Security(api_key_header)
):
    pass
#endregion


#region CREDIT MANAGEMENT
@route(
    request_method=app.get,
    path='/api/users/credits',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Credit Management"],
)
async def get_user_credits(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.get,
    path='/api/users/credits/usage',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Credit Management"],
)
async def get_user_credit_usage(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.get,
    path='/api/users/credits/transactions',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Credit Management"],
)
async def get_user_credit_transactions(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.get,
    path='/api/users/{user_id}/credits/transactions',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Credit Management"],
)
async def get_user_credit_transactions(
    user_id: str,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass
#endregion


#region PAYMENT MANAGEMENT
@route(
    request_method=app.post,
    path='/api/stripe/checkout/session/create',
    status_code=status.HTTP_201_CREATED,
    payload_key='checkout_session_create',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Payment Management"],
)
async def create_checkout_session(
    checkout_session_create: CheckoutSessionCreate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.post,
    path='/api/stripe/checkout/session/success',
    status_code=status.HTTP_200_OK,
    payload_key='event_object',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=False,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Payment Management"],
)
async def handle_checkout_session_success(
    event_object: EventObject,
    request: Request,
    response: Response,
):
    pass

@route(
    request_method=app.get,
    path='/api/stripe/checkout/session/{checkout_session_id}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Payment Management"],
)
async def get_checkout_session(
    checkout_session_id: str,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass


#endregion

#region STRIPE PRODUCT MANAGEMENT
@route(
    request_method=app.post,
    path='/api/stripe/products/create',
    status_code=status.HTTP_201_CREATED,
    payload_key='product_create',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def create_stripe_product(
    product_create: StripeProductCreate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.put,
    path='/api/stripe/products/{product_id}/update',
    status_code=status.HTTP_200_OK,
    payload_key='product_update',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def update_stripe_product(
    product_id: str,
    product_update: StripeProductUpdate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.delete,
    path='/api/stripe/products/{product_id}/delete',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def delete_stripe_product(
    product_id: str,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.get,
    path='/api/stripe/products/{product_id}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def get_stripe_product(
    product_id: str,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.get,
    path='/api/stripe/products',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def get_stripe_products(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.post,
    path='/api/stripe/prices/create',
    status_code=status.HTTP_201_CREATED,
    payload_key='price_create',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def create_stripe_price(
    price_create: StripePriceCreate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.put,
    path='/api/stripe/prices/{price_id}/update',
    status_code=status.HTTP_200_OK,
    payload_key='price_update',
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_admin_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def update_stripe_price(
    price_id: str,
    price_update: StripePriceUpdate,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.get,
    path='/api/stripe/prices/{price_id}',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def get_stripe_price(
    price_id: str,
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

@route(
    request_method=app.get,
    path='/api/stripe/prices',
    status_code=status.HTTP_200_OK,
    payload_key=None,
    service_url=settings.USER_SERVICE_URL,
    authentication_required=True,
    post_processing_func=None,
    authentication_token_decoder='auth.decode_jwt',
    service_authorization_checker='auth.is_default_user',
    service_header_generator='auth.generate_request_header',
    response_model=None,
    response_stream=False,
    tags=["Stripe Product Management"],
)
async def get_stripe_prices(
    request: Request,
    response: Response,
    token=Security(api_key_header)
):
    pass

#endregion

UNIPILE_SUBDOMAIN = "api15"
UNIPILE_PORT = "14599"
UNIPILE_API_KEY = "tfKWJRUc.x0E7IhKeKDegFMCAbZJARD4aTikr6tZNVpGMy3ifTSk="

def delete_ngrok_webhooks():
    url = f"https://{UNIPILE_SUBDOMAIN}.unipile.com:{UNIPILE_PORT}/api/v1/webhooks"

    headers = {
        "accept": "application/json",
        "X-API-KEY": UNIPILE_API_KEY
    }
    response = requests.get(url=url, headers=headers)
    if response.status_code not in [200]:
        logger.info(response.text)
        raise Exception(response.text)
    
    webhooks_list = response.json()["items"]
    for webhook in webhooks_list:
        if webhook["name"] in ["ngrok_new_email_sent", "ngrok_new_email_received", "ngrok_email_tracking", "ngrok_new_message", "ngrok_linkedin_status_update"]:
            webhook_id = webhook["id"]
            delete_url = f"https://{UNIPILE_SUBDOMAIN}.unipile.com:{UNIPILE_PORT}/api/v1/webhooks/{webhook_id}"
            response = requests.delete(url=delete_url, headers=headers)
            logger.info(response.text)

def set_ngrok_webhooks(base_url):
    url = f"https://{UNIPILE_SUBDOMAIN}.unipile.com:{UNIPILE_PORT}/api/v1/webhooks"

    headers = {
        "accept": "application/json",
        "content-type": "application/json",
        "X-API-KEY": UNIPILE_API_KEY
    }    

    #set up new_email_sent webhooks
    email_sent_url = base_url + '/api/campaign/emails/webhooks/sent'
    payload = {
        "source": "email",
        # "account_ids": ["eioPLkMmSB2IL5zyXusUBw"],
        "enabled": True,
        "events": ["mail_sent"],
        "name": "ngrok_new_email_sent",
        "request_url": email_sent_url,
        "format": "json",
        "headers": [
            {
                "key": "Content-Type",
                "value": "application/json"
            }
        ]        
    }    
    response = requests.post(url=url, json=payload, headers=headers)
    if response.status_code not in [200,201]:
        logger.error(response.text)

    #set up new_email_received webhooks
    email_received_url = base_url + '/api/campaign/emails/webhooks/received'
    payload = {
        "source": "email",
        # "account_ids": ["eioPLkMmSB2IL5zyXusUBw"],
        "enabled": True,
        "events": ["mail_received"],
        "name": "ngrok_new_email_received",
        "request_url": email_received_url,
        "format": "json",
        "headers": [
            {
                "key": "Content-Type",
                "value": "application/json"
            }
        ]
    }    
    response = requests.post(url=url, json=payload, headers=headers)
    if response.status_code not in [200,201]:
        logger.error(response.text)

    #set up email_tracking webhooks
    email_tracking_url = base_url + '/api/campaign/emails/webhooks/tracking'
    payload = {
        "source": "email_tracking",
        "request_url": email_tracking_url,
        "name": "ngrok_email_tracking",
        "format": "json",
        # "account_ids": ["eioPLkMmSB2IL5zyXusUBw"],
        "enabled": True,
        "events": ["mail_opened"],
        "headers": [
            {
                "key": "Content-Type",
                "value": "application/json"
            }
        ]
    }    
    response = requests.post(url=url, json=payload, headers=headers)
    if response.status_code not in [200,201]:
        logger.error(response.text)

    #set up linkedin_message webhooks
    linkedin_message_url = base_url + '/api/campaign/linkedin/webhooks/message/new'
    payload = {
        "source": "messaging",
        "request_url": linkedin_message_url,
        "name": "ngrok_new_message",
        "format": "json",
        # "account_ids": ["eioPLkMmSB2IL5zyXusUBw"],
        "enabled": True,
        "events": ["message_received"],
        "headers": [
            {
                "key": "Content-Type",
                "value": "application/json"
            }
        ]        
    }    
    response = requests.post(url=url, json=payload, headers=headers)
    if response.status_code not in [200,201]:
        logger.error(response.text)

    #set up linkedin_message webhooks
    account_status_url = base_url + '/api/users/unipile/linkedin/status-update'
    payload = {
        "source": "account_status",
        "request_url": account_status_url,
        "name": "ngrok_linkedin_status_update",
        "format": "json",
        # "account_ids": ["eioPLkMmSB2IL5zyXusUBw"],
        "enabled": True,
        "events": ["credentials"],
        "headers": [
            {
                "key": "Content-Type",
                "value": "application/json"
            }
        ]        
    }    
    response = requests.post(url=url, json=payload, headers=headers)
    if response.status_code not in [200,201]:
        logger.error(response.text)

if __name__ == "__main__":
    port = int(settings.SERVER_PORT)
    app_module = "main:app"
    # yjymksrzqjcapsvs
    # ngrok.set_auth_token("*************************************************")
    # public_url = ngrok.connect(port)
    # print("ngrok tunnel \"{}\" -> \"http://localhost:{}/\"".format(public_url, port))    
    # actual_url = str(public_url).replace('"','').split(' ')[1]
    # delete_ngrok_webhooks()
    # set_ngrok_webhooks(actual_url.strip())
    # logger.info(actual_url)
    # logger.info(actual_url)
    # logger.info(actual_url)

    uvicorn.run(app_module, host="0.0.0.0", port=port, reload=True)

#nothing, just a comment
#nothing, just a comment
#nothing, just a comment









