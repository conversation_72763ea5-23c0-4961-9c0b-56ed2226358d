import React, { useEffect, useState } from 'react';
import { OnboardingHeaderPart } from '../../header/OnboardingHeader';
import { useNavigate } from 'react-router-dom';
import Swal from 'sweetalert2';
import { FiMapPin } from 'react-icons/fi';
import { AiOutlineLoading } from 'react-icons/ai';
import { FaPen } from 'react-icons/fa';

const BillingPage = () => {
  const navigate = useNavigate();
  const roboman_api = process.env.REACT_APP_ROBOMAN_API;
  const roboman_website = process.env.REACT_APP_MAIN_WEBSITE;

  const [stripePrices, setStripePrices] = useState([]);
  const [loading, setLoading] = useState(true);

  const getStripePrices = async () => {
    try {
      const response = await fetch(`${roboman_api}/stripe/prices`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': localStorage.getItem('access_token'), 
        },
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('Request failed:');
      } else {
        console.log('Request successful');
        setStripePrices(data);
      }
    } catch (error) {
      console.error('Network or parsing error:', error);
    } finally {
      setLoading(false); // Stop the loading spinner
    }
  };

  useEffect(() => {
    getStripePrices()
  }, []);

  const createPaymentSession = async (payment_id) => {
    // Payment session

    try {
      const response = await fetch(`${roboman_api}/stripe/checkout/session/create`, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': localStorage.getItem('access_token'),
        },
        body: JSON.stringify({
          price_id: stripePrices[payment_id]['price_id'],         // replace with your actual price_id
          cancel_url: `${roboman_website}/dashboard`,
          success_url: `${roboman_website}/successpayment?session_id={CHECKOUT_SESSION_ID}`,
        }),
      });

      //check if the response is ok
      const data = await response.json();

      if (!response.ok) {
        console.error('Checkout session creation failed');
      } else {
        console.log('Checkout session created successfully');
        window.location.href = data.url;
      }
    } catch (error) {
      console.error('Network or parsing error:', error);
    }
  };

  const PricingPlans = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-full">
          <AiOutlineLoading className="text-4xl text-blue-500 animate-spin" />
          <span className="ml-4 text-lg text-gray-700">Loading pricing plans...</span>
        </div>
      );
    }

    if (stripePrices.length === 0) {
      return (
        <div className="flex justify-center items-center h-full">
          <span className="text-lg text-gray-700">Payment page is not available at now.</span>
        </div>
      );
    }

    return (
      <div className="bg-gray-100 flex flex-col items-center py-10 w-full">
        <h1 className="text-4xl font-bold text-center mb-10">Roboman Pricing Plans</h1>
        <div className="flex flex-wrap justify-center gap-8">
          {/* Starter Plan */}
          <div className="bg-white shadow-md rounded-lg p-6 w-[300px] flex flex-col items-center">
            {/* Plan Name */}
            <div className="bg-[#223F9E] text-white text-lg font-bold py-2 px-4 rounded-full w-full text-center">
              Starter Plan
            </div>
            {/* Price */}
            <div className="bg-gray-200 text-gray-800 text-md font-semibold py-2 px-4 w-full text-center rounded-full mt-2">
              £49.99 + VAT
            </div>
            {/* Features */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mt-4 w-full h-[300px]">
              <ul className="text-gray-700 text-sm space-y-2 mt-4">
                <li>• 200 prospects</li>
                <li>• Hyper-personalised email + LinkedIn messaging</li>
                <li>• Automated follow-ups</li>
                <li>• AI avatar setup (Anna)</li>
                <li>• Cancel anytime</li>
                <li>✅ Ideal for freelancers, solo founders, boutique agencies.</li>
              </ul>
            </div>
            {/* Start Plan Button */}
            <button 
              className="mt-6 bg-[#223F9E] text-white py-2 px-4 rounded-full hover:bg-blue-600 transition rounded-full duration-300 ease-in-out" 
              onClick={() => createPaymentSession(0)}
            >
              <strong>Buy The Plan</strong>
            </button>
          </div>

          {/* Growth Plan */}
          <div className="bg-white shadow-md rounded-lg p-6 w-[300px] flex flex-col items-center">
            {/* Plan Name */}
            <div className="bg-[#223F9E] text-white text-lg font-bold py-2 px-4 rounded-full w-full text-center">
              Growth Plan
            </div>
            {/* Price */}
            <div className="bg-gray-200 text-gray-800 text-md font-semibold py-2 px-4 w-full text-center rounded-full mt-2">
              £119.99 + VAT
            </div>
            {/* Features */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mt-4 w-full h-[300px]">
              <ul className="text-gray-700 text-sm space-y-2 mt-4">
                <li>• 500 prospects</li>
                <li>• Everything in Starter</li>
                <li>• Priority prospect matching</li>
                <li>• Personality-based A/B testing (DISC & OCEAN)</li>
                <li>• 1 strategy call/month (optional)</li>
                <li>✅ Ideal for fast-scaling consultants, agencies, startups.</li>
              </ul>
            </div>
            {/* Start Plan Button */}
            <button 
              className="mt-6 bg-[#223F9E] text-white py-2 px-4 rounded-full hover:bg-blue-600 transition rounded-full duration-300 ease-in-out" 
              onClick={() => createPaymentSession(1)}
            >
              <strong>Buy The Plan</strong>
            </button>
          </div>

          {/* Scale Plan */}
          <div className="bg-white shadow-md rounded-lg p-6 w-[300px] flex flex-col items-center">
            {/* Plan Name */}
            <div className="bg-[#223F9E] text-white text-lg font-bold py-2 px-4 rounded-full w-full text-center">
              Scale Plan
            </div>
            {/* Price */}
            <div className="bg-gray-200 text-gray-800 text-md font-semibold py-2 px-4 w-full text-center rounded-full mt-2">
              £239.99 + VAT
            </div>
            {/* Features */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mt-4 w-full h-[300px]">
              <ul className="text-gray-700 text-sm space-y-2 mt-4">
                <li>• 1000 prospects</li>
                <li>• Everything in Growth</li>
                <li>• Multi-account LinkedIn/email sending</li>
                <li>• Custom ICP refinement with Anna</li>
                <li>• Early access to new features</li>
                <li>✅ Ideal for scaling startups, lead-gen agencies, recruitment firms.</li>
              </ul>
            </div>
            {/* Start Plan Button */}
            <button 
              className="mt-6 bg-[#223F9E] text-white py-2 px-4 rounded-full hover:bg-blue-600 transition rounded-full duration-300 ease-in-out" 
              onClick={() => createPaymentSession(2)}
            >
              <strong>Buy The Plan</strong>
            </button>
          </div>
        </div>
      </div>
    );
  };

  const Footer = () => {
    // This part is for the footer section
    const showBillPage = localStorage.getItem('showbillpage');

    return (
      <div className="py-4 bg-white flex justify-end shadow-2xl">
        <div className="mb-3 mt-3">
          {showBillPage ? (
            // Show "Back" button if `showbillpage` is true
            <button
              className="bg-[#223F9E] text-white font-semibold py-2 px-10 mr-[50px] rounded-full"
              onClick={() => navigate('/dashboard')}
            >
              Back
            </button>
          ) : (
            // Show "Continue" button if `showbillpage` is false
            <button
              className="bg-[#223F9E] text-white font-semibold py-2 px-10 mr-[50px] rounded-full"
              onClick={() => navigate('/nextpage')}
            >
              Continue
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Render user data or other components here */}
      <div className="flex flex-col w-full bg-gray-100 min-h-screen">
        {/* This part is for the header section */}
        <OnboardingHeaderPart isLogout={false} />
        {/* This part is for the main/body section */}
        <div className="flex-grow">
          <PricingPlans />
        </div>
        {/* Footer is placed here to ensure it stays at the bottom */}
        <Footer />
      </div>
    </div>
  );
}

export default BillingPage;