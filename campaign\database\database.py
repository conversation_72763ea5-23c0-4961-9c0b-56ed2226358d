from core.config import settings

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_scoped_session
from typing import AsyncIterator
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy_utils import database_exists, create_database
from asyncio import current_task
from loguru import logger


POSTGRES_DATABASEURL = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.DATABASE_HOST}:{settings.DATABASE_PORT}/{settings.DATABASE_NAME}"
ASYNC_POSTGRES_DATABASEURL = f"postgresql+asyncpg://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.DATABASE_HOST}:{settings.DATABASE_PORT}/{settings.DATABASE_NAME}"

# if not database_exists(POSTGRES_DATABASEURL):
#     create_database(POSTGRES_DATABASEURL)

async_engine = create_async_engine(
    ASYNC_POSTGRES_DATABASEURL,
    echo=False,
    future=True,
    pool_size=50,
    max_overflow=100,
    pool_timeout=30,
    pool_recycle=600
)

SessionFactory = async_scoped_session(
    sessionmaker(async_engine, class_=AsyncSession, expire_on_commit=False),
    scopefunc=current_task,
)

Base = declarative_base()


async def get_session() -> AsyncIterator[AsyncSession]:
    async with SessionFactory() as session:
        try:
            yield session
        except Exception as e:
            logger.error(e)
            await session.rollback()
            raise
        finally:
            await session.close()