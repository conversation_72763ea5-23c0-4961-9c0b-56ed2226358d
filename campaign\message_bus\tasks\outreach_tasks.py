import requests
import json
import re
from bs4 import BeautifulSoup
from loguru import logger
from sqlalchemy import update, delete, distinct
from sqlalchemy.future import select
from sqlalchemy.orm import aliased
import asyncio
from sqlalchemy.ext.asyncio import  AsyncSession
from datetime import datetime, timedelta, time, date

from utils.unipile_utils.unipile_client import EmailClient, LinkedInClient
from database.database import SessionFactory, get_session
from database.models import SentEmail, ReceivedEmail, Company, Draft, Representative, SenderEmail, Campaign, LinkedInConnection, SentMessage
from message_bus.task_publisher import publish_task
from message_bus.rpc_client import RPC<PERSON>lient
from shared_state import app_state
from core.config import settings

#region EMAIL OUTREACH
async def get_sender(db: AsyncSession):
    try:
        query = select(SenderEmail).filter(SenderEmail.remaining_emails>0).order_by(SenderEmail.remaining_emails.desc(), SenderEmail.updated_at.asc())
        results = await db.execute(query)
        senders = results.scalars().all()
        if len(senders) == 0:
            return None
        else:
            sender = senders[0]
            return {
                "sender_address": sender.sender_address,
                "unipile_account_id": sender.unipile_account_id
            }
        # return {
        #     "sender_address": "<EMAIL>",
        #     "unipile_account_id": "DVuozmRlTw-my9ix8ZTJmg"
        # }
    except Exception as e:
        raise e

async def create_drafts(message):
    """
    Send an email to the recipient.
    message_body structure:\n
        {
            "campaign_id" : str
            "company_id": str
            "prompt_name": Optional[str] = None
            "auto_send_email": bool
            "auto_send_linkedin": bool
            "auto_outreach": bool
            "user_id": str
        }
    """

    await asyncio.sleep(1)
    try:
        json_string = message.body.decode()
        message_body = json.loads(json_string)
        company_id = message_body["company_id"]         # str of company_id to be processed.        
        prompt_name = message_body["prompt_name"] if "prompt_name" in message_body else None         # Optional[str] of prompt_name to be used for email generation.        
        campaign_id = message_body["campaign_id"]
        auto_outreach = message_body["auto_outreach"] if "auto_outreach" in message_body else False         # bool to determine if drafts should be sent automatically.
        # auto_send_email = message_body["auto_send_email"] if "auto_send_email" in message_body else False         # bool to determine if drafts should be sent automatically.        
        # auto_send_linkedin = message_body["auto_send_linkedin"] if "auto_send_linkedin" in message_body else False         # bool to determine if drafts should be sent automatically.        
        user_id = message_body["user_id"] if "user_id" in message_body else None
        logger.info(f"[CONSUMER] Processing create_drafts task")       
        async for db in get_session():
            query = select(Company).where(Company.company_id==company_id)
            results = await db.execute(query)
            company = results.scalars().first()
            query = select(Campaign).where(Campaign.campaign_id==campaign_id)
            results = await db.execute(query)
            campaign = results.scalars().first()

            #check if user has enough credits
            if company.credit_spent == False:
                headers = {
                    "accept": "application/json",
                    "request-user-id": str(user_id)
                }
                response = requests.get(
                    f"{settings.USER_SERVICE_URL}/api/users/credits",
                    headers=headers
                )
                if response.status_code not in [200, 201]:
                    raise Exception(f"Failed to retrieve user credits: {response.text}")
                else:
                    user_credit = response.json()
                    if user_credit["current_credits"] < 1:
                        raise Exception("User does not have enough credits")


            campaign_info = campaign.campaign_info
            campaign_contact = campaign.campaign_contact
            user_nick_name = "Robert Mandev"
            if campaign_contact.get("user_nick_name") is not None:
                user_nick_name = campaign_contact["user_nick_name"]

            query = (
                select(
                    Representative.rep_name, 
                    Representative.rep_email, 
                    Representative.personality_analysis, 
                    Representative.email_personalization, 
                    Representative.communication_advice,
                    Representative.company_id,
                    Representative.rep_linkedin_address,
                    Representative.analysis_status,
                    Company.outreach_progress,
                    Company.industry,
                    Company.company_name,
                    Company.auto_outreach
                )
                .join(Company, Representative.company_id==Company.company_id)
                .filter(Representative.company_id==company_id)
            )

            result = await db.execute(query)
            records = result.fetchall()
            for row in records:
                if row.company_id is not None and row.analysis_status in ["COMPLETE", "NOT_FOUND"]:
                    #rpc to generate response
                    # service = "aiengine.*"
                    # function_name = "generate_drafts"
                    params = {
                        "from_name": user_nick_name,
                        "to_name": row.rep_name,
                        "core_service": campaign_info["core_service"],
                        "key_benefits": campaign_info["key_benefits"],
                        "problem_solved": campaign_info["problem_solved"],
                        "must_have_info": campaign_info["must_have_info"],
                        "output_format": campaign.email_format,
                        "personality_analysis": row.personality_analysis,
                        "email_personalization": row.email_personalization,
                        "communication_advice": row.communication_advice,
                        "industry": row.industry,
                        "company_name": row.company_name,
                        "prompt_name": prompt_name
                    }

                    # rpc = await RPCClient().connect()
                    # response = await rpc.call(service=service, function_name=function_name, params=params)
                    # response_body = json.loads(response)
                    # result = response_body.get("result")

                    aiengine_url = f"{settings.AI_SERVICE_URL}/api/nlp/generate_drafts"

                    headers = {
                        "accept": "application/json",
                        "Content-Type": "application/json"
                    }
                    response = requests.post(aiengine_url, headers=headers, json=params)
                    if response.status_code not in [200, 201]:
                        raise Exception(f"Error from aiengine service: Failed to generate drafts for company_id: {row.company_id}")
                    
                    result = response.json()

                    drafts_list = result["drafts_list"]
                    drafts_to_create = []
                    for draft in drafts_list:
                        draft_body = draft["body"]                            
                        soup = BeautifulSoup(draft_body, "html.parser")
                        draft_body_plain = soup.text

                        query = select(Draft).filter(Draft.company_id==row.company_id, Draft.draft_type==draft["draft_type"])
                        result = await db.execute(query)
                        existing_draft = result.scalars().first()
                        if existing_draft is not None:
                            if existing_draft.email_sent == False:
                                stmt = (
                                    update(Draft)
                                    .filter(Draft.company_id==row.company_id, Draft.draft_type==draft["draft_type"])
                                    .values(
                                        subject = draft["subject"],
                                        body = draft_body,
                                        body_plain = draft_body_plain,
                                        day_to_send = draft["day_to_send"]
                                    )
                                )
                                await db.execute(stmt)
                            else:
                                continue
                        else:                    
                            new_draft = Draft(
                                from_name = user_nick_name,
                                draft_type = draft["draft_type"],
                                company_id = row.company_id,
                                to_address = row.rep_email,
                                to_name = row.rep_name,
                                to_linkedin_address = row.rep_linkedin_address,
                                subject = draft["subject"],
                                body = draft_body,
                                body_plain = draft_body_plain,
                                day_to_send = draft["day_to_send"]
                            )
                            drafts_to_create.append(new_draft)
                    
                    db.add_all(drafts_to_create)

                    if row.auto_outreach != True:
                        is_auto_outreach = True if auto_outreach == True else False
                    else:
                        is_auto_outreach = False

                    stmt = update(Company).filter(Company.company_id==row.company_id).values(
                        content_subject = drafts_list[0]["subject"],
                        content = drafts_list[0]["body"],
                        email_confirmation_status_id = 2,
                        auto_outreach = is_auto_outreach,
                    )
                    await db.execute(stmt)

                    if company.credit_spent == False:
                        #update user credit usage
                        headers = {
                            "accept": "application/json",
                            "request-user-id": str(user_id)
                        }
                        response = requests.put(
                            f"{settings.USER_SERVICE_URL}/api/users/credits/update-usage",
                            headers=headers,
                            json={
                                "campaign_id": str(campaign_id),
                                "company_id": str(company.company_id),
                                "campaign_name": campaign.campaign_name,
                                "company_name": company.company_name
                            }
                        )
                        if response.status_code in [200, 201]:
                            stmt = update(Company).filter(Company.company_id==row.company_id).values(credit_spent=True)
                            await db.execute(stmt)

                    await db.commit()
                    logger.info(f"[CONSUMER] Successfully created drafts for company_id: {row.company_id}")

                    # query = select(Company).filter(Company.company_id==row.company_id)
                    # result = await db.execute(query)
                    # company = result.scalars().first()
                    
                    # if company.auto_outreach == True:
                    #     if auto_send_email:
                    #         # if row.auto_send_email == True:                                  
                    #         message = {
                    #             "company_id": str(company.company_id),
                    #         }
                    #         await publish_task("send_email_outreach", message, connection_pool=app_state["rabbitmq_conn_pool"])

                    #     if auto_send_linkedin:
                    #         # if row.auto_linkedin_outreach == True:
                    #         message = {
                    #             "company_id": str(company.company_id),
                    #             "user_id": str(user_id),
                    #         }
                    #         await publish_task("send_linkedin_outreach", message, connection_pool=app_state["rabbitmq_conn_pool"])
            
    except Exception as e:
        logger.error(f"[CONSUMER] Failed to process create_drafts task\n {str(e)}") 


async def update_db_emails(draft_email: Draft, from_address: str, tracking_id: str, to_info: list, unipile_account_id: str ,db: AsyncSession):
    """Create a SentEmail record and update Draft in db"""
    try:
        sent_email = SentEmail(
            subject=draft_email.subject,
            body=draft_email.body,
            body_plain=draft_email.body_plain,
            from_address=from_address,
            to_info = to_info,
            to_addresses=[draft_email.to_address],
            company_id = draft_email.company_id,
            tracking_id = tracking_id,
            draft_type = draft_email.draft_type,
            account_id = unipile_account_id
        )        
        db.add(sent_email)
        stmt = update(Draft).where(Draft.draft_id==draft_email.draft_id).values(email_sent=True)
        await db.execute(stmt)

        stmt = update(Company).filter(Company.company_id==draft_email.company_id).values(
            outreach_progress=draft_email.draft_type,
            content_subject = draft_email.subject,
            content = draft_email.body,
            email_confirmation_status_id = 4,
            last_date_sent_email = date.today(),
            last_time_sent_email = datetime.now()
        )
        await db.execute(stmt)

        stmt = update(SenderEmail).filter(SenderEmail.sender_address==from_address).values(remaining_emails=SenderEmail.remaining_emails-1)
        await db.execute(stmt)

    except Exception as e:
        raise e


async def send_email_outreach(message):
    """
    Send an email to the recipient.
    message_body structure:\n
        {
            "company_id": str
        }
    """

    await asyncio.sleep(1)
    try:
        json_string = message.body.decode()
        message_body = json.loads(json_string)
        company_id = message_body["company_id"]
        logger.info(f"[CONSUMER] Processing send_email_outreach task")
        async for db in get_session():
            query = select(Company).filter(Company.company_id==company_id)
            result = await db.execute(query)
            company = result.scalars().first()            
            if company.email_confirmation_status_id not in [6,7]:
                query = select(Draft).filter(Draft.company_id==company_id, Draft.email_sent==False).order_by(Draft.draft_type.asc())
                result = await db.execute(query)
                drafts = result.scalars().all()
                if len(drafts) > 0:
                    draft = drafts[0]
                    draft_body = draft.body
                    query = select(Campaign.campaign_contact).filter(Campaign.campaign_id==company.campaign_id)
                    results = await db.execute(query)
                    campaign_contact = results.scalars().first()

                    if campaign_contact.get("calendly_link") is not None:
                        calendly_link = campaign_contact.get("calendly_link")
                        calendly_booking_line = f"""<p>\nBook a meeting on Calendly: <a href={calendly_link} target="_blank">Click here</a></p>"""
                        draft_body = draft_body.replace("</body>", f"{calendly_booking_line}\n\n</body>")

                    sender = await get_sender(db=db)     
                    
                    if sender is None:
                        raise Exception("[CONSUMER] No more email senders available")
                        
                    email_client = EmailClient()
                    email_client.set_unipile_account_id(sender["unipile_account_id"])
                    response = email_client.send_email(
                        subject=draft.subject,
                        content=draft_body,
                        name_from=draft.from_name.title(),
                        name_to=draft.to_name,
                        email_to=draft.to_address
                    )
                    if response.status_code not in [200,201]:
                        raise Exception(f"[CONSUMER] Failed to send email to {draft.to_address}\n {response.text}")
                    
                    to_info = [
                        {
                            "display_name": draft.to_name.title(),
                            "identifier": draft.to_address,
                            "identifier_type": "EMAIL_ADDRESS"
                        }
                    ]
                    await update_db_emails(draft_email=draft, from_address=sender["sender_address"], tracking_id=response.json()["tracking_id"], to_info=to_info, unipile_account_id=sender["unipile_account_id"], db=db)
                    await db.commit()                

                    logger.info(f"[CONSUMER] Successfully sent email draft {draft.draft_type} to {draft.to_address}")

            else:
                logger.info(f"[CONSUMER] no email to send to {company_id}")
    except Exception as e:
        logger.error(f"[CONSUMER] Failed to process send_email_outreach task\n {str(e)}")

async def send_response(message):
    json_string = message.body.decode()
    message_body = json.loads(json_string)    

    try:
        async for db in get_session():
            sender = await get_sender(db=db)
            if sender is None:
                    raise Exception("No more email senders available")
            email_client = EmailClient()
            email_client.set_unipile_account_id(sender["unipile_account_id"])    
            email_to = message_body["to_address"]
            company_id = message_body["company_id"]
            response = email_client.send_email(
                subject=message_body["subject"],
                content=message_body["body"],
                name_from=message_body["from_name"],
                name_to=message_body["to_name"],
                email_to=email_to
            )
            if response.status_code not in [200,201]:
                raise Exception(response.text)
            else:
                stmt = update(Company).filter(Company.company_id==company_id).values(email_confirmation_status_id=7)
                await db.execute(stmt)
                await db.commit()
                logger.info(f"successfully responded the reply from {email_to}")
    except Exception as e:
        logger.error(f"failed to send response to reply email: {str(e)}")

#endregion