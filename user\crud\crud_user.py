from database.models import User, user_role
from database.models import System<PERSON><PERSON>
from crud.crud_base import CRUDBase
from schema.role_schema import Role
from schema.user_schema import AccountCreate, UserUpdate

from sqlalchemy.orm import Session
from typing import Optional
import uuid


class CRUDUser(CRUDBase[User, AccountCreate, UserUpdate]):
    # def get_by_username(self, db: SessionFactory, *, username: str) -> Optional[UserAccount]:
    #     return db.query(UserAccount).filter(UserAccount.username == username).first()

    def get_by_user_id(self, db: Session, *, user_id: uuid.UUID) -> Optional[User]:
        return db.query(User).filter(User.user_id == user_id).first()


    def is_active(self, db: Session, user: User) -> bool:
        return user.is_active


    def is_superadmin(self, db: Session, user: User) -> bool:
        is_sa = db.query(
                SystemRole
            ).join(
                user_role
            ).join(
                User
            ).filter(
                User.user_id == user.user_id
            ).first().role_id == Role.SUPERADMIN.value
        return is_sa


    def get_all_users(self, db: Session, page, page_limit):
        offset = (page - 1) * page_limit
        user_list = db.query(User).offset(offset).limit(page_limit).all()
        return user_list


    def delete_user(self, db: Session, user_id):
        # Remember to remove the table with foreign key first
        login_data_to_be_deleted = db.query(User).filter(User.user_id == user_id).first()
        db.delete(login_data_to_be_deleted)
        # Delete the real data
        self.remove(db=db, id=user_id)
        db.commit()


crud_user = CRUDUser(User)