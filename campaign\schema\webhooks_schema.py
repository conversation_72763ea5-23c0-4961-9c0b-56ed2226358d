from pydantic import BaseModel
from typing import List, Optional, Literal
from pydantic.networks import EmailStr


class WebhooksNewEmail(BaseModel):
    email_id: str = None
    account_id: str = None
    event: str = None
    webhook_name: str = None
    date: str = None
    from_attendee: dict = None
    to_attendees: List[dict] = None
    bcc_attendees: List = None
    cc_attendees: List = None
    reply_to_attendees: List = None
    provider_id: str = None
    message_id: str = None
    has_attachments: bool = False
    subject: str = None
    body: str = None
    body_plain: str = None
    attachments: List = None
    folders: List = None
    role: str = None
    read_date: str = None
    is_complete: bool = False
    in_reply_to: Optional[dict] = None
    tracking_id: str = None
    origin: str = None    

    class Config:
        orm_mode = True    

class WebhooksEmailTracking(BaseModel):
    event: Optional[str]
    event_id: Optional[str]
    tracking_id: Optional[str]
    date: Optional[str]
    email_id: Optional[str]
    account_id: Optional[str]
    ip: Optional[str]
    user_agent: Optional[str]
    url: Optional[str]
    label: Optional[str]
    custom_domain: Optional[str]    

    class Config:
        orm_mode = True    


class WebhooksNewMessage(BaseModel):
    account_id: str
    account_type: Literal["LINKEDIN"]
    account_info: dict
    event: Literal[
        "message_reaction",
        "message_read",
        "message_received",
        "message_edited",
        "message_deleted"
    ]
    chat_id: str
    timestamp: str
    webhook_name: str
    message_id: str
    message: Optional[str] = None
    sender: dict
    attendees: List[dict]
    attachments: Optional[dict] = None
    reaction: Optional[str] = None
    reaction_sender: Optional[dict] = None
    
    class Config:
        orm_mode = True    