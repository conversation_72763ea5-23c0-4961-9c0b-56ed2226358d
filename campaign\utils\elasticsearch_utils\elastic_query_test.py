from elasticsearch import Elasticsearch
from typing import List
from loguru import logger
from message_bus.rpc_client import RPCClient
import json
import re


def test_string_matching_search(
    es_client: Elasticsearch, 
    query: dict
):
    search_result = es_client.search(index='test-index', body=query).body['hits']['hits']
    result = [item['_source'] for item in search_result]

    # Filter out duplicates based on 'Company Linkedin Url'
    unique_results = []
    seen_urls = []

    for item in result:
        company_linkedin_url = item['Company Linkedin Url']
        if company_linkedin_url not in seen_urls:
            unique_results.append(item)
            seen_urls.append(company_linkedin_url)
            
    return unique_results

def string_matching_search(
    es_client: Elasticsearch, 
    industry_list: list[str], 
    search_size: int,
    existing_company_urls: list[str] = [],
    location: str = None, 
):
    query = {
        "query": {
            "bool": {
                "must": [
                    {
                        "bool": {
                            "should": [
                                {
                                    "match": {
                                        "Company Industry": {
                                            "query": term.lower(),
                                            "fuzziness": "AUTO",
                                            "analyzer": "stop",
                                            "operator": "or",
                                            "minimum_should_match": 2
                                        }
                                    }
                                } for term in industry_list
                            ],
                            "minimum_should_match": 1
                        }
                    },
                ],
                "must_not": [
                    {
                        "match_phrase": {
                            "Company Linkedin Url": url
                        }
                    } for url in existing_company_urls
                ]
            }
        },
        "size": search_size,
        "_source": {
            "excludes": [
                "Company Industry embedding",
                "Company Location Name embedding",
                "Company Location Continent embedding"
            ]
        }
    }

    if location is not None:
        minimum_should_match = 1
        # tokens = re.findall(r'\b\w+\b', location)
        # if len(tokens) > 3:
        #     minimum_should_match = round(0.666 * len(tokens))

        location_query = {
            "bool": {
                "should": [
                    {
                        "match": {
                            field: {
                                "query": location.lower(),
                                "fuzziness": "AUTO",
                                "analyzer": "english",
                                "operator": "or"
                                # "minimum_should_match": minimum_should_match
                            }
                        }
                    } for field in [
                        "Company Location Locality",
                        "Company Location Region",
                        "Company Location Country",
                        "Company Location Continent"
                    ]
                ],
                "minimum_should_match": 1
            }
        }        
        query["query"]["bool"]["must"].append(location_query)

    search_result = es_client.search(index='test-index', body=query).body['hits']['hits']
    result = [item['_source'] for item in search_result]

    # Filter out duplicates based on 'Company Linkedin Url'
    unique_results = []
    seen_urls = []

    for item in result:
        company_linkedin_url = item['Company Linkedin Url']
        if company_linkedin_url not in seen_urls:
            unique_results.append(item)
            seen_urls.append(company_linkedin_url)
            
    logger.info(f"Found {len(unique_results)} companies from STRING MATCHING")
        
    return unique_results


def hybrid_search(
    es_client: Elasticsearch,
    industry_list: List[str],
    # industry_list_embedding: List[List[float]],
    location_embedding: List[float],
    existing_company_urls: List[str] = [],
    location: str = None,
    search_size: int = 100,
    # industry_similarity_threshold: float = 0.4,
    location_similarity_threshold: float = 0.4,
    # industry_weight: float = 0.5,
    # location_weight: float = 0.5
):
    
    query = {
        "size": search_size,
        "min_score": 0.001,
        "query": {
            "function_score": {
                "query": {
                    "bool": {
                        "must": [
                            {
                                "bool": {
                                    "should": [
                                        {
                                            "match": {
                                                "Company Industry": {
                                                    "query": term.lower(),
                                                    "fuzziness": "AUTO",
                                                    "analyzer": "stop",
                                                    "operator": "or",
                                                    "minimum_should_match": 2
                                                }
                                            }
                                        } for term in industry_list
                                    ],
                                    "minimum_should_match": 1
                                }
                            }
                        ],
                        "must_not": [
                            {
                                "match_phrase": {
                                    "Company Linkedin Url": url
                                }
                            } for url in existing_company_urls
                        ]
                    }
                }
            }
        },
        "_source": {
            "excludes": [
                "Company Industry embedding",
                "Company Location Name embedding",
                "Company Location Continent embedding"
            ]
        }
    }

    if location is not None:
        script_score = {
            "script": {
                "source": """
                    double loc1 = cosineSimilarity(params.location_vector, 'Company Location Name embedding');
                    double loc2 = cosineSimilarity(params.location_vector, 'Company Location Continent embedding');
                    double max_location_sim = Math.max(loc1, loc2);

                    if (max_location_sim >= params.location_threshold) {
                        return params.location_weight * max_location_sim;
                    } else {
                        return 0.0;
                    }
                """,
                "params": {
                    "location_vector": location_embedding,
                    "location_threshold": location_similarity_threshold,
                    "location_weight": 1.0
                }
            }
        }
        boost_mode = "multiply"
        query["query"]["function_score"]["script_score"] = script_score
        query["query"]["function_score"]["boost_mode"] = boost_mode



    search_result = es_client.search(index='test-index', body=query).body['hits']['hits']
    
    # Log the scores for debugging
    for hit in search_result:
        logger.debug(f"Score: {hit['_score']:.3f} | Company: {hit['_source']['Company Name']}")
    
    result = [item['_source'] for item in search_result]
    # Filter out duplicates based on 'Company Linkedin Url'
    unique_results = []
    seen_urls = []

    for item in result:
        company_linkedin_url = item['Company Linkedin Url']
        if company_linkedin_url not in seen_urls:
            unique_results.append(item)
            seen_urls.append(company_linkedin_url)

    logger.info(f"Found {len(unique_results)} companies from HYBRID SEARCH")
    return unique_results

def semantic_search(
    es_client: Elasticsearch,
    # industry_list: List[str],
    industry_list_embedding: List[List[float]],
    location_embedding: List[float],
    existing_company_urls: List[str] = [],
    location: str = None,
    search_size: int = 100,
    industry_similarity_threshold: float = 0.4,
    location_similarity_threshold: float = 0.4,
    industry_weight: float = 0.5,
    location_weight: float = 0.5
):
    # Make sure all vectors are lists
    industry_vectors = [vec if isinstance(vec, list) else vec.tolist() for vec in industry_list_embedding]

    if location is None:
        location_similarity_threshold = 0.0
        location_weight = 0.0
        industry_weight = 1.0

    query = {
        "size": search_size,
        "min_score": 0.001, 
        "query": {
            "function_score": {
                "query": {
                    "bool": {
                        "must_not": [
                            {
                                "match_phrase": {
                                    "Company Linkedin Url": url
                                }
                            } for url in existing_company_urls
                        ]
                    }
                },
                "script_score": {
                    "script": {
                        "source": """
                            double max_industry_sim = -2.0;
                            for (v in params.industry_vectors) {
                                double sim = cosineSimilarity(v, 'Company Industry embedding');
                                if (sim > max_industry_sim) {
                                    max_industry_sim = sim;
                                }
                            }

                            double loc1 = cosineSimilarity(params.location_vector, 'Company Location Name embedding');
                            double loc2 = cosineSimilarity(params.location_vector, 'Company Location Continent embedding');
                            double max_location_sim = Math.max(loc1, loc2);

                            boolean industry_match = max_industry_sim >= params.industry_threshold;
                            boolean location_match = max_location_sim >= params.location_threshold;

                            if (industry_match && location_match) {
                                return (params.industry_weight * max_industry_sim) + 
                                    (params.location_weight * max_location_sim);
                            } else {
                                return 0.0;
                            }
                        """,
                        "params": {
                            "industry_vectors": industry_vectors,
                            "location_vector": location_embedding,
                            "industry_threshold": industry_similarity_threshold,
                            "location_threshold": location_similarity_threshold,
                            "industry_weight": industry_weight,
                            "location_weight": location_weight
                        }
                    }
                },
                "boost_mode": "replace"
            }
        },
        "_source": {
            "excludes": [
                "Company Industry embedding",
                "Company Location Name embedding",
                "Company Location Continent embedding"
            ]
        }
    }
    
    search_result = es_client.search(index='test-index', body=query).body['hits']['hits']
    
    # Log the scores for debugging
    for hit in search_result:
        logger.debug(f"Score: {hit['_score']:.3f} | Company: {hit['_source']['Company Name']}")
    
    result = [item['_source'] for item in search_result]
    # Filter out duplicates based on 'Company Linkedin Url'
    unique_results = []
    seen_urls = []

    for item in result:
        company_linkedin_url = item['Company Linkedin Url']
        if company_linkedin_url not in seen_urls:
            unique_results.append(item)
            seen_urls.append(company_linkedin_url)

    logger.info(f"Found {len(unique_results)} companies from SEMANTIC SEARCH")
    return unique_results


async def search_matching_companies(
    es_client: Elasticsearch,
    embedding_dim: int,
    industry_list: list[str], 
    location: str,
    existing_company_urls: List[str] = [],
    search_size: int = 100,
    industry_similarity_threshold: float = 0.4,
    location_similarity_threshold: float = 0.4,
    industry_weight: float = 0.5,
    location_weight: float = 0.5
):
    
    if location.lower().strip() in ["global", "worldwide", "international", "world", "everywhere", ""]:
        location = None

    temp_size = search_size * 2
    string_search_results = string_matching_search(
        es_client=es_client, 
        industry_list=industry_list, 
        location=location, 
        existing_company_urls=existing_company_urls, 
        search_size=temp_size
    )

    search_results = string_search_results

    if len(search_results) < search_size:
        existing_company_urls = existing_company_urls + [item['Company Linkedin Url'] for item in string_search_results]
        rpc = await RPCClient().connect()
        function_name = "get_text_embedding_test"
        texts_to_embed = industry_list + ["global"]
        texts_to_embed = [text_.lower() for text_ in texts_to_embed]

        response = await rpc.call(service="aiengine.*", function_name=function_name, params={"text_list": texts_to_embed, "embedding_dim": embedding_dim})
        response_body = json.loads(response)
        result = response_body.get("result")

        if result == "error":
            logger.error(
                "Error from RPC server: " + response_body.get("detail")
            )
        else:
            industry_list_embedding = result["embeddings_list"][:-1]
            location_embedding = result["embeddings_list"][-1]            
            hybrid_search_results = hybrid_search(
                es_client=es_client, 
                industry_list=industry_list,
                location=location,
                location_embedding=location_embedding,
                location_similarity_threshold=location_similarity_threshold,
                search_size=temp_size,
                existing_company_urls=existing_company_urls
            )
            for item in hybrid_search_results:
                if len(search_results) == search_size:
                    break
                search_results.append(item)
            
            if len(search_results) < search_size:
                existing_company_urls = existing_company_urls + [item['Company Linkedin Url'] for item in hybrid_search_results]
                semantic_search_results = semantic_search(
                    es_client=es_client, 
                    location=location,
                    industry_list_embedding=industry_list_embedding,
                    location_embedding=location_embedding,
                    location_similarity_threshold=location_similarity_threshold,
                    industry_similarity_threshold=industry_similarity_threshold,
                    search_size=temp_size,
                    existing_company_urls=existing_company_urls,
                    industry_weight=industry_weight,
                    location_weight=location_weight
                )
                for item in semantic_search_results:
                    if len(search_results) == search_size:
                        break
                    search_results.append(item)

    if len(search_results) > search_size:
        search_results = search_results[:search_size]

    return search_results


def search_primary_rep(es_client: Elasticsearch, company_linkedin_address: str):
    top_brass = ['chief executive officer', 'chief operating officer', 'chief marketing officer']
    subordinate = ['president','manager','director']
    cc_list = top_brass + subordinate

    query = {
        "query": {
            "bool": {
                "must": [
                    {
                        "match_phrase": {
                            "Company Linkedin Url": company_linkedin_address
                        }
                    }
                ],
                "should" :[
                    {
                        "match_phrase": {
                            "Job title": term
                        }
                    } for term in top_brass
                ]
            }
        },
        "size": 1
    }

    search_result = es_client.search(index='test-index', body=query).body['hits']['hits']
    result = [item['_source'] for item in search_result]

    # Filter out duplicates based on 'Company Linkedin Url'
    unique_results = [] + result

    if len(unique_results) < 1:
        query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "match_phrase": {
                                "Company Linkedin Url": company_linkedin_address
                            }
                        }
                    ],
                    "should" :[
                        {
                            "match_phrase": {
                                "Job title": term
                            }
                        } for term in subordinate
                    ]
                }
            },
            "size": 1
        }
        search_result = es_client.search(index='test-index', body=query).body['hits']['hits']
        result = [item['_source'] for item in search_result]    

        unique_results += result
            
    return unique_results