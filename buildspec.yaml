version: 0.2

env:
  parameter-store:
    AWS_ACCESS_KEY_ID: /production/infra/codebuild-env/ACCESS_KEY
    AWS_SECRET_ACCESS_KEY: /production/infra/codebuild-env/SECRET_KEY
    AWS_DEFAULT_REGION: /production/infra/codebuild-env/AWS_REGION
  variables:
    ECR_REPO_URL: "068530292583.dkr.ecr.ap-south-1.amazonaws.com/production-roboman-repo"
    IMAGE_TAG: "latest"
    ENV: "production"

phases:
  install:
    commands:
      - echo "Installing dependencies"
      - aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin 068530292583.dkr.ecr.ap-south-1.amazonaws.com
      - echo "Installing Helm and kubectl if necessary"
      - curl -sL https://get.helm.sh/helm-v3.10.3-linux-amd64.tar.gz | tar -xz -C /usr/local/bin --strip-components=1 linux-amd64/helm
      - curl -sL https://s3.us-west-2.amazonaws.com/amazon-eks/1.21.2/2021-07-05/bin/linux/amd64/kubectl -o /usr/local/bin/kubectl
      - chmod +x /usr/local/bin/kubectl
      - echo "Setting up Git Credentials Helper for CodeCommit"
      - git config --global credential.helper '!aws codecommit credential-helper $@'
      - git config --global credential.UseHttpPath true

  pre_build:
    commands:
      - echo "Building and Pushing Docker images"
      - |
        CHANGES=$(git diff --name-only HEAD^ HEAD | awk -F/ '{print $1}' | sort -u | grep '^frontend')
        if [ -z "$CHANGES" ]; then
          echo "No changes in the frontend directory. Skipping build."
        else
          cd frontend && aws ssm get-parameter --name /production/roboman/frontend/app_env --with-decryption --output text --query Parameter.Value > .env
          docker build -t $ECR_REPO_URL:frontend-$IMAGE_TAG . && cd ..
          docker tag $ECR_REPO_URL:frontend-$IMAGE_TAG $ECR_REPO_URL:frontend-$CODEBUILD_RESOLVED_SOURCE_VERSION
        fi
        CHANGES=$(git diff --name-only HEAD^ HEAD | awk -F/ '{print $1}' | sort -u | grep '^aiengine')
        if [ -z "$CHANGES" ]; then
          echo "No changes in the aiengine directory. Skipping build."
        else
          cd aiengine && aws ssm get-parameter --name /production/roboman/aiengine/app_env --with-decryption --output text --query Parameter.Value > .env
          docker build -t $ECR_REPO_URL:aiengine-$IMAGE_TAG . && cd ..
          docker tag $ECR_REPO_URL:aiengine-$IMAGE_TAG $ECR_REPO_URL:aiengine-$CODEBUILD_RESOLVED_SOURCE_VERSION
        fi
        CHANGES=$(git diff --name-only HEAD^ HEAD | awk -F/ '{print $1}' | sort -u | grep '^campaign')
        if [ -z "$CHANGES" ]; then
          echo "No changes in the campaign directory. Skipping build."
        else
          cd campaign && aws ssm get-parameter --name /production/roboman/campaign/app_env --with-decryption --output text --query Parameter.Value > .env
          docker build -t $ECR_REPO_URL:campaign-$IMAGE_TAG . && cd ..
          docker tag $ECR_REPO_URL:campaign-$IMAGE_TAG $ECR_REPO_URL:campaign-$CODEBUILD_RESOLVED_SOURCE_VERSION
        fi
        CHANGES=$(git diff --name-only HEAD^ HEAD | awk -F/ '{print $1}' | sort -u | grep '^gateway')
        if [ -z "$CHANGES" ]; then
          echo "No changes in the gateway directory. Skipping build."
        else
          cd gateway && aws ssm get-parameter --name /production/roboman/gateway/app_env --with-decryption --output text --query Parameter.Value > .env
          docker build -t $ECR_REPO_URL:gateway-$IMAGE_TAG . && cd ..
          docker tag $ECR_REPO_URL:gateway-$IMAGE_TAG $ECR_REPO_URL:gateway-$CODEBUILD_RESOLVED_SOURCE_VERSION
        fi
        CHANGES=$(git diff --name-only HEAD^ HEAD | awk -F/ '{print $1}' | sort -u | grep '^user')
        if [ -z "$CHANGES" ]; then
          echo "No changes in the user directory. Skipping build."
        else
          cd user && aws ssm get-parameter --name /production/roboman/user/app_env --with-decryption --output text --query Parameter.Value > .env
          docker build -t $ECR_REPO_URL:user-$IMAGE_TAG . && cd ..
          docker tag $ECR_REPO_URL:user-$IMAGE_TAG $ECR_REPO_URL:user-$CODEBUILD_RESOLVED_SOURCE_VERSION
        fi

  post_build:
    commands:
      - echo "Pushing Docker images & Deploy"
      - |
        CHANGES=$(git diff --name-only HEAD^ HEAD | awk -F/ '{print $1}' | sort -u | grep '^frontend')
        if [ -z "$CHANGES" ]; then
          echo "No changes in the directory. Skipping build."
        else
          docker push $ECR_REPO_URL:frontend-$CODEBUILD_RESOLVED_SOURCE_VERSION
          echo "Setup Prepare for Deploy"
          if [ ! -d "infra/helm-chart" ]; then
            git clone --branch production https://git-codecommit.ap-south-1.amazonaws.com/v1/repos/infrastructure
          fi
          aws eks update-kubeconfig --name production-roboman-cluster
          cd infrastructure/helm-chart
          export MODULE_NAME=frontend
          sed "s/MODULE_NAME/${MODULE_NAME}/g" RootChart.yaml > Chart.yaml
          helm upgrade --install $MODULE_NAME . --namespace $ENV --create-namespace -f $ENV/$MODULE_NAME.yaml --set image.tag=frontend-$CODEBUILD_RESOLVED_SOURCE_VERSION
          cd ../..
        fi
        CHANGES=$(git diff --name-only HEAD^ HEAD | awk -F/ '{print $1}' | sort -u | grep '^aiengine')
        if [ -z "$CHANGES" ]; then
          echo "No changes in the directory. Skipping build."
        else
          docker push $ECR_REPO_URL:aiengine-$CODEBUILD_RESOLVED_SOURCE_VERSION
          echo "Setup Prepare for Deploy"
          if [ ! -d "infra/helm-chart" ]; then
            git clone --branch production https://git-codecommit.ap-south-1.amazonaws.com/v1/repos/infrastructure
          fi
          aws eks update-kubeconfig --name production-roboman-cluster
          cd infrastructure/helm-chart
          export MODULE_NAME=aiengine
          sed "s/MODULE_NAME/${MODULE_NAME}/g" RootChart.yaml > Chart.yaml
          helm upgrade --install $MODULE_NAME . --namespace $ENV --create-namespace -f $ENV/$MODULE_NAME.yaml --set image.tag=aiengine-$CODEBUILD_RESOLVED_SOURCE_VERSION
          cd ../..
        fi
        CHANGES=$(git diff --name-only HEAD^ HEAD | awk -F/ '{print $1}' | sort -u | grep '^campaign')
        if [ -z "$CHANGES" ]; then
          echo "No changes in the directory. Skipping build."
        else
          docker push $ECR_REPO_URL:campaign-$CODEBUILD_RESOLVED_SOURCE_VERSION
          echo "Setup Prepare for Deploy"
          if [ ! -d "infra/helm-chart" ]; then
            git clone --branch production https://git-codecommit.ap-south-1.amazonaws.com/v1/repos/infrastructure
          fi
          aws eks update-kubeconfig --name production-roboman-cluster
          cd infrastructure/helm-chart
          export MODULE_NAME=campaign
          sed "s/MODULE_NAME/${MODULE_NAME}/g" RootChart.yaml > Chart.yaml
          helm upgrade --install $MODULE_NAME . --namespace $ENV --create-namespace -f $ENV/$MODULE_NAME.yaml --set image.tag=campaign-$CODEBUILD_RESOLVED_SOURCE_VERSION
          cd ../..
        fi
        CHANGES=$(git diff --name-only HEAD^ HEAD | awk -F/ '{print $1}' | sort -u | grep '^gateway')
        if [ -z "$CHANGES" ]; then
          echo "No changes in the directory. Skipping build."
        else
          docker push $ECR_REPO_URL:gateway-$CODEBUILD_RESOLVED_SOURCE_VERSION
          echo "Setup Prepare for Deploy"
          if [ ! -d "infra/helm-chart" ]; then
            git clone --branch production https://git-codecommit.ap-south-1.amazonaws.com/v1/repos/infrastructure
          fi
          aws eks update-kubeconfig --name production-roboman-cluster
          cd infrastructure/helm-chart
          export MODULE_NAME=gateway
          sed "s/MODULE_NAME/${MODULE_NAME}/g" RootChart.yaml > Chart.yaml
          helm upgrade --install $MODULE_NAME . --namespace $ENV --create-namespace -f $ENV/$MODULE_NAME.yaml --set image.tag=gateway-$CODEBUILD_RESOLVED_SOURCE_VERSION
          cd ../..
        fi
        CHANGES=$(git diff --name-only HEAD^ HEAD | awk -F/ '{print $1}' | sort -u | grep '^user')
        if [ -z "$CHANGES" ]; then
          echo "No changes in the directory. Skipping build."
        else
          docker push $ECR_REPO_URL:user-$CODEBUILD_RESOLVED_SOURCE_VERSION
          echo "Setup Prepare for Deploy"
          if [ ! -d "infra/helm-chart" ]; then
            git clone --branch production https://git-codecommit.ap-south-1.amazonaws.com/v1/repos/infrastructure
          fi
          aws eks update-kubeconfig --name production-roboman-cluster
          cd infrastructure/helm-chart
          export MODULE_NAME=user
          sed "s/MODULE_NAME/${MODULE_NAME}/g" RootChart.yaml > Chart.yaml
          helm upgrade --install $MODULE_NAME . --namespace $ENV --create-namespace -f $ENV/$MODULE_NAME.yaml --set image.tag=user-$CODEBUILD_RESOLVED_SOURCE_VERSION
          cd ../..
        fi

artifacts:
  files:
    - "**/*"
  discard-paths: yes
