from pydantic import BaseModel, Field
from typing import List, Optional
from pydantic.networks import EmailStr

class EmailGeneration(BaseModel):
    company_id_list: List[str]
    campaign_id: str
    prompt_name: Optional[str] = None

class EmailSend(BaseModel):
    company_id_list: List[str]   

class CreateDraftEmail(BaseModel):
    company_id: str
    draft_type: int = 0
    from_address: EmailStr = "<EMAIL>"
    from_name: str
    to_address: EmailStr
    to_name: str
    subject: str
    body: str
    body_plain: str

    class Config:
        orm_mode = True

class UpdateDraftEmail(BaseModel):
    company_id: str
    draft_id: str
    subject: Optional[str] = None
    body: Optional[str] = None

class EmailOutreach(BaseModel):
    campaign_id : str
    company_id_list: List[str]
    prompt_name: Optional[str] = None

class StartAutoOutreach(BaseModel):
    prompt_name: Optional[str] = None

class SendEmailRequest(BaseModel):
    company_id_list: List[str]    

class SendLinkedInRequest(BaseModel):
    company_id_list: List[str]  

class RespondEmail(BaseModel):
    company_id: str
    subject: str
    body: str

class RespondMessage(BaseModel):
    company_id: str
    message_content: str