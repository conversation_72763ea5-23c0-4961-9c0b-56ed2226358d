from schema.conversation_chatmessage_schema import ChatMessageCreate
from crud.crud_chat import get_lastest_messages
from core.config import settings

from sqlalchemy.ext.asyncio import AsyncSession
import aiohttp
import async_timeout
import asyncio
from loguru import logger


async def create_request_data(
	db: AsyncSession,
	message_data: ChatMessageCreate,
):	
	history = await get_lastest_messages(db, message_data.conversation_id)

	if not history:
		history = []

	input_data = {
		"request_message": message_data.message_content,
		"model_id": message_data.model_id,
		"history": history,
		"conversation_id": message_data.conversation_id,
	}

	return input_data


async def streaming(
	user_id: str,
	url: str,
	method: str,
	data: dict = None,
):
	headers = {'request-user-id': user_id}

	if not data:
		data = {}

	with async_timeout.timeout(settings.TIMEOUT):
		async with aiohttp.ClientSession() as session:
			request = getattr(session, method)
			async with request(url, json=data, headers=headers) as response:
				try:
					async for data in response.content.iter_any():
						yield data
						await asyncio.sleep(0.01)
				except asyncio.CancelledError as e:
					logger.error('Canceled!!!!!')
					logger.error(f'{e}')