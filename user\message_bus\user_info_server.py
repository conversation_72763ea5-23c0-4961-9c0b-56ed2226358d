from database.database import SessionFactory
from database.models import User
from core.config import settings

from aio_pika.abc import AbstractIncomingMessage
from aio_pika import Message, connect
from typing import List
from loguru import logger
import logging
import json


def process_data(data: str) -> List[dict]:
    usage_dict = json.loads(data)
    user_ids = [data.get('user_id') for data in usage_dict]

    with SessionFactory() as db:
        user_login_data = db.query(
            User.user_id.label("user_id"),
            User.email
        ).where(
            User.user_id.in_(user_ids))
        
    user_dict = [
        {
            "user_id": str(data.user_id),
            "email": str(data.email)
        } for data in user_login_data]
    
    combined_dict = []
    usage_lookup = {usage['user_id']: usage for usage in usage_dict}

    for user in user_dict:
        user_id = user['user_id']
        if user_id in usage_lookup:
            combined_data = {**user, **usage_lookup[user_id]}
        else:
            combined_data = user
        combined_dict.append(combined_data)

    sorted_combined_dict = sorted(combined_dict, key=lambda x: x['total_cost'], reverse=True)

    return sorted_combined_dict


async def user_rpc_server() -> None:
    # Perform connection
    connection = await connect(f"amqp://{settings.RABBITMQ_DEFAULT_USER}:{settings.RABBITMQ_DEFAULT_PASS}@{settings.RABBITMQ_HOST}:{settings.RABBITMQ_PORT}/")

    # Creating a channel
    channel = await connection.channel()
    exchange = channel.default_exchange

    # Declaring queue
    queue = await channel.declare_queue("UserInformation")

    logger.info(" [x] Awaiting RPC requests")

    # Start listening the queue with name 'hello'
    async with queue.iterator() as qiterator:
        message: AbstractIncomingMessage
        async for message in qiterator:
            try:
                async with message.process(requeue=False):
                    assert message.reply_to is not None

                    logger.info(f" [.] data recieved({message.body.decode()})")
                    response = process_data(message.body.decode())
                    message_body = json.dumps(response)

                    await exchange.publish(
                        Message(
                            body=message_body.encode('utf-8'),
                            correlation_id=message.correlation_id,
                        ),
                        routing_key=message.reply_to,
                    )
                    logger.info("Request complete")
            except Exception:
                logging.exception("Processing error for message %r", message)