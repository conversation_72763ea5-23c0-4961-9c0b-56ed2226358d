from functools import lru_cache
import secrets
from typing import List
import os
from dotenv import load_dotenv
from pydantic import AnyHttpUrl, validator, BaseSettings
import json


load_dotenv(".env")

try:
    with open("./database/system_data/user.json", 'r') as file:
        user_dict = json.load(file)
except:
    user_dict = None

class Settings(BaseSettings):
    # SERVER_NAME: str
    # SERVER_HOST: str = "digitalocean.com"
    SERVER_PORT: int = 8000
    
    API_V2_STR: str = ""
    SECRET_KEY: str = secrets.token_urlsafe(32)

    # 60 minutes * 24 hours * 8 days = 8 days
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8

    # 60 seconds * 60 minutes * 6 hours = 6 hours
    KEY_CACHE_EXPIRE_SECONDS: int = 60 * 60 * 6

    BACKEND_CORS_ORIGINS: list = ["*"]
    
    # Reids client keys
    AIENGINE_CACHE_KEY: str = "message_data:json_struct"
    CHAT_CACHE_KEY: str = "usage_data:json_struct"

    PROJECT_NAME: str

    DATABASE_NAME: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    DATABASE_HOST: str
    DATABASE_PORT: str

    RABBITMQ_DEFAULT_USER: str
    RABBITMQ_DEFAULT_PASS: str
    RABBITMQ_HOST: str
    RABBITMQ_PORT: int
    EXCHANGE_NAME: str
    QUEUE_NAME: str

    MJ_APIKEY_PUBLIC: str 
    MJ_APIKEY_PRIVATE: str

    JWT_SECRET: str
    JWT_ALGORITHM: str
    TIMEOUT: int = 99999999
    
    AI_ENGINE_URL = "http://aiengine-svc/api/llm/stream/default"

    #AWS
    # USER_SERVICE_URL = "http://user-svc"
    # AI_SERVICE_URL = "http://aiengine-svc"

    # local devevlopment
    USER_SERVICE_URL = "http://roboman-user:8000"
    AI_SERVICE_URL = "http://roboman-aiservice:8000"  

    ES_PASSWORD : str
    ES_USERNAME : str
    ES_HOST : str
    ES_INDEX_NAME: str = "roboman-test-index"

    CHROME_DRIVER_HOST = "chrome-driver.infrastructure.svc.cluster.local"
    CHROME_DRIVER_PORT = "4444"

    #AWS S3
    S3_ACCESS_ID : str
    S3_SECRET_KEY : str
    S3_BUCKET_NAME : str = 'roboman-elastic-search'    

    UNIPILE_SUBDOMAIN : str
    UNIPILE_PORT : str
    UNIPILE_API_KEY : str


    HUMANTIC_AI_API_KEY: str
    HUMANTIC_AI_BASE_URL: str
    class Config:
        env_file = ".env"
        case_sensitive = True

@lru_cache()
def get_settings():
    return Settings()

settings = get_settings()