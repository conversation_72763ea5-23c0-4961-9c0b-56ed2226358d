# API Endpoint: Get Matching Companies V2
---
## Description

This endpoint allows users to retrieve a list of companies that match specific campaign details, primarily based on industry. It leverages Elasticsearch to search for companies matching the provided criteria and returns a list of company details including company name, industry, LinkedIn URL, representative name, and representative email.

## HTTP Request

```
Method : POST
URL : https://roboman-gateway.intuicon.ai/api/campaign/companies/match/v2
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
Content-Type: application/json
```

## Request Body

The request body must be a JSON object containing the following fields:

```
campaign_name (string, required): The name of the campaign.
core_service (string, required): The core service or product of the campaign.
unique_selling_proposition (string, required): The unique selling proposition of the campaign.
target_audience (string, required): The target audience for the campaign.
problem_solved (string, required): The problem that the campaign's product or service solves.
key_benefits (array of strings, required): The key benefits of the product or service.
primary_goal_of_outreach_campaign (string, required): The primary goal of the outreach campaign.
ideal_client (array of strings, required): The ideal clients for the campaign.
success_measurement (string, required): How success will be measured for the campaign.
industry (array of strings, required): The industry or industries the campaign is targeting.
location (string, required): The geographical location targeted by the campaign.
```

### Example Request Body

```
{
  "campaign_name": "Example Campaign",
  "core_service": "Software Development",
  "unique_selling_proposition": "Custom software solutions tailored to your needs.",
  "target_audience": "Small and medium-sized businesses",
  "problem_solved": "Lack of efficient software solutions.",
  "key_benefits": [
    "Efficiency",
    "Scalability",
    "Customization"
  ],
  "primary_goal_of_outreach_campaign": "Generate leads",
  "ideal_client": [
    "Tech startups",
    "Retail businesses"
  ],
  "success_measurement": "Number of leads generated",
  "industry": [
    "Technology"
  ],
  "location": "US"
}
```

## Response Body

On successful request, the API returns a JSON array of company objects. Each object contains the following fields:

```
[
  {
    "company_name": "Company Name",
    "industry": "Company Industry",
    "company_linkedin": "Company Linkedin Url",
    "rep_name": "Full name",
    "rep_email": "representative email"
  },
  {
    "company_name": "Company Name",
    "industry": "Company Industry",
    "company_linkedin": "Company Linkedin Url",
    "rep_name": "Full name",
    "rep_email": "representative email"
  }  
]
```

### Example Response Body

```
[
  {
    "company_name": "Acme Corp",
    "industry": "Technology",
    "company_linkedin": "https://www.linkedin.com/company/acme-corp",
    "rep_name": "John Doe",
    "rep_email": "<EMAIL>"
  },
  {
    "company_name": "Beta Inc",
    "industry": "Technology",
    "company_linkedin": "https://www.linkedin.com/company/beta-inc",
    "rep_name": "Jane Smith",
    "rep_email": "<EMAIL>"
  }
]
```

## Example cURL Command

```
curl -X 'POST' \
  'https://roboman-gateway.intuicon.ai/api/campaign/companies/match/v2' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_access_token>' \
  -H 'Content-Type: application/json' \
  -d '{
    "campaign_name": "Example Campaign",
    "core_service": "Software Development",
    "unique_selling_proposition": "Custom software solutions tailored to your needs.",
    "target_audience": "Small and medium-sized businesses",
    "problem_solved": "Lack of efficient software solutions.",
    "key_benefits": [
      "Efficiency",
      "Scalability",
      "Customization"
    ],
    "primary_goal_of_outreach_campaign": "Generate leads",
    "ideal_client": [
      "Tech startups",
      "Retail businesses"
    ],
    "success_measurement": "Number of leads generated",
    "industry": [
      "Technology"
    ],
    "location": "US"
  }'
```
---
# API Endpoint: Get Personal Campaigns
---
## Description

This endpoint retrieves a list of campaigns associated with the authenticated user. It returns detailed information for each campaign, including its settings and statistics on prospects, sent emails, opened emails, and replies.

## HTTP Request

```
Method : GET
URL : https://roboman-gateway.intuicon.ai/api/campaign/personal
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request-user-id: string (required). User ID obtained from the authorization token.
```

## Request Body

This endpoint does not require a request body.

## Response Body

On successful request, the API returns a JSON array of campaign objects. Each object contains campaign details and statistics.

```
[
  {
    "campaign_id": "campaign UUID",
    "campaign_name": "Campaign Name",
    "campaign_info": {
      "core_service": "...",
      "unique_selling_proposition": "...",
      "target_audience": "...",
      "problem_solved": "...",
      "key_benefits": [
        "...",
        "..."
      ],
      "primary_goal_of_outreach_campaign": "...",
      "ideal_client": [
        "...",
        "..."
      ],
      "success_measurement": "...",
      "industry": [
        "...",
        "..."
      ],
      "location": "...",
      "must_have_info": "..."
    },
    "email_format": "...",
    "linkedin_msg_format": "...",
    "user_id": "user UUID",
    "created_at": "timestamp",
    "updated_at": "timestamp",
    "stats": {
      "prospects_count": 100,
      "sent": {
        "count": 50,
        "rate": 0.5
      },
      "opened": {
        "count": 20,
        "rate": 0.4
      },
      "replied": {
        "count": 5,
        "rate": 0.1
      },
      "interested": {
        "count": 0,
        "rate": 0.0
      }
    }
  },
  ...
]
```

### Example Response Body

```
[
  {
    "campaign_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "campaign_name": "Summer Sales Campaign",
    "campaign_info": {
      "core_service": "Discounted Products",
      "unique_selling_proposition": "...",
      "target_audience": "Existing Customers",
      "problem_solved": "...",
      "key_benefits": [],
      "primary_goal_of_outreach_campaign": "Increase Sales",
      "ideal_client": [],
      "success_measurement": "Sales Volume",
      "industry": [],
      "location": "Global",
      "must_have_info": "..."
    },
    "email_format": "...",
    "linkedin_msg_format": "...",
    "user_id": "user UUID",
    "created_at": "2024-03-05T10:00:00",
    "updated_at": "2024-03-06T14:30:00",
    "stats": {
      "prospects_count": 50,
      "sent": {
        "count": 30,
        "rate": 0.6
      },
      "opened": {
        "count": 15,
        "rate": 0.5
      },
      "replied": {
        "count": 3,
        "rate": 0.1
      },
      "interested": {
        "count": 0,
        "rate": 0.0
      }
    }
  },
  {
    "campaign_id": "b2c3d4e5-f6a7-8901-2345-67890abcdef1",
    "campaign_name": "New Product Launch",
    "campaign_info": {
      "core_service": "Innovative Gadget",
      "unique_selling_proposition": "...",
      "target_audience": "Tech Enthusiasts",
      "problem_solved": "...",
      "key_benefits": [],
      "primary_goal_of_outreach_campaign": "Generate Awareness",
      "ideal_client": [],
      "success_measurement": "Website Visits",
      "industry": [],
      "location": "US",
      "must_have_info": "..."
    },
    "email_format": "...",
    "linkedin_msg_format": "...",
    "user_id": "user UUID",
    "created_at": "2024-04-01T12:00:00",
    "updated_at": "2024-04-02T16:45:00",
    "stats": {
      "prospects_count": 100,
      "sent": {
        "count": 70,
        "rate": 0.7
      },
      "opened": {
        "count": 40,
        "rate": 0.57
      },
      "replied": {
        "count": 10,
        "rate": 0.14
      },
      "interested": {
        "count": 0,
        "rate": 0.0
      }
    }
  }
]
```

## Example cURL Command

```
curl -X 'GET' \
  'https://roboman-gateway.intuicon.ai/api/campaign/personal' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer <your_access_token>' \
  -H 'request-user-id: <user_id>'
```
---

# API Endpoint: Retrieve Campaign Details
---
## Description

This endpoint allows authenticated users to retrieve detailed information about a specific campaign by its unique identifier. The response includes all campaign properties such as name, campaign information, contact details, email and LinkedIn message formats, and timestamps.

## HTTP Request

```
Method: GET
URL: https://roboman-gateway.intuicon.ai/api/campaign/{campaign_id}
```

## Path Parameters

```
campaign_id (UUID, required): The unique identifier of the campaign to retrieve.
```

## Headers

```
Accept: application/json
request-user-id: string (required). The ID of the requesting user for authentication.
```

## Response Body

On successful retrieval, the API returns a JSON object containing detailed information about the requested campaign.

```
{
  "campaign_id": "b011627c-5576-478a-8899-8f956730ace5",
  "campaign_name": "Product Launch Campaign",
  "campaign_info": {
    "core_service": "AI-powered sales automation",
    "unique_selling_proposition": "Increase sales efficiency by 40% with AI",
    "target_audience": "B2B SaaS companies",
    "problem_solved": "Time-consuming manual sales outreach",
    "key_benefits": ["Increased efficiency", "Higher conversion rates", "Personalized messaging"],
    "primary_goal_of_outreach_campaign": "Generate qualified leads",
    "ideal_client": ["Tech startups", "Enterprise SaaS"],
    "success_measurement": "20% increase in qualified leads",
    "industry": ["technology", "software"],
    "location": "United States"
  },
  "campaign_contact": {
    "must_have_info": "Include reference to AI capabilities"
  },
  "email_format": "Write an email of 300 words",
  "linkedin_msg_format": "Write a message of 100 words",
  "created_at": "2024-06-15T07:15:15.234406+00:00",
  "updated_at": "2024-06-15T07:15:15.234406+00:00",
  "user_id": "a8b7c6d5-e4f3-2g1h-0i9j-8k7l6m5n4o3p"
}
```

## Example cURL Command

```
curl -X 'GET' \
'https://roboman-gateway.intuicon.ai/api/campaign/b011627c-5576-478a-8899-8f956730ace5' \
-H 'accept: application/json' \
-H 'request-user-id: a8b7c6d5-e4f3-2g1h-0i9j-8k7l6m5n4o3p'
```

## Response Codes

```
200 OK: The campaign was successfully retrieved
404 Not Found: The campaign with the specified ID does not exist
401 Unauthorized: Missing or invalid request-user-id header
500 Internal Server Error: An unexpected error occurred on the server
```
---

# API Endpoint: Get User Campaign Status
---
## Description

This endpoint allows authenticated users to retrieve a summary of their campaign statuses. It provides an overview of the number of campaigns, total companies, and a breakdown of email confirmation statuses across all campaigns associated with the user.

## HTTP Request

```
Method: GET
URL: https://roboman-gateway.intuicon.ai/api/campaign/status/
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request-user-id: string (required). User ID obtained from the authorization token.
```

## Request Body

This endpoint does not require a request body.

## Response Body

On successful retrieval, the API returns a JSON object containing campaign statistics and email status counts.

```
{
    "num_of_campaigns": 2,
    "num_of_companies": 150,
    "email_status_count": {
        "not started": 45,
        "waiting to review": 30,
        "reviewed": 25,
        "sent": 50,
        "opened": 35,
        "replied": 20
    }
}
```

### Response Fields

- `num_of_campaigns` (integer): Total number of campaigns associated with the user
- `num_of_companies` (integer): Total number of companies across all user campaigns
- `email_status_count` (object): Breakdown of companies by email confirmation status
  - `not started` (integer): Number of companies with email confirmation status ID 1
  - `waiting to review` (integer): Number of companies with email confirmation status ID 2
  - `reviewed` (integer): Number of companies with email confirmation status ID 3
  - `sent` (integer): Number of companies with email confirmation status IDs 4, 5, or 6
  - `opened` (integer): Number of companies with email confirmation status IDs 5 or 6
  - `replied` (integer): Number of companies with email confirmation status ID 6

## Example cURL Command

```
curl -X 'GET' \
'https://roboman-gateway.intuicon.ai/api/campaign/status/' \
-H 'accept: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-H 'request-user-id: a8b7c6d5-e4f3-2g1h-0i9j-8k7l6m5n4o3p'
```

## Response Codes

```
200 OK: The campaign status was successfully retrieved
401 Unauthorized: Missing or invalid request-user-id header
500 Internal Server Error: An unexpected error occurred on the server
```
---

# API Endpoint: Retrieve Campaign Status
---
## Description

This endpoint allows authenticated users to retrieve the status of a specific campaign. It provides a summary of the email confirmation statuses for all companies associated with the campaign, including counts for each status category (not started, waiting to review, reviewed, sent, opened, and replied).

## HTTP Request

```
Method: GET
URL: https://roboman-gateway.intuicon.ai/api/campaign/status/{campaign_id}
```

## Path Parameters

```
campaign_id (UUID, required): The unique identifier of the campaign.
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
```

## Response Body

On successful retrieval, the API returns a JSON object containing the total number of companies in the campaign and a breakdown of email status counts.

```
{
    "num_of_companies": 50,
    "email_status_count": {
        "not started": 10,
        "waiting to review": 15,
        "reviewed": 5,
        "sent": 20,
        "opened": 12,
        "replied": 8
    }
}
```

### Response Fields

- `num_of_companies` (integer): Total number of companies associated with the campaign.
- `email_status_count` (object): Counts of companies in each email confirmation status:
  - `not started` (integer): Number of companies with status ID 1 (email process not started).
  - `waiting to review` (integer): Number of companies with status ID 2 (waiting for email content review).
  - `reviewed` (integer): Number of companies with status ID 3 (email content has been reviewed).
  - `sent` (integer): Number of companies with status IDs 4, 5, or 6 (email has been sent).
  - `opened` (integer): Number of companies with status IDs 5 or 6 (email has been opened by recipient).
  - `replied` (integer): Number of companies with status ID 6 (recipient has replied to the email).

## Example cURL Command

```
curl -X 'GET' \
'https://roboman-gateway.intuicon.ai/api/campaign/status/b011627c-5576-478a-8899-8f956730ace5' \
-H 'accept: application/json' \
-H 'Authorization: Bearer <your_access_token>'
```

## Status Codes

- `200 OK`: The request was successful, and the campaign status data is returned.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to access this campaign.
- `404 Not Found`: The specified campaign ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server.

---

# API Endpoint: Retrieve User's Campaigns Status
---
## Description

This endpoint allows authenticated users to retrieve the status of all campaigns associated with their account. It provides a summary of the total number of campaigns, total number of companies across all campaigns, and a breakdown of email confirmation statuses for all companies.

## HTTP Request

```
Method: GET
URL: https://roboman-gateway.intuicon.ai/api/campaign/status/
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request_user_id: string (required). The unique identifier of the authenticated user.
```

## Response Body

On successful retrieval, the API returns a JSON object containing the total number of campaigns, total number of companies, and a breakdown of email status counts across all campaigns.

```
{
    "num_of_campaigns": 3,
    "num_of_companies": 50,
    "email_status_count": {
        "not started": 10,
        "waiting to review": 15,
        "reviewed": 5,
        "sent": 20,
        "opened": 12,
        "replied": 8
    }
}
```

### Response Fields

- `num_of_campaigns` (integer): Total number of campaigns associated with the user.
- `num_of_companies` (integer): Total number of companies across all of the user's campaigns.
- `email_status_count` (object): Counts of companies in each email confirmation status:
  - `not started` (integer): Number of companies with status ID 1 (email process not started).
  - `waiting to review` (integer): Number of companies with status ID 2 (waiting for email content review).
  - `reviewed` (integer): Number of companies with status ID 3 (email content has been reviewed).
  - `sent` (integer): Number of companies with status IDs 4, 5, or 6 (email has been sent).
  - `opened` (integer): Number of companies with status IDs 5 or 6 (email has been opened by recipient).
  - `replied` (integer): Number of companies with status ID 6 (recipient has replied to the email).

## Example cURL Command

```
curl -X 'GET' \
'https://roboman-gateway.intuicon.ai/api/campaign/status/' \
-H 'accept: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-H 'request_user_id: 550e8400-e29b-41d4-a716-************'
```

## Status Codes

- `200 OK`: The request was successful, and the campaign status data is returned.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to access this information.
- `500 Internal Server Error`: An unexpected error occurred on the server.

---

# API Endpoint: Update Campaign Content Format
---
## Description

This endpoint allows authenticated users to update the content format settings for a specific campaign. The content format settings include templates or instructions for generating email and LinkedIn message content for the campaign.

## HTTP Request

```
Method: PUT
URL: https://roboman-gateway.intuicon.ai/api/campaign/{campaign_id}/update-content-format
```

## Path Parameters

```
campaign_id (UUID, required): The unique identifier of the campaign to update.
```

## Headers

```
Accept: application/json
Content-Type: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request_user_id: string (required). The unique identifier of the authenticated user.
```

## Request Body

The request body should be a JSON object containing one or both of the following fields:

```
{
  "email_format": "Write an email of 300 words with a professional tone",
  "linkedin_msg_format": "Write a message of 100 words focusing on benefits"
}
```

### Request Fields

- `email_format` (string, optional): Instructions or template for generating email content for the campaign.
- `linkedin_msg_format` (string, optional): Instructions or template for generating LinkedIn message content for the campaign.

Note: At least one of these fields must be provided. If a field is not provided, its current value will remain unchanged.

## Response Body

On successful update, the API returns a JSON object confirming the update:

```
{
  "status": "updated successfully",
  "detail": {
    "campaign": "My Campaign:b011627c-5576-478a-8899-8f956730ace5"
  }
}
```

### Response Fields

- `status` (string): Confirmation message indicating the update was successful.
- `detail` (object): Additional details about the update:
  - `campaign` (string): A string containing the campaign name and ID in the format "name:id".

## Example cURL Command

```
curl -X 'PUT' \
'https://roboman-gateway.intuicon.ai/api/campaign/b011627c-5576-478a-8899-8f956730ace5/update-content-format' \
-H 'accept: application/json' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-H 'request_user_id: 550e8400-e29b-41d4-a716-************' \
-d '{
  "email_format": "Write an email of 300 words with a professional tone",
  "linkedin_msg_format": "Write a message of 100 words focusing on benefits"
}'
```

## Status Codes

- `200 OK`: The request was successful, and the campaign content format was updated.
- `400 Bad Request`: The request body is invalid or missing required fields.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to update this campaign.
- `404 Not Found`: The specified campaign ID does not exist or does not belong to the authenticated user.
- `500 Internal Server Error`: An unexpected error occurred on the server.

---

# API Endpoint: Update Campaign Content Format
---
## Description

This endpoint allows authenticated users to update the content format settings for a specific campaign. The content format settings include templates or instructions for generating email and LinkedIn message content for the campaign.

## HTTP Request

```
Method: PUT
URL: https://roboman-gateway.intuicon.ai/api/campaign/{campaign_id}/update-content-format
```

## Path Parameters

```
campaign_id (UUID, required): The unique identifier of the campaign to update.
```

## Headers

```
Accept: application/json
Content-Type: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request_user_id: string (required). The unique identifier of the authenticated user.
```

## Request Body

The request body should be a JSON object containing one or both of the following fields:

```
{
  "email_format": "Write an email of 300 words with a professional tone",
  "linkedin_msg_format": "Write a message of 100 words focusing on benefits"
}
```

### Request Fields

- `email_format` (string, optional): Instructions or template for generating email content for the campaign.
- `linkedin_msg_format` (string, optional): Instructions or template for generating LinkedIn message content for the campaign.

Note: At least one of these fields must be provided. If a field is not provided, its current value will remain unchanged.

## Response Body

On successful update, the API returns a JSON object confirming the update:

```
{
  "status": "updated successfully",
  "detail": {
    "campaign": "My Campaign:b011627c-5576-478a-8899-8f956730ace5"
  }
}
```

### Response Fields

- `status` (string): Confirmation message indicating the update was successful.
- `detail` (object): Additional details about the update:
  - `campaign` (string): A string containing the campaign name and ID in the format "name:id".

## Example cURL Command

```
curl -X 'PUT' \
'https://roboman-gateway.intuicon.ai/api/campaign/b011627c-5576-478a-8899-8f956730ace5/update-content-format' \
-H 'accept: application/json' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-H 'request_user_id: 550e8400-e29b-41d4-a716-************' \
-d '{
  "email_format": "Write an email of 300 words with a professional tone",
  "linkedin_msg_format": "Write a message of 100 words focusing on benefits"
}'
```

## Status Codes

- `200 OK`: The request was successful, and the campaign content format was updated.
- `400 Bad Request`: The request body is invalid or missing required fields.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to update this campaign.
- `404 Not Found`: The specified campaign ID does not exist or does not belong to the authenticated user.
- `500 Internal Server Error`: An unexpected error occurred on the server.

---

# API Endpoint: Update Campaign Details
---
## Description

This endpoint allows authenticated users to update the detailed information of a specific campaign. The update can include various aspects of the campaign such as the campaign name, core service, target audience, and other campaign-related information.

## HTTP Request

```
Method: PUT
URL: https://roboman-gateway.intuicon.ai/api/campaign/{campaign_id}/update-details
```

## Path Parameters

```
campaign_id (UUID, required): The unique identifier of the campaign to update.
```

## Headers

```
Accept: application/json
Content-Type: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request_user_id: string (required). The unique identifier of the authenticated user.
```

## Request Body

The request body should be a JSON object containing one or more of the following fields:

```
{
  "campaign_name": "Updated Campaign Name",
  "core_service": "Updated core service description",
  "unique_selling_proposition": "Our unique approach to solving problems",
  "target_audience": "Small to medium businesses in the tech industry",
  "problem_solved": "Inefficient customer communication processes",
  "key_benefits": ["Increased efficiency", "Better customer satisfaction", "Cost reduction"],
  "primary_goal_of_outreach_campaign": "Generate qualified leads for our sales team",
  "ideal_client": ["Tech startups", "SaaS companies", "E-commerce businesses"],
  "success_measurement": "20% increase in qualified leads within 3 months",
  "industry": ["Technology", "SaaS", "E-commerce"],
  "location": "United States, Canada",
  "must_have_info": "Additional important information about the campaign",
  "email_format": "Write an email of 300 words with a professional tone",
  "linkedin_msg_format": "Write a message of 100 words focusing on benefits"
}
```

### Request Fields

- `campaign_name` (string, optional): The name of the campaign.
- `core_service` (string, optional): Description of the core service offered.
- `unique_selling_proposition` (string, optional): What makes the service unique compared to competitors.
- `target_audience` (string, optional): Description of the target audience for the campaign.
- `problem_solved` (string, optional): The problem that the service solves for customers.
- `key_benefits` (array of strings, optional): List of key benefits provided by the service.
- `primary_goal_of_outreach_campaign` (string, optional): The main goal of the outreach campaign.
- `ideal_client` (array of strings, optional): Descriptions of ideal client profiles.
- `success_measurement` (string, optional): How success will be measured for this campaign.
- `industry` (array of strings, optional): Target industries for the campaign.
- `location` (string, optional): Geographic target for the campaign.
- `must_have_info` (string, optional): Additional critical information about the campaign.
- `email_format` (string, optional): Instructions or template for generating email content.
- `linkedin_msg_format` (string, optional): Instructions or template for generating LinkedIn message content.

Note: Fields that are not provided will retain their current values.

## Response Body

On successful update, the API returns a JSON object confirming the update:

```
{
  "status": "updated successfully",
  "detail": {
    "campaign": "My Campaign:b011627c-5576-478a-8899-8f956730ace5"
  }
}
```

### Response Fields

- `status` (string): Confirmation message indicating the update was successful.
- `detail` (object): Additional details about the update:
  - `campaign` (string): A string containing the campaign name and ID in the format "name:id".

## Example cURL Command

```
curl -X 'PUT' \
'https://roboman-gateway.intuicon.ai/api/campaign/b011627c-5576-478a-8899-8f956730ace5/update-details' \
-H 'accept: application/json' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-H 'request_user_id: 550e8400-e29b-41d4-a716-************' \
-d '{
  "campaign_name": "Updated Campaign Name",
  "core_service": "Updated core service description",
  "target_audience": "Small to medium businesses in the tech industry",
  "industry": ["Technology", "SaaS"]
}'
```

## Status Codes

- `200 OK`: The request was successful, and the campaign details were updated.
- `400 Bad Request`: The request body is invalid or missing required fields.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to update this campaign.
- `404 Not Found`: The specified campaign ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server.

---

# API Endpoint: Delete Campaign
---
## Description

This endpoint allows authenticated users to delete a specific campaign. When a campaign is deleted, all associated data for that campaign will be permanently removed from the system.

## HTTP Request

```
Method: DELETE
URL: https://roboman-gateway.intuicon.ai/api/campaign/{campaign_id}/delete
```

## Path Parameters

```
campaign_id (UUID, required): The unique identifier of the campaign to delete.
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request_user_id: string (required). The unique identifier of the authenticated user.
```

## Response Body

On successful deletion, the API returns a JSON object confirming the deletion:

```
{
  "status": "deleted successfully",
  "detail": {
    "campaign": "My Campaign:b011627c-5576-478a-8899-8f956730ace5"
  }
}
```

### Response Fields

- `status` (string): Confirmation message indicating the deletion was successful.
- `detail` (object): Additional details about the deletion:
  - `campaign` (string): A string containing the campaign name and ID in the format "name:id".

## Example cURL Command

```
curl -X 'DELETE' \
'https://roboman-gateway.intuicon.ai/api/campaign/b011627c-5576-478a-8899-8f956730ace5/delete' \
-H 'accept: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-H 'request_user_id: 550e8400-e29b-41d4-a716-************'
```

## Status Codes

- `200 OK`: The request was successful, and the campaign was deleted.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to delete this campaign.
- `404 Not Found`: The specified campaign ID does not exist or does not belong to the authenticated user.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- This operation cannot be undone. Once a campaign is deleted, all associated data is permanently removed.
- Only the owner of the campaign (the user who created it) can delete the campaign.
- The system verifies that the campaign belongs to the authenticated user before performing the deletion.

---
---
---

# API Endpoint: Add Multiple Companies to Campaign
---
## Description

This endpoint allows authenticated users to automatically add multiple companies to a specific campaign based on industry matching. The system searches for companies in the specified industries and adds them to the campaign, along with their primary representatives when available.

## HTTP Request

```
Method: POST
URL: https://roboman-gateway.intuicon.ai/api/campaign/{campaign_id}/companies/add-multiple/{search_size}
```

## Path Parameters

```
campaign_id (UUID, required): The unique identifier of the campaign to which companies will be added.
search_size (integer, required): The number of companies to search for and add. Must be between 1 and 300.
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request_user_id: string (required). The unique identifier of the authenticated user.
```

## Response Body

On successful addition of companies, the API returns a JSON object confirming the operation:

```
{
  "status": "success",
  "detail": "found 25 matching companies"
}
```

### Response Fields

- `status` (string): Confirmation message indicating the operation was successful.
- `detail` (string): Additional information about the operation, including the number of companies that were found and added to the campaign.

## Example cURL Command

```
curl -X 'POST' \
'https://roboman-gateway.intuicon.ai/api/campaign/b011627c-5576-478a-8899-8f956730ace5/companies/add-multiple/50' \
-H 'accept: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-H 'request_user_id: 550e8400-e29b-41d4-a716-************'
```

## Status Codes

- `200 OK`: The request was successful, and companies were added to the campaign.
- `400 Bad Request`: The search_size parameter is invalid (must be between 1 and 300).
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to add companies to this campaign.
- `404 Not Found`: The specified campaign ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- The system searches for companies that match the industries specified in the campaign's settings.
- Companies that are already associated with the campaign (based on LinkedIn URL) will not be added again.
- For each company added, the system attempts to find a primary representative, prioritizing executives (CEO, COO, CMO) and then other roles (president, manager, director).
- The search results are filtered to avoid duplicates.
- The actual number of companies added may be less than the requested search_size if there are not enough matching companies available or if some companies already exist in the campaign.

---

# API Endpoint: Manually Add Company to Campaign
---
## Description

This endpoint allows authenticated users to manually add a single company and its representative to a specific campaign. The company information and representative details are provided in the request body.

## HTTP Request

```
Method: POST
URL: https://roboman-gateway.intuicon.ai/api/campaign/{campaign_id}/companies/add
```

## Path Parameters

```
campaign_id (UUID, required): The unique identifier of the campaign to which the company will be added.
```

## Headers

```
Accept: application/json
Content-Type: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
```

## Request Body

The request body should be a JSON object containing information about the company and its representative:

```
{
  "company_name": "Acme Corporation",
  "company_email": "<EMAIL>",
  "company_linkedin": "https://www.linkedin.com/company/acmecorp",
  "industry": "Technology",
  "rep_name": "John Doe",
  "rep_email": "<EMAIL>",
  "rep_linkedin_address": "https://www.linkedin.com/in/johndoe"
}
```

### Request Fields

- `company_name` (string, required): The name of the company.
- `company_email` (string, optional): The email address of the company.
- `company_linkedin` (string, optional): The LinkedIn URL of the company.
- `industry` (string, required): The industry category of the company.
- `rep_name` (string, required): The name of the company representative.
- `rep_email` (string, optional): The email address of the company representative.
- `rep_linkedin_address` (string, optional): The LinkedIn URL of the company representative.

## Response Body

On successful addition of the company, the API returns a JSON object confirming the operation:

```
{
  "status": "success",
  "detail": {
    "campaign": "My Campaign:b011627c-5576-478a-8899-8f956730ace5"
  }
}
```

### Response Fields

- `status` (string): Confirmation message indicating the operation was successful.
- `detail` (object): Additional details about the operation:
  - `campaign` (string): A string containing the campaign name and ID in the format "name:id".

## Example cURL Command

```
curl -X 'POST' \
'https://roboman-gateway.intuicon.ai/api/campaign/b011627c-5576-478a-8899-8f956730ace5/companies/add' \
-H 'accept: application/json' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-d '{
  "company_name": "Acme Corporation",
  "company_email": "<EMAIL>",
  "company_linkedin": "https://www.linkedin.com/company/acmecorp",
  "industry": "Technology",
  "rep_name": "John Doe",
  "rep_email": "<EMAIL>",
  "rep_linkedin_address": "https://www.linkedin.com/in/johndoe"
}'
```

## Status Codes

- `200 OK`: The request was successful, and the company was added to the campaign.
- `400 Bad Request`: The request body is invalid or missing required fields.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to add companies to this campaign.
- `404 Not Found`: The specified campaign ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- If a LinkedIn address is provided for the representative, the system will automatically trigger a task to update the representative's personality profile using the Humantic AI service.
- The representative is automatically set as the primary contact for the company.
- Both the company and its representative are added to the database in a single transaction.

---

# API Endpoint: Update Company Email Status
---
## Description

This endpoint allows authenticated users to update the email content, subject, and confirmation status for a specific company in a campaign. This is typically used when reviewing or modifying email content before sending it to the company representative.

## HTTP Request

```
Method: PUT
URL: https://roboman-gateway.intuicon.ai/api/campaign/companies/update-email-status
```

## Headers

```
Accept: application/json
Content-Type: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request_user_id: string (required). The unique identifier of the authenticated user.
```

## Request Body

The request body should be a JSON object containing the company ID and one or more fields to update:

```
{
  "company_id": "f0d583db-7fac-4f6c-99f7-0d98964234e1",
  "content_subject": "Subject: Transform Your Business with Our Innovative Solutions",
  "content": "Dear Pioneering Systems Team,\n\nI hope this message finds you well...",
  "email_confirmation_status_id": 2
}
```

### Request Fields

- `company_id` (string, required): The unique identifier of the company to update.
- `content_subject` (string, optional): The subject line for the email to be sent to the company.
- `content` (string, optional): The body content of the email to be sent to the company.
- `email_confirmation_status_id` (integer, optional): The ID representing the email confirmation status. Valid values are:
  - 1: Not started
  - 2: Waiting to review
  - 3: Reviewed
  - 4: Sent
  - 5: Opened

Note: At least one of the optional fields must be provided. Fields that are not provided will retain their current values.

## Response Body

On successful update, the API returns a JSON object confirming the update:

```
{
  "status": "updated successfully",
  "detail": {
    "company": "Pioneering Systems:f0d583db-7fac-4f6c-99f7-0d98964234e1",
    "campaign": "b011627c-5576-478a-8899-8f956730ace5"
  }
}
```

### Response Fields

- `status` (string): Confirmation message indicating the update was successful.
- `detail` (object): Additional details about the update:
  - `company` (string): A string containing the company name and ID in the format "name:id".
  - `campaign` (string): The ID of the campaign that the company belongs to.

## Example cURL Command

```
curl -X 'PUT' \
'https://roboman-gateway.intuicon.ai/api/campaign/companies/update-email-status' \
-H 'accept: application/json' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-H 'request_user_id: 550e8400-e29b-41d4-a716-************' \
-d '{
  "company_id": "f0d583db-7fac-4f6c-99f7-0d98964234e1",
  "content_subject": "Subject: Transform Your Business with Our Innovative Solutions",
  "content": "Dear Pioneering Systems Team,\n\nI hope this message finds you well...",
  "email_confirmation_status_id": 3
}'
```

## Status Codes

- `200 OK`: The request was successful, and the company email status was updated.
- `400 Bad Request`: The request body is invalid or missing required fields.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to update this company.
- `404 Not Found`: The specified company ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- The email confirmation status IDs correspond to specific stages in the email outreach process:
  - 1: Not started - Email content has not been generated yet
  - 2: Waiting to review - Email content has been generated and is waiting for review
  - 3: Reviewed - Email content has been reviewed and approved
  - 4: Sent - Email has been sent to the recipient
  - 5: Opened - Recipient has opened the email
- When updating the email confirmation status, ensure you are using the correct status ID for the current stage in your workflow.
- This endpoint only updates the specified fields; any fields not included in the request will remain unchanged.

---

# API Endpoint: Delete Company from Campaign
---
## Description

This endpoint allows authenticated users to delete a specific company from a campaign. When a company is deleted, all associated data for that company, including representative information and email content, will be permanently removed from the system.

## HTTP Request

```
Method: DELETE
URL: https://roboman-gateway.intuicon.ai/api/campaign/companies/{company_id}/delete
```

## Path Parameters

```
company_id (UUID, required): The unique identifier of the company to delete.
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request_user_id: string (required). The unique identifier of the authenticated user.
```

## Response Body

On successful deletion, the API returns a JSON object confirming the deletion:

```
{
  "status": "deleted successfully",
  "detail": {
    "company": "Acme Corporation:f0d583db-7fac-4f6c-99f7-0d98964234e1",
    "campaign": "b011627c-5576-478a-8899-8f956730ace5"
  }
}
```

### Response Fields

- `status` (string): Confirmation message indicating the deletion was successful.
- `detail` (object): Additional details about the deletion:
  - `company` (string): A string containing the company name and ID in the format "name:id".
  - `campaign` (string): The ID of the campaign that the company belonged to.

## Example cURL Command

```
curl -X 'DELETE' \
'https://roboman-gateway.intuicon.ai/api/campaign/companies/f0d583db-7fac-4f6c-99f7-0d98964234e1/delete' \
-H 'accept: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-H 'request_user_id: 550e8400-e29b-41d4-a716-************'
```

## Status Codes

- `200 OK`: The request was successful, and the company was deleted.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to delete this company.
- `404 Not Found`: The specified company ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- This operation cannot be undone. Once a company is deleted, all associated data is permanently removed.
- Due to the database's cascade delete configuration, deleting a company will also delete all related records, including:
  - Company representatives
  - Email content and status information
  - Any drafted or sent emails associated with the company
- The system verifies that the company exists before performing the deletion.

---

# API Endpoint: Update Representative's Humantic AI Profile
---
## Description

This endpoint allows authenticated users to update a company representative's personality profile using Humantic AI data. The profile includes personality traits (OCEAN and DISC analysis), communication preferences, and personalized advice for effective outreach. This information helps tailor email and communication strategies to match the representative's personality.

## HTTP Request

```
Method: PUT
URL: https://roboman-gateway.intuicon.ai/api/campaign/companies/{company_id}/representatives/{rep_id}/humantic/update
```

## Path Parameters

```
company_id (UUID, required): The unique identifier of the company.
rep_id (UUID, required): The unique identifier of the representative whose profile will be updated.
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
```

## Response Body

On successful update, the API returns a JSON object confirming the operation:

```
{
  "status": "success",
  "detail": "updated John Doe"
}
```

If the Humantic profile is not immediately available, the system will queue a task to fetch it:

```
{
  "status": "success",
  "detail": "humantic profile not readily available, it will be fetched from Humantic AI"
}
```

### Response Fields

- `status` (string): Confirmation message indicating the operation status.
- `detail` (string): Additional information about the operation, including either the name of the updated representative or a message indicating that the profile will be fetched asynchronously.

## Example cURL Command

```
curl -X 'PUT' \
'https://roboman-gateway.intuicon.ai/api/campaign/companies/f0d583db-7fac-4f6c-99f7-0d98964234e1/representatives/a7c5b934-1d23-4fb2-9d3a-8b6f45e21c87/humantic/update' \
-H 'accept: application/json' \
-H 'Authorization: Bearer <your_access_token>'
```

## Status Codes

- `200 OK`: The request was successful, and the representative's profile was updated or queued for update.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to update this representative.
- `404 Not Found`: The specified company ID or representative ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- The Humantic AI profile includes a wide range of data about the representative, including:
  - Personality analysis (OCEAN and DISC traits)
  - Work history and skills
  - Location and education information
  - Profile image
  - Communication preferences
  - Email personalization advice
  - Cold calling advice
  - Hiring behavioral factors
- If a Humantic profile already exists for the representative's LinkedIn username, the data is immediately applied to the representative's record.
- If no profile exists, the system sets the representative's analysis_status to "IN_PROGRESS" and queues an asynchronous task to fetch the profile from Humantic AI.
- The Humantic AI profile is created and fetched based on the representative's LinkedIn profile URL.
- The personality analysis can be used to tailor communication strategies for more effective outreach.

---

# API Endpoint: Retrieve Primary Representative for a Company
---
## Description

This endpoint allows authenticated users to retrieve the primary representative for a specific company. The response includes detailed information about the representative, including contact details and personality analysis if available.

## HTTP Request

```
Method: GET
URL: https://roboman-gateway.intuicon.ai/api/campaign/companies/{company_id}/representative/
```

## Path Parameters

```
company_id (UUID, required): The unique identifier of the company whose primary representative will be retrieved.
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
```

## Response Body

On successful retrieval, the API returns a JSON object containing detailed information about the primary representative:

```
{
  "rep_id": "a7c5b934-1d23-4fb2-9d3a-8b6f45e21c87",
  "company_id": "f0d583db-7fac-4f6c-99f7-0d98964234e1",
  "rep_name": "John Doe",
  "rep_email": "<EMAIL>",
  "rep_linkedin_address": "https://www.linkedin.com/in/johndoe",
  "rep_linkedin_urn": null,
  "user_description": "Marketing Director with 10+ years of experience",
  "work_history": {
    "current": {
      "title": "Marketing Director",
      "company": "Acme Corporation",
      "duration": "2018-present"
    },
    "previous": [
      {
        "title": "Marketing Manager",
        "company": "XYZ Inc",
        "duration": "2015-2018"
      }
    ]
  },
  "profile_image": "https://example.com/profile.jpg",
  "location": "San Francisco, CA",
  "skills": ["Digital Marketing", "Content Strategy", "SEO"],
  "followers": 1250,
  "prographics": {
    "industry": "Marketing and Advertising",
    "seniority": "Director"
  },
  "education": {
    "schools": [
      {
        "name": "Stanford University",
        "degree": "MBA",
        "field": "Marketing",
        "year": "2012"
      }
    ]
  },
  "personality_analysis": {
    "summary": {
      "ocean": {
        "label": ["Openness", "Conscientiousness", "Extraversion"],
        "description": "This person is creative, organized, and outgoing."
      },
      "disc": {
        "label": ["Influence", "Steadiness"],
        "description": "This person is persuasive and reliable."
      }
    }
  },
  "cold_calling_advice": {
    "do": ["Be enthusiastic", "Focus on benefits"],
    "dont": ["Be too pushy", "Use technical jargon"]
  },
  "communication_advice": {
    "do": ["Use social proof", "Be friendly and approachable"],
    "dont": ["Be overly formal", "Rush the conversation"]
  },
  "email_personalization": {
    "advice": {
      "Subject": "Keep it concise and benefit-focused",
      "Subject Length": "5-7 words",
      "Salutation": "Hi John,",
      "Greeting": "Personalized and friendly",
      "Bullet Points": "Use to highlight key benefits"
    }
  },
  "sales_profile_url": "https://example.com/sales-profile",
  "primary_contact": true,
  "analysis_status": "COMPLETE",
  "analysis_confidence": {
    "ocean": 0.85,
    "disc": 0.92
  }
}
```

### Response Fields

The response includes all fields from the Representative model, which may include:

- `rep_id` (string): The unique identifier of the representative.
- `company_id` (string): The unique identifier of the company.
- `rep_name` (string): The name of the representative.
- `rep_email` (string): The email address of the representative.
- `rep_linkedin_address` (string): The LinkedIn URL of the representative.
- `rep_linkedin_urn` (string): The LinkedIn URN of the representative.
- `user_description` (string): A brief description of the representative.
- `work_history` (object): The representative's work experience.
- `profile_image` (string): URL to the representative's profile image.
- `location` (string): The representative's location.
- `skills` (array): List of the representative's skills.
- `followers` (integer): Number of LinkedIn followers.
- `prographics` (object): Professional demographics information.
- `education` (object): Educational background information.
- `personality_analysis` (object): OCEAN and DISC personality traits analysis.
- `cold_calling_advice` (object): Advice for cold calling this representative.
- `communication_advice` (object): Advice for communicating with this representative.
- `email_personalization` (object): Advice for personalizing emails to this representative.
- `sales_profile_url` (string): URL to the representative's sales profile.
- `primary_contact` (boolean): Indicates if this is the primary contact for the company (will be true).
- `analysis_status` (string): Status of the personality analysis (e.g., "COMPLETE", "IN_PROGRESS", "NOT_STARTED", "NOT_FOUND").
- `analysis_confidence` (object): Confidence scores for the personality analysis.

Note: Some fields may be null if the representative's profile has not been fully analyzed by Humantic AI.

## Example cURL Command

```
curl -X 'GET' \
'https://roboman-gateway.intuicon.ai/api/campaign/companies/f0d583db-7fac-4f6c-99f7-0d98964234e1/representative/' \
-H 'accept: application/json' \
-H 'Authorization: Bearer <your_access_token>'
```

## Status Codes

- `200 OK`: The request was successful, and the representative data is returned.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to access this information.
- `404 Not Found`: No primary representative was found for the specified company.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- This endpoint returns only the primary representative for the company (where `primary_contact` is set to `true`).
- The personality analysis data is populated by the Humantic AI integration and may not be available for all representatives.
- If the personality analysis has not been completed, you can use the "Update Representative's Humantic AI Profile" endpoint to request an analysis.
- The personality insights can be used to tailor communication strategies for more effective outreach.

---

# API Endpoint: Update Company Representative
---
## Description

This endpoint allows authenticated users to update information about a company representative. This can include contact details, LinkedIn information, personality traits, and primary contact status.

## HTTP Request

```
Method: PUT
URL: https://roboman-gateway.intuicon.ai/api/campaign/companies/representative/update
```

## Headers

```
Accept: application/json
Content-Type: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
```

## Request Body

The request body should be a JSON object containing the representative ID, company ID, and one or more fields to update:

```
{
  "rep_id": "a7c5b934-1d23-4fb2-9d3a-8b6f45e21c87",
  "company_id": "f0d583db-7fac-4f6c-99f7-0d98964234e1",
  "rep_email": "<EMAIL>",
  "rep_linkedin_address": "https://www.linkedin.com/in/johndoe",
  "rep_linkedin_urn": "urn:li:person:abcdefg",
  "ocean_label": ["Openness", "Conscientiousness", "Extraversion"],
  "disc_label": ["Influence", "Steadiness"],
  "primary_contact": true
}
```

### Request Fields

- `rep_id` (string, required): The unique identifier of the representative to update.
- `company_id` (string, required): The unique identifier of the company the representative belongs to.
- `rep_email` (string, optional): The email address of the representative.
- `rep_linkedin_address` (string, optional): The LinkedIn URL of the representative.
- `rep_linkedin_urn` (string, optional): The LinkedIn URN (Uniform Resource Name) of the representative.
- `ocean_label` (array of strings, optional): OCEAN personality traits of the representative (Openness, Conscientiousness, Extraversion, Agreeableness, Neuroticism).
- `disc_label` (array of strings, optional): DISC personality traits of the representative (Dominance, Influence, Steadiness, Conscientiousness).
- `primary_contact` (boolean, optional): Whether this representative is the primary contact for the company.

Note: At least one of the optional fields must be provided. Fields that are not provided will retain their current values.

## Response Body

On successful update, the API returns a JSON object confirming the update:

```
{
  "status": "updated successfully",
  "detail": {
    "rep": "John Doe:a7c5b934-1d23-4fb2-9d3a-8b6f45e21c87",
    "company": "f0d583db-7fac-4f6c-99f7-0d98964234e1"
  }
}
```

### Response Fields

- `status` (string): Confirmation message indicating the update was successful.
- `detail` (object): Additional details about the update:
  - `rep` (string): A string containing the representative name and ID in the format "name:id".
  - `company` (string): The ID of the company that the representative belongs to.

## Example cURL Command

```
curl -X 'PUT' \
'https://roboman-gateway.intuicon.ai/api/campaign/companies/representative/update' \
-H 'accept: application/json' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-d '{
  "rep_id": "a7c5b934-1d23-4fb2-9d3a-8b6f45e21c87",
  "company_id": "f0d583db-7fac-4f6c-99f7-0d98964234e1",
  "rep_email": "<EMAIL>",
  "primary_contact": true
}'
```

## Status Codes

- `200 OK`: The request was successful, and the representative information was updated.
- `400 Bad Request`: The request body is invalid or missing required fields.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to update this representative.
- `404 Not Found`: The specified representative ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- The `rep_id` and `company_id` fields are required to identify the representative to update.
- For more comprehensive personality profile updates, consider using the "Update Representative's Humantic AI Profile" endpoint, which provides detailed personality analysis and communication advice.
- If you set a representative as the primary contact (`primary_contact: true`), you may want to ensure that no other representatives for the same company are also set as primary contacts.
- The OCEAN and DISC labels are typically populated by the Humantic AI integration but can be manually updated using this endpoint if needed.

---

# API Endpoint: Delete Company Representative
---
## Description

This endpoint allows authenticated users to delete a specific representative from a company. When a representative is deleted, all associated data for that representative will be permanently removed from the system.

## HTTP Request

```
Method: DELETE
URL: https://roboman-gateway.intuicon.ai/api/campaign/companies/representative/{rep_id}/delete
```

## Path Parameters

```
rep_id (UUID, required): The unique identifier of the representative to delete.
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
```

## Response Body

On successful deletion, the API returns a JSON object confirming the deletion:

```
{
  "status": "deleted successfully",
  "detail": {
    "rep": "John Doe:a7c5b934-1d23-4fb2-9d3a-8b6f45e21c87",
    "company": "f0d583db-7fac-4f6c-99f7-0d98964234e1"
  }
}
```

### Response Fields

- `status` (string): Confirmation message indicating the deletion was successful.
- `detail` (object): Additional details about the deletion:
  - `rep` (string): A string containing the representative name and ID in the format "name:id".
  - `company` (string): The ID of the company that the representative belonged to.

## Example cURL Command

```
curl -X 'DELETE' \
'https://roboman-gateway.intuicon.ai/api/campaign/companies/representative/a7c5b934-1d23-4fb2-9d3a-8b6f45e21c87/delete' \
-H 'accept: application/json' \
-H 'Authorization: Bearer <your_access_token>'
```

## Status Codes

- `200 OK`: The request was successful, and the representative was deleted.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to delete this representative.
- `404 Not Found`: The specified representative ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- This operation cannot be undone. Once a representative is deleted, all associated data is permanently removed.
- If the deleted representative was the primary contact for the company, you should designate another representative as the primary contact using the "Update Company Representative" endpoint.
- Deleting a representative does not delete the associated company. The company record will remain in the system.
- The system verifies that the representative exists before performing the deletion.
- Consider updating the representative information instead of deleting if you only need to change some details.

---

# API Endpoint: Retrieve Cached Humantic AI Profiles
---
## Description

This endpoint allows authenticated users to retrieve all cached Humantic AI profiles stored in the system. These profiles contain detailed personality analysis and professional information for company representatives, which can be used to tailor communication strategies for more effective outreach.

## HTTP Request

```
Method: GET
URL: https://roboman-gateway.intuicon.ai/api/campaign/humantic/profiles
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
```

## Response Body

On successful retrieval, the API returns an array of Humantic profile objects:

```
[
  {
    "profile_id": "f0d583db-7fac-4f6c-99f7-0d98964234e1",
    "linkedin_username": "johndoe",
    "linkedin_address": "https://www.linkedin.com/in/johndoe",
    "results": {
      "user_profile_image": "https://example.com/profile.jpg",
      "user_description": "Marketing Director with 10+ years of experience",
      "work_history": {
        "current": {
          "title": "Marketing Director",
          "company": "Acme Corporation",
          "duration": "2018-present"
        },
        "previous": [
          {
            "title": "Marketing Manager",
            "company": "XYZ Inc",
            "duration": "2015-2018"
          }
        ]
      },
      "location": "San Francisco, CA",
      "skills": ["Digital Marketing", "Content Strategy", "SEO"],
      "followers": 1250,
      "prographics": {
        "industry": "Marketing and Advertising",
        "seniority": "Director"
      },
      "education": {
        "schools": [
          {
            "name": "Stanford University",
            "degree": "MBA",
            "field": "Marketing",
            "year": "2012"
          }
        ]
      },
      "personality_analysis": {
        "summary": {
          "ocean": {
            "label": ["Openness", "Conscientiousness", "Extraversion"],
            "description": "This person is creative, organized, and outgoing."
          },
          "disc": {
            "label": ["Influence", "Steadiness"],
            "description": "This person is persuasive and reliable."
          }
        }
      },
      "persona": {
        "sales": {
          "cold_calling_advice": {
            "do": ["Be enthusiastic", "Focus on benefits"],
            "dont": ["Be too pushy", "Use technical jargon"]
          },
          "communication_advice": {
            "do": ["Use social proof", "Be friendly and approachable"],
            "dont": ["Be overly formal", "Rush the conversation"]
          },
          "email_personalization": {
            "advice": {
              "Subject": "Keep it concise and benefit-focused",
              "Subject Length": "5-7 words",
              "Salutation": "Hi John,",
              "Greeting": "Personalized and friendly",
              "Bullet Points": "Use to highlight key benefits"
            }
          },
          "profile_url": "https://example.com/sales-profile"
        },
        "hiring": {
          "behavioural_factors": {
            "strengths": ["Leadership", "Communication"],
            "weaknesses": ["Impatience"]
          }
        }
      }
    },
    "meta_data": {
      "analysis_status": "COMPLETE",
      "status": "FOUND",
      "status_code": 1,
      "confidence": {
        "ocean": 0.85,
        "disc": 0.92
      }
    },
    "created_at": "2024-01-15T10:30:00.000Z",
    "updated_at": "2024-01-15T10:30:00.000Z"
  },
  // Additional profiles...
]
```

### Response Fields

Each profile in the array includes:

- `profile_id` (string): The unique identifier of the Humantic profile.
- `linkedin_username` (string): The LinkedIn username extracted from the LinkedIn URL.
- `linkedin_address` (string): The full LinkedIn URL of the representative.
- `results` (object): Detailed results from the Humantic AI analysis, including:
  - `user_profile_image` (string): URL to the representative's profile image.
  - `user_description` (string): A brief description of the representative.
  - `work_history` (object): The representative's work experience.
  - `location` (string): The representative's location.
  - `skills` (array): List of the representative's skills.
  - `followers` (integer): Number of LinkedIn followers.
  - `prographics` (object): Professional demographics information.
  - `education` (object): Educational background information.
  - `personality_analysis` (object): OCEAN and DISC personality traits analysis.
  - `persona` (object): Persona-specific advice, including:
    - `sales` (object): Sales-related advice, including:
      - `cold_calling_advice` (object): Advice for cold calling this representative.
      - `communication_advice` (object): Advice for communicating with this representative.
      - `email_personalization` (object): Advice for personalizing emails to this representative.
      - `profile_url` (string): URL to the representative's sales profile.
    - `hiring` (object): Hiring-related information, including:
      - `behavioural_factors` (object): Behavioral strengths and weaknesses.
- `meta_data` (object): Metadata about the analysis, including:
  - `analysis_status` (string): Status of the analysis (e.g., "COMPLETE").
  - `status` (string): Status of the profile retrieval (e.g., "FOUND").
  - `status_code` (integer): Status code from the Humantic AI service.
  - `confidence` (object): Confidence scores for the personality analysis.
- `created_at` (string): Timestamp when the profile was created.
- `updated_at` (string): Timestamp when the profile was last updated.

## Example cURL Command

```
curl -X 'GET' \
'https://roboman-gateway.intuicon.ai/api/campaign/humantic/profiles' \
-H 'accept: application/json' \
-H 'Authorization: Bearer <your_access_token>'
```

## Status Codes

- `200 OK`: The request was successful, and the Humantic profiles are returned.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to access this information.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- This endpoint returns all cached Humantic profiles in the system, which may include profiles for representatives that are no longer active.
- The profiles are cached to improve performance and reduce the number of calls to the Humantic AI service.
- The personality insights can be used to tailor communication strategies for more effective outreach.
- To update a specific representative's Humantic profile, use the "Update Representative's Humantic AI Profile" endpoint.
- The amount of data returned by this endpoint can be large if there are many profiles in the system. Consider implementing pagination if needed.

---
---
---

# API Endpoint: Generate Emails for Campaign Companies
---
## Description

This endpoint allows authenticated users to generate personalized email content for multiple companies in a campaign. The system uses AI to create tailored emails based on the campaign information and the personality profiles of the company representatives. The email generation process is asynchronous, with emails being generated in the background.

## HTTP Request

```
Method: POST
URL: https://roboman-gateway.intuicon.ai/api/campaign/emails/generate
```

## Headers

```
Accept: application/json
Content-Type: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
```

## Request Body

The request body should be a JSON object containing the following fields:

```
{
  "company_id_list": ["f0d583db-7fac-4f6c-99f7-0d98964234e1", "d13a66b2-7f33-4209-b681-914fed38d498"],
  "campaign_id": "b011627c-5576-478a-8899-8f956730ace5",
  "prompt_name": "sales_introduction"
}
```

### Request Fields

- `company_id_list` (array of strings, required): A list of company IDs for which emails should be generated.
- `campaign_id` (string, required): The unique identifier of the campaign that contains the email content template and other campaign information.
- `prompt_name` (string, optional): A specific prompt template name to use for email generation. If not provided, the default template will be used.

## Response Body

On successful initiation of the email generation process, the API returns a simple success message:

```
{
  "status": "success"
}
```

### Response Fields

- `status` (string): Confirmation message indicating that the email generation process has been successfully initiated.

## Example cURL Command

```
curl -X 'POST' \
'https://roboman-gateway.intuicon.ai/api/campaign/emails/generate' \
-H 'accept: application/json' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-d '{
  "company_id_list": ["f0d583db-7fac-4f6c-99f7-0d98964234e1", "d13a66b2-7f33-4209-b681-914fed38d498"],
  "campaign_id": "b011627c-5576-478a-8899-8f956730ace5",
  "prompt_name": "sales_introduction"
}'
```

## Status Codes

- `200 OK`: The request was successful, and the email generation process has been initiated.
- `400 Bad Request`: The request body is invalid or missing required fields.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to generate emails for this campaign.
- `404 Not Found`: The specified campaign ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- This endpoint initiates an asynchronous process. The emails are not generated immediately but are queued for background processing.
- The system uses the following information to generate personalized emails:
  - Campaign details (core service, key benefits, problem solved, etc.)
  - Company information (industry, company name)
  - Representative details (name, email)
  - Personality analysis of the representative (if available)
  - Email personalization advice based on the representative's personality profile (if available)
  - Communication advice tailored to the representative's personality (if available)
- The email format is determined by the `email_format` field in the campaign settings.
- After emails are generated, they will be available for review and can be sent using the "Send Emails" endpoint.
- The generated emails will have their `email_confirmation_status_id` set to indicate they are waiting for review.
- To check the status of generated emails, use the appropriate endpoints to retrieve company information with their email status.

---

# API Endpoint: Create Draft Email
---
## Description

This endpoint allows authenticated users to create a draft email for a specific company in a campaign. The draft email can be used as a template for sending personalized emails to company representatives. When a draft email is created, the company's email confirmation status is updated to indicate that the email is waiting for review.

## HTTP Request

```
Method: POST
URL: https://roboman-gateway.intuicon.ai/api/campaign/emails/draft/create
```

## Headers

```
Accept: application/json
Content-Type: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request_user_id: string (required). The unique identifier of the authenticated user.
```

## Request Body

The request body should be a JSON object containing the following fields:

```
{
  "company_id": "f0d583db-7fac-4f6c-99f7-0d98964234e1",
  "draft_type": 0,
  "from_address": "<EMAIL>",
  "from_name": "John Sender",
  "to_address": "<EMAIL>",
  "to_name": "Jane Recipient",
  "subject": "Introducing Our Innovative Solution",
  "body": "<p>Dear Jane,</p><p>I hope this email finds you well...</p>",
  "body_plain": "Dear Jane,\n\nI hope this email finds you well..."
}
```

### Request Fields

- `company_id` (string, required): The unique identifier of the company for which the draft email is being created.
- `draft_type` (integer, optional, default: 0): The type of draft email. This can be used to categorize different types of emails (e.g., initial outreach, follow-up).
- `from_address` (string, optional, default: "<EMAIL>"): The email address of the sender.
- `from_name` (string, required): The name of the sender.
- `to_address` (string, required): The email address of the recipient (company representative).
- `to_name` (string, required): The name of the recipient (company representative).
- `subject` (string, required): The subject line of the email.
- `body` (string, required): The HTML content of the email body.
- `body_plain` (string, required): The plain text content of the email body.

## Response

This endpoint returns a 201 Created status code on successful creation of the draft email. There is no response body.

## Example cURL Command

```
curl -X 'POST' \
'https://roboman-gateway.intuicon.ai/api/campaign/emails/draft/create' \
-H 'accept: application/json' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-H 'request_user_id: 550e8400-e29b-41d4-a716-************' \
-d '{
  "company_id": "f0d583db-7fac-4f6c-99f7-0d98964234e1",
  "draft_type": 0,
  "from_address": "<EMAIL>",
  "from_name": "John Sender",
  "to_address": "<EMAIL>",
  "to_name": "Jane Recipient",
  "subject": "Introducing Our Innovative Solution",
  "body": "<p>Dear Jane,</p><p>I hope this email finds you well...</p>",
  "body_plain": "Dear Jane,\n\nI hope this email finds you well..."
}'
```

## Status Codes

- `201 Created`: The request was successful, and the draft email was created.
- `400 Bad Request`: The request body is invalid or missing required fields.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to create draft emails for this company.
- `404 Not Found`: The specified company ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- When a draft email is created, the company's email confirmation status is automatically updated to 2 (waiting to review).
- Draft emails can be used as templates for sending personalized emails to company representatives.
- The `draft_type` field can be used to categorize different types of emails, such as initial outreach emails, follow-up emails, etc.
- Both HTML (`body`) and plain text (`body_plain`) versions of the email content should be provided to ensure compatibility with different email clients.
- The draft email is stored in the system but is not sent to the recipient until explicitly requested through the appropriate endpoint.
- To update an existing draft email, use the "Update Draft Email" endpoint.
- To send a draft email, use the "Send Email" endpoint.

---

# API Endpoint: Update Draft Email
---
## Description

This endpoint allows authenticated users to update an existing draft email for a specific company in a campaign. When a draft email is updated, the company's email confirmation status is updated to indicate that the email has been reviewed.

## HTTP Request

```
Method: PUT
URL: https://roboman-gateway.intuicon.ai/api/campaign/emails/draft/update
```

## Headers

```
Accept: application/json
Content-Type: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
```

## Request Body

The request body should be a JSON object containing the following fields:

```
{
  "company_id": "f0d583db-7fac-4f6c-99f7-0d98964234e1",
  "draft_id": "a7c5b934-1d23-4fb2-9d3a-8b6f45e21c87",
  "subject": "Updated: Introducing Our Innovative Solution",
  "body": "<p>Dear Jane,</p><p>I hope this email finds you well. I wanted to follow up on our previous conversation...</p>"
}
```

### Request Fields

- `company_id` (string, required): The unique identifier of the company associated with the draft email.
- `draft_id` (string, required): The unique identifier of the draft email to update.
- `subject` (string, optional): The updated subject line of the email. If not provided, the existing subject will be retained.
- `body` (string, optional): The updated HTML content of the email body. If not provided, the existing body will be retained.

Note: At least one of the optional fields (`subject` or `body`) should be provided to make meaningful updates.

## Response Body

On successful update, the API returns a JSON object confirming the update:

```
{
  "status": "success"
}
```

### Response Fields

- `status` (string): Confirmation message indicating that the update was successful.

## Example cURL Command

```
curl -X 'PUT' \
'https://roboman-gateway.intuicon.ai/api/campaign/emails/draft/update' \
-H 'accept: application/json' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-d '{
  "company_id": "f0d583db-7fac-4f6c-99f7-0d98964234e1",
  "draft_id": "a7c5b934-1d23-4fb2-9d3a-8b6f45e21c87",
  "subject": "Updated: Introducing Our Innovative Solution",
  "body": "<p>Dear Jane,</p><p>I hope this email finds you well. I wanted to follow up on our previous conversation...</p>"
}'
```

## Status Codes

- `200 OK`: The request was successful, and the draft email was updated.
- `400 Bad Request`: The request body is invalid or missing required fields.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to update this draft email.
- `404 Not Found`: The specified draft ID or company ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- When a draft email is updated, the company's email confirmation status is automatically updated to 3 (reviewed).
- This status change indicates that the email content has been reviewed and is ready to be sent.
- Only the `subject` and `body` fields of the draft email can be updated through this endpoint.
- Other fields such as recipient information (`to_address`, `to_name`) cannot be modified using this endpoint.
- If you need to update the plain text version of the email body (`body_plain`), you will need to create a new draft email.
- After updating a draft email, it can be sent using the "Send Email" endpoint.
- The updated draft email is stored in the system but is not sent to the recipient until explicitly requested through the appropriate endpoint.

---

# API Endpoint: Retrieve Draft Emails for a Company
---
## Description

This endpoint allows authenticated users to retrieve all draft emails associated with a specific company. Draft emails are templates that have been created for sending personalized emails to company representatives.

## HTTP Request

```
Method: GET
URL: https://roboman-gateway.intuicon.ai/api/campaign/emails/draft/{company_id}
```

## Path Parameters

```
company_id (UUID, required): The unique identifier of the company whose draft emails will be retrieved.
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
```

## Response Body

On successful retrieval, the API returns an array of draft email objects:

```
[
  {
    "draft_id": "a7c5b934-1d23-4fb2-9d3a-8b6f45e21c87",
    "draft_type": 0,
    "company_id": "f0d583db-7fac-4f6c-99f7-0d98964234e1",
    "from_address": "<EMAIL>",
    "from_name": "John Sender",
    "to_address": "<EMAIL>",
    "to_name": "Jane Recipient",
    "subject": "Introducing Our Innovative Solution",
    "body": "<p>Dear Jane,</p><p>I hope this email finds you well...</p>",
    "body_plain": "Dear Jane,\n\nI hope this email finds you well..."
  },
  {
    "draft_id": "b8d694c5-2e34-5fc3-0e4b-9c7f56e32d98",
    "draft_type": 1,
    "company_id": "f0d583db-7fac-4f6c-99f7-0d98964234e1",
    "from_address": "<EMAIL>",
    "from_name": "John Sender",
    "to_address": "<EMAIL>",
    "to_name": "Jane Recipient",
    "subject": "Follow-up: Our Previous Conversation",
    "body": "<p>Dear Jane,</p><p>I wanted to follow up on our previous conversation...</p>",
    "body_plain": "Dear Jane,\n\nI wanted to follow up on our previous conversation..."
  }
]
```

### Response Fields

Each draft email object in the array includes:

- `draft_id` (string): The unique identifier of the draft email.
- `draft_type` (integer): The type of draft email (e.g., 0 for initial outreach, 1 for first follow-up).
- `company_id` (string): The unique identifier of the company associated with the draft email.
- `from_address` (string): The email address of the sender.
- `from_name` (string): The name of the sender.
- `to_address` (string): The email address of the recipient (company representative).
- `to_name` (string): The name of the recipient (company representative).
- `subject` (string): The subject line of the email.
- `body` (string): The HTML content of the email body.
- `body_plain` (string): The plain text content of the email body.

## Example cURL Command

```
curl -X 'GET' \
'https://roboman-gateway.intuicon.ai/api/campaign/emails/draft/f0d583db-7fac-4f6c-99f7-0d98964234e1' \
-H 'accept: application/json' \
-H 'Authorization: Bearer <your_access_token>'
```

## Status Codes

- `200 OK`: The request was successful, and the draft emails are returned.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to access this information.
- `404 Not Found`: No draft emails were found for the specified company.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- The draft emails are returned in ascending order of `draft_type`, which typically represents the sequence of emails in an outreach campaign (e.g., initial email, first follow-up, second follow-up).
- Draft emails with lower `draft_type` values are typically sent first in an email sequence.
- The response includes both HTML (`body`) and plain text (`body_plain`) versions of the email content.
- To update a draft email, use the "Update Draft Email" endpoint.
- To create a new draft email, use the "Create Draft Email" endpoint.
- To send a draft email, use the "Send Email" endpoint.
- If no draft emails exist for the specified company, an empty array will be returned.

---

# API Endpoint: Send Emails to Companies
---
## Description

This endpoint allows authenticated users to send draft emails to company representatives. The system sends the first unsent draft email for each specified company. The email sending process is asynchronous, with emails being sent in the background.

## HTTP Request

```
Method: POST
URL: https://roboman-gateway.intuicon.ai/api/campaign/emails/send
```

## Headers

```
Accept: application/json
Content-Type: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request_user_id: string (required). The unique identifier of the authenticated user.
```

## Request Body

The request body should be a JSON object containing a list of company IDs:

```
{
  "company_id_list": ["f0d583db-7fac-4f6c-99f7-0d98964234e1", "d13a66b2-7f33-4209-b681-914fed38d498"]
}
```

### Request Fields

- `company_id_list` (array of strings, required): A list of company IDs for which draft emails should be sent.

## Response Body

On successful initiation of the email sending process, the API returns a simple success message:

```
{
  "status": "success"
}
```

### Response Fields

- `status` (string): Confirmation message indicating that the email sending process has been successfully initiated.

## Example cURL Command

```
curl -X 'POST' \
'https://roboman-gateway.intuicon.ai/api/campaign/emails/send' \
-H 'accept: application/json' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-H 'request_user_id: 550e8400-e29b-41d4-a716-************' \
-d '{
  "company_id_list": ["f0d583db-7fac-4f6c-99f7-0d98964234e1", "d13a66b2-7f33-4209-b681-914fed38d498"]
}'
```

## Status Codes

- `200 OK`: The request was successful, and the email sending process has been initiated.
- `400 Bad Request`: The request body is invalid or missing required fields.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to send emails for these companies.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- This endpoint initiates an asynchronous process. The emails are not sent immediately but are queued for background processing.
- For each company in the `company_id_list`, the system:
  1. Finds the first unsent draft email (where `email_sent` is `false`)
  2. Orders draft emails by `draft_type` in ascending order (typically sending initial emails before follow-ups)
  3. Queues the email for sending via the message queue
- If no unsent draft emails are found for a company, an error is logged but the process continues for other companies.
- After an email is sent, its `email_sent` status will be updated to `true` in the database.
- The company's email confirmation status will be updated to reflect that the email has been sent.
- The system uses the "Robert Mandev" sender name by default.
- To track the status of sent emails, use the appropriate endpoints to retrieve company information with their email status.
- Email tracking is enabled, allowing you to see when emails are opened or clicked.

---

# API Endpoint: Start Email Outreach Campaign
---
## Description

This endpoint allows authenticated users to start an email outreach campaign for all companies associated with a specific campaign. The system sends the first unsent draft email for each company in the campaign. The email sending process is asynchronous, with emails being sent in the background.

## HTTP Request

```
Method: POST
URL: https://roboman-gateway.intuicon.ai/api/campaign/emails/start
```

## Headers

```
Accept: application/json
Content-Type: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
```

## Request Body

The request body should be a JSON object containing the campaign ID and optional parameters:

```
{
  "campaign_id": "b011627c-5576-478a-8899-8f956730ace5",
  "company_id_list": ["f0d583db-7fac-4f6c-99f7-0d98964234e1", "d13a66b2-7f33-4209-b681-914fed38d498"],
  "prompt_name": "sales_introduction"
}
```

### Request Fields

- `campaign_id` (string, required): The unique identifier of the campaign for which to start the email outreach.
- `company_id_list` (array of strings, optional): A list of specific company IDs within the campaign for which to send emails. If not provided, emails will be sent to all companies in the campaign.
- `prompt_name` (string, optional): A specific prompt template name to use for email generation if new emails need to be generated. If not provided, the default template will be used.

## Response Body

On successful initiation of the email outreach campaign, the API returns a simple success message:

```
{
  "status": "success"
}
```

### Response Fields

- `status` (string): Confirmation message indicating that the email outreach campaign has been successfully initiated.

## Example cURL Command

```
curl -X 'POST' \
'https://roboman-gateway.intuicon.ai/api/campaign/emails/start' \
-H 'accept: application/json' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-d '{
  "campaign_id": "b011627c-5576-478a-8899-8f956730ace5"
}'
```

## Status Codes

- `200 OK`: The request was successful, and the email outreach campaign has been initiated.
- `400 Bad Request`: The request body is invalid or missing required fields.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to start an email outreach for this campaign.
- `404 Not Found`: The specified campaign ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- This endpoint initiates an asynchronous process. The emails are not sent immediately but are queued for background processing.
- The endpoint retrieves all companies associated with the specified campaign and processes them one by one.
- For each company, the system:
  1. Finds the first unsent draft email (where `email_sent` is `false`)
  2. Orders draft emails by `draft_type` in ascending order (typically sending initial emails before follow-ups)
  3. Queues the email for sending via the message queue
- If no unsent draft emails are found for a company, an error is logged but the process continues for other companies.
- The `follow_up` parameter is set to `true` in the message, indicating that this is part of a campaign follow-up sequence.
- After an email is sent, its `email_sent` status will be updated to `true` in the database.
- The company's email confirmation status will be updated to reflect that the email has been sent.
- The system uses the "Robert Mandev" sender name by default.
- This endpoint is particularly useful for starting or continuing an email outreach campaign for all companies in a campaign at once, rather than selecting specific companies to email.
- To track the status of sent emails, use the appropriate endpoints to retrieve company information with their email status.

---

# API Endpoint: Set Up Automatic Email Outreach
---
## Description

This endpoint allows authenticated users to set up automatic email outreach for companies in a campaign. The system either sends existing draft emails or generates and sends new emails based on campaign information and recipient personality profiles. The email generation and sending processes are asynchronous, with emails being processed in the background.

## HTTP Request

```
Method: POST
URL: https://roboman-gateway.intuicon.ai/api/campaign/emails/outreach/auto
```

## Headers

```
Accept: application/json
Content-Type: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
```

## Request Body

The request body should be a JSON object containing the following fields:

```
{
  "campaign_id": "b011627c-5576-478a-8899-8f956730ace5",
  "company_id_list": ["f0d583db-7fac-4f6c-99f7-0d98964234e1", "d13a66b2-7f33-4209-b681-914fed38d498"],
  "prompt_name": "sales_introduction"
}
```

### Request Fields

- `campaign_id` (string, required): The unique identifier of the campaign for which to set up automatic email outreach.
- `company_id_list` (array of strings, required): A list of company IDs within the campaign for which to set up automatic email outreach.
- `prompt_name` (string, optional): A specific prompt template name to use for email generation if new emails need to be generated. If not provided, the default template will be used.

## Response Body

On successful initiation of the automatic email outreach, the API returns a simple success message:

```
{
  "status": "success"
}
```

### Response Fields

- `status` (string): Confirmation message indicating that the automatic email outreach has been successfully initiated.

## Example cURL Command

```
curl -X 'POST' \
'https://roboman-gateway.intuicon.ai/api/campaign/emails/outreach/auto' \
-H 'accept: application/json' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-d '{
  "campaign_id": "b011627c-5576-478a-8899-8f956730ace5",
  "company_id_list": ["f0d583db-7fac-4f6c-99f7-0d98964234e1", "d13a66b2-7f33-4209-b681-914fed38d498"],
  "prompt_name": "sales_introduction"
}'
```

## Status Codes

- `200 OK`: The request was successful, and the automatic email outreach has been initiated.
- `400 Bad Request`: The request body is invalid or missing required fields.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to set up automatic email outreach for this campaign.
- `404 Not Found`: The specified campaign ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- This endpoint initiates an asynchronous process. The emails are not generated or sent immediately but are queued for background processing.
- For each company in the `company_id_list`, the system:
  1. Checks if automatic email outreach is already enabled for the company (if so, it skips the company)
  2. Determines if draft emails already exist for the company
  3. If no drafts exist, it queues a task to generate and automatically send a new email
  4. If drafts exist, it finds the first unsent draft email and queues it for sending
  5. Updates the company's `auto_email_outreach` flag to `true` to indicate that automatic outreach is enabled
- The email generation process uses the campaign information (core service, key benefits, problem solved, etc.) and the recipient's personality profile to create personalized emails.
- The `auto_send` parameter is set to `true` for newly generated emails, indicating that they should be sent automatically after generation.
- The `follow_up` parameter is set to `true` for existing draft emails, indicating that they are part of a follow-up sequence.
- After setting up automatic outreach, the system will continue to send follow-up emails according to the predefined schedule without requiring further API calls.
- Companies with `auto_email_outreach` already set to `true` will be skipped to avoid duplicate emails.
- This endpoint is particularly useful for setting up "set and forget" email campaigns that will automatically progress through the outreach sequence.

---

# API Endpoint: Disable Automatic Email Outreach
---
## Description

This endpoint allows authenticated users to disable automatic email outreach for specific companies in a campaign. When automatic outreach is disabled, emails for these companies will need to be sent manually rather than being processed automatically by the system.

## HTTP Request

```
Method: PUT
URL: https://roboman-gateway.intuicon.ai/api/campaign/emails/outreach/manual
```

## Headers

```
Accept: application/json
Content-Type: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
```

## Request Body

The request body should be a JSON object containing the following fields:

```
{
  "campaign_id": "b011627c-5576-478a-8899-8f956730ace5",
  "company_id_list": ["f0d583db-7fac-4f6c-99f7-0d98964234e1", "d13a66b2-7f33-4209-b681-914fed38d498"],
  "prompt_name": null
}
```

### Request Fields

- `campaign_id` (string, required): The unique identifier of the campaign containing the companies.
- `company_id_list` (array of strings, required): A list of company IDs for which to disable automatic email outreach.
- `prompt_name` (string, optional): This field is not used by this endpoint but is part of the EmailOutreach schema.

## Response Body

On successful disabling of automatic email outreach, the API returns a simple success message:

```
{
  "status": "success"
}
```

### Response Fields

- `status` (string): Confirmation message indicating that automatic email outreach has been successfully disabled for the specified companies.

## Example cURL Command

```
curl -X 'PUT' \
'https://roboman-gateway.intuicon.ai/api/campaign/emails/outreach/manual' \
-H 'accept: application/json' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-d '{
  "campaign_id": "b011627c-5576-478a-8899-8f956730ace5",
  "company_id_list": ["f0d583db-7fac-4f6c-99f7-0d98964234e1", "d13a66b2-7f33-4209-b681-914fed38d498"]
}'
```

## Status Codes

- `200 OK`: The request was successful, and automatic email outreach has been disabled for the specified companies.
- `400 Bad Request`: The request body is invalid or missing required fields.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to modify email outreach settings for these companies.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- This endpoint sets the `auto_email_outreach` flag to `false` for all companies in the provided `company_id_list`.
- When the `auto_email_outreach` flag is set to `false`, the system will not automatically send emails to these companies as part of the automated outreach process.
- Draft emails that have already been created for these companies will remain in the system but will not be automatically sent.
- To send emails to these companies after disabling automatic outreach, you will need to use the "Send Emails" endpoint to manually trigger the sending process.
- This endpoint is useful when you want to review and manually control the email outreach process for specific companies rather than allowing the system to handle it automatically.
- To re-enable automatic email outreach for these companies, use the "Set Up Automatic Email Outreach" endpoint.

---

# API Endpoint: Respond to Reply Email
---
## Description

This endpoint allows authenticated users to send a response to an email that was received from a company representative. The system uses the provided subject and body to compose a response email, which is then sent to the original sender using an available email sender account.

## HTTP Request

```
Method: POST
URL: https://roboman-gateway.intuicon.ai/api/campaign/emails/reply-mails/respond
```

## Headers

```
Accept: application/json
Content-Type: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
```

## Request Body

The request body should be a JSON object containing the following fields:

```
{
  "received_email_id": "f0d583db-7fac-4f6c-99f7-0d98964234e1",
  "subject": "Re: Your Inquiry About Our Services",
  "body": "<p>Dear John,</p><p>Thank you for your interest in our services. I'd be happy to provide more information...</p>"
}
```

### Request Fields

- `received_email_id` (string, required): The unique identifier of the received email to which you are responding. This is the company_id field in the ReceivedEmail record.
- `subject` (string, required): The subject line for the response email.
- `body` (string, required): The HTML content of the response email body.

## Response Body

On successful sending of the response email, the API returns a simple success message:

```
{
  "status": "success"
}
```

### Response Fields

- `status` (string): Confirmation message indicating that the response email was successfully sent.

## Example cURL Command

```
curl -X 'POST' \
'https://roboman-gateway.intuicon.ai/api/campaign/emails/reply-mails/respond' \
-H 'accept: application/json' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-d '{
  "received_email_id": "f0d583db-7fac-4f6c-99f7-0d98964234e1",
  "subject": "Re: Your Inquiry About Our Services",
  "body": "<p>Dear John,</p><p>Thank you for your interest in our services. I would be happy to provide more information...</p>"
}'
```

## Status Codes

- `200 OK`: The request was successful, and the response email was sent.
- `400 Bad Request`: The request body is invalid or missing required fields.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to send response emails.
- `500 Internal Server Error`: An unexpected error occurred on the server, such as the received email not being found or failure to send the response.

## Notes

- This endpoint is used to respond to emails that have been received from company representatives in response to previous outreach emails.
- The system automatically retrieves the recipient's name and email address from the received email record.
- The response email is sent using an available sender email account from the system's pool of sender accounts.
- The system selects the sender account with the highest number of remaining emails to ensure balanced usage across all sender accounts.
- After sending the response, the system decrements the `remaining_emails` count for the sender account that was used.
- The sender name is automatically set to "Robert Mandev" for consistency in communication.
- If no received email is found with the provided ID, or if the email sending fails, the endpoint will return a 500 error with an appropriate error message.
- For generating AI-assisted response content based on the received email, consider using a separate endpoint or service before calling this endpoint.

---

# API Endpoint: Retrieve Sent Emails for a Campaign
---
## Description

This endpoint allows authenticated users to retrieve all sent emails associated with a specific campaign. The response includes detailed information about each email, including content, recipient details, and tracking status.

## HTTP Request

```
Method: GET
URL: https://roboman-gateway.intuicon.ai/api/campaign/{campaign_id}/emails/sent-mails
```

## Path Parameters

```
campaign_id (UUID, required): The unique identifier of the campaign whose sent emails will be retrieved.
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request_user_id: string (required). The unique identifier of the authenticated user.
```

## Response Body

On successful retrieval, the API returns an array of sent email objects:

```
[
  {
    "internal_id": "a7c5b934-1d23-4fb2-9d3a-8b6f45e21c87",
    "unipile_id": "email_12345",
    "draft_type": 0,
    "subject": "Introducing Our Innovative Solution",
    "body": "<p>Dear Jane,</p><p>I hope this email finds you well...</p>",
    "body_plain": "Dear Jane,\n\nI hope this email finds you well...",
    "from_address": "<EMAIL>",
    "from_info": {
      "display_name": "John Sender",
      "identifier": "<EMAIL>"
    },
    "to_addresses": ["<EMAIL>"],
    "to_info": [
      {
        "display_name": "Jane Recipient",
        "identifier": "<EMAIL>"
      }
    ],
    "email_opened": true,
    "email_replied": false,
    "email_link_clicked": false,
    "message_id": "message_12345",
    "tracking_id": "tracking_12345",
    "reply_to_message_id": null,
    "reply_to_email_id": null,
    "company_id": "f0d583db-7fac-4f6c-99f7-0d98964234e1",
    "created_at": "2023-06-15T10:30:00.000Z",
    "updated_at": "2023-06-15T10:30:00.000Z"
  },
  // Additional sent emails...
]
```

### Response Fields

Each sent email object in the array includes:

- `internal_id` (string): The unique identifier of the sent email in the system.
- `unipile_id` (string): The identifier of the email in the Unipile email service.
- `draft_type` (integer): The type of email (e.g., 0 for initial outreach, 1 for first follow-up).
- `subject` (string): The subject line of the email.
- `body` (string): The HTML content of the email body.
- `body_plain` (string): The plain text content of the email body.
- `from_address` (string): The email address of the sender.
- `from_info` (object): Additional information about the sender.
- `to_addresses` (array): List of recipient email addresses.
- `to_info` (array): Additional information about the recipients.
- `email_opened` (boolean): Whether the email has been opened by the recipient.
- `email_replied` (boolean): Whether the recipient has replied to the email.
- `email_link_clicked` (boolean): Whether any links in the email have been clicked.
- `message_id` (string): The message ID assigned by the email service.
- `tracking_id` (string): The ID used for tracking email events.
- `reply_to_message_id` (string): The message ID of the email this is replying to, if applicable.
- `reply_to_email_id` (string): The email ID this is replying to, if applicable.
- `company_id` (string): The unique identifier of the company associated with this email.
- `created_at` (string): Timestamp when the email was created/sent.
- `updated_at` (string): Timestamp when the email record was last updated.

## Example cURL Command

```
curl -X 'GET' \
'https://roboman-gateway.intuicon.ai/api/campaign/b011627c-5576-478a-8899-8f956730ace5/emails/sent-mails' \
-H 'accept: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-H 'request_user_id: 550e8400-e29b-41d4-a716-************'
```

## Status Codes

- `200 OK`: The request was successful, and the sent emails are returned.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to access this information.
- `404 Not Found`: The specified campaign ID does not exist or no sent emails were found.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- The sent emails are returned in ascending order of creation date, with the oldest emails first.
- The endpoint retrieves all sent emails for all companies associated with the specified campaign.
- Email tracking information (`email_opened`, `email_replied`, `email_link_clicked`) is included in the response, allowing you to monitor the effectiveness of your email outreach.
- To retrieve replies to a specific sent email, use the "Retrieve Reply Emails" endpoint with the `internal_id` of the sent email.
- To delete a sent email record, use the "Delete Sent Email" endpoint.
- This endpoint only retrieves information about emails that have already been sent. To view draft emails that have not yet been sent, use the "Retrieve Draft Emails" endpoint.

---

# API Endpoint: Retrieve Reply Emails for a Sent Email
---
## Description

This endpoint allows authenticated users to retrieve all reply emails that were received in response to a specific sent email. The response includes detailed information about each reply, including content, sender details, and sentiment analysis.

## HTTP Request

```
Method: GET
URL: https://roboman-gateway.intuicon.ai/api/campaign/emails/{sent_email_id}/reply-mails
```

## Path Parameters

```
sent_email_id (UUID, required): The unique identifier (internal_id) of the sent email whose replies will be retrieved.
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request_user_id: string (required). The unique identifier of the authenticated user.
```

## Response Body

On successful retrieval, the API returns an array of received email objects:

```
[
  {
    "internal_id": "b8d694c5-2e34-5fc3-0e4b-9c7f56e32d98",
    "unipile_id": "reply_12345",
    "sent_email_internal_id": "a7c5b934-1d23-4fb2-9d3a-8b6f45e21c87",
    "sent_email_unipile_id": "email_12345",
    "subject": "Re: Introducing Our Innovative Solution",
    "body": "<p>Dear John,</p><p>Thank you for reaching out. I'm interested in learning more...</p>",
    "body_plain": "Dear John,\n\nThank you for reaching out. I'm interested in learning more...",
    "from_address": "<EMAIL>",
    "from_info": {
      "display_name": "Jane Recipient",
      "identifier": "<EMAIL>"
    },
    "to_addresses": ["<EMAIL>"],
    "to_info": [
      {
        "display_name": "John Sender",
        "identifier": "<EMAIL>"
      }
    ],
    "message_id": "reply_message_12345",
    "tracking_id": "tracking_12345",
    "reply_to_message_id": "message_12345",
    "reply_to_email_id": "email_12345",
    "company_id": "f0d583db-7fac-4f6c-99f7-0d98964234e1",
    "sentiment": "positive",
    "emotions": ["interested", "curious", "engaged"],
    "key_takeaways": "The recipient is interested in learning more about the product and would like to schedule a call.",
    "suggested_response": {
      "subject": "Re: Scheduling a Call to Discuss Our Solution",
      "body": "<p>Dear Jane,</p><p>Thank you for your interest in our solution...</p>"
    },
    "explanation": "The recipient shows positive engagement and is ready to move to the next step in the sales process.",
    "created_at": "2023-06-16T14:45:00.000Z"
  },
  // Additional reply emails...
]
```

### Response Fields

Each received email object in the array includes:

- `internal_id` (string): The unique identifier of the received email in the system.
- `unipile_id` (string): The identifier of the email in the Unipile email service.
- `sent_email_internal_id` (string): The internal ID of the original sent email that this is a reply to.
- `sent_email_unipile_id` (string): The Unipile ID of the original sent email.
- `subject` (string): The subject line of the reply email.
- `body` (string): The HTML content of the reply email body.
- `body_plain` (string): The plain text content of the reply email body.
- `from_address` (string): The email address of the sender (the original recipient who replied).
- `from_info` (object): Additional information about the sender.
- `to_addresses` (array): List of recipient email addresses (typically your outreach email address).
- `to_info` (array): Additional information about the recipients.
- `message_id` (string): The message ID assigned by the email service.
- `tracking_id` (string): The ID used for tracking email events.
- `reply_to_message_id` (string): The message ID of the original sent email.
- `reply_to_email_id` (string): The email ID of the original sent email.
- `company_id` (string): The unique identifier of the company associated with this email.
- `sentiment` (string): The analyzed sentiment of the reply (positive, neutral, or negative).
- `emotions` (array): List of emotions detected in the reply.
- `key_takeaways` (string): A summary of the main points from the reply.
- `suggested_response` (object): AI-generated suggested response to this reply, including:
  - `subject` (string): Suggested subject line for the response.
  - `body` (string): Suggested HTML content for the response.
- `explanation` (string): Explanation of the sentiment analysis and response strategy.
- `created_at` (string): Timestamp when the reply was received.

## Example cURL Command

```
curl -X 'GET' \
'https://roboman-gateway.intuicon.ai/api/campaign/emails/a7c5b934-1d23-4fb2-9d3a-8b6f45e21c87/reply-mails' \
-H 'accept: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-H 'request_user_id: 550e8400-e29b-41d4-a716-************'
```

## Status Codes

- `200 OK`: The request was successful, and the reply emails are returned.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to access this information.
- `404 Not Found`: The specified sent email ID does not exist or no replies were found.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- This endpoint retrieves all replies that were received in response to a specific sent email.
- The system automatically performs sentiment analysis on received emails, categorizing them as positive, neutral, or negative.
- The sentiment analysis includes detected emotions, key takeaways, and a suggested response.
- To respond to a reply email, use the "Respond to Reply Email" endpoint with the `internal_id` of the received email.
- If no replies have been received for the specified sent email, an empty array will be returned.
- The AI-generated suggested response can be used as a starting point for crafting your own response to the reply.
- The `company_id` field links the reply to a specific company in your campaign, allowing you to track the conversation in the context of your outreach efforts.

---

# API Endpoint: Delete Sent Email
---
## Description

This endpoint allows authenticated users to delete a specific sent email from the system. When a sent email is deleted, all associated data for that email, including any received replies, will be permanently removed from the system.

## HTTP Request

```
Method: DELETE
URL: https://roboman-gateway.intuicon.ai/api/campaign/emails/{sent_email_id}/delete
```

## Path Parameters

```
sent_email_id (UUID, required): The unique identifier (internal_id) of the sent email to delete.
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request_user_id: string (required). The unique identifier of the authenticated user.
```

## Response Body

On successful deletion, the API returns a simple success message:

```
{
  "status": "success"
}
```

### Response Fields

- `status` (string): Confirmation message indicating that the sent email was successfully deleted.

## Example cURL Command

```
curl -X 'DELETE' \
'https://roboman-gateway.intuicon.ai/api/campaign/emails/a7c5b934-1d23-4fb2-9d3a-8b6f45e21c87/delete' \
-H 'accept: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-H 'request_user_id: 550e8400-e29b-41d4-a716-************'
```

## Status Codes

- `200 OK`: The request was successful, and the sent email was deleted.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to delete this sent email.
- `404 Not Found`: The specified sent email ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- This operation cannot be undone. Once a sent email is deleted, all associated data is permanently removed.
- Due to the database's cascade delete configuration, deleting a sent email will also delete all related records, including:
  - Any received replies to this email
  - Email tracking information
  - Any other data associated with this email
- The system verifies that the sent email exists before performing the deletion.
- This endpoint is useful for removing emails that were sent in error or are no longer relevant to your campaign.
- Deleting a sent email does not affect the actual email that was delivered to the recipient's inbox; it only removes the record from your system.
- If you need to retrieve information about a sent email before deleting it, use the "Retrieve Sent Emails for a Campaign" endpoint.

---

# API Endpoint: Retrieve Email Outreach Statistics for a Campaign
---
## Description

This endpoint allows authenticated users to retrieve comprehensive email outreach statistics for a specific campaign. The statistics include the total number of prospects, as well as counts and rates for sent emails, opened emails, and replied emails.

## HTTP Request

```
Method: GET
URL: https://roboman-gateway.intuicon.ai/api/campaign/{campaign_id}/emails/outreach-stats
```

## Path Parameters

```
campaign_id (UUID, required): The unique identifier of the campaign for which to retrieve email outreach statistics.
```

## Headers

```
Accept: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
```

## Response Body

On successful retrieval, the API returns a JSON object containing detailed statistics:

```
{
  "campaign_id": "b011627c-5576-478a-8899-8f956730ace5",
  "prospects_count": 50,
  "sent": {
    "count": 45,
    "rate": 0.9
  },
  "opened": {
    "count": 30,
    "rate": 0.67
  },
  "replied": {
    "count": 15,
    "rate": 0.33
  },
  "interested": {
    "count": 0,
    "rate": 0.0
  }
}
```

### Response Fields

- `campaign_id` (string): The unique identifier of the campaign.
- `prospects_count` (integer): The total number of companies/prospects in the campaign.
- `sent` (object): Statistics about sent emails:
  - `count` (integer): The number of unique companies that have been sent at least one email.
  - `rate` (float): The percentage of companies that have been sent at least one email (count/prospects_count).
- `opened` (object): Statistics about opened emails:
  - `count` (integer): The number of unique companies that have opened at least one email.
  - `rate` (float): The percentage of companies that have opened at least one email out of those that were sent emails (count/sent.count).
- `replied` (object): Statistics about replied emails:
  - `count` (integer): The number of unique companies that have replied to at least one email.
  - `rate` (float): The percentage of companies that have replied to at least one email out of those that were sent emails (count/sent.count).
- `interested` (object): Statistics about interested prospects (currently not implemented):
  - `count` (integer): Always 0 in the current implementation.
  - `rate` (float): Always 0.0 in the current implementation.

## Example cURL Command

```
curl -X 'GET' \
'https://roboman-gateway.intuicon.ai/api/campaign/b011627c-5576-478a-8899-8f956730ace5/emails/outreach-stats' \
-H 'accept: application/json' \
-H 'Authorization: Bearer <your_access_token>'
```

## Status Codes

- `200 OK`: The request was successful, and the statistics are returned.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to access this information.
- `404 Not Found`: The specified campaign ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server.

## Notes

- The statistics are calculated based on unique companies, not individual emails. For example, if a company has been sent multiple emails but has only opened one, it counts as 1 in the "opened" count.
- The "sent" rate is calculated as the percentage of companies in the campaign that have been sent at least one email.
- The "opened" and "replied" rates are calculated as percentages of companies that were sent emails, not of all companies in the campaign.
- The "interested" statistics are included in the response structure but are not currently implemented (always return 0 and 0.0).
- Email opening is tracked using tracking pixels, which may not be 100% accurate as some email clients block tracking pixels by default.
- This endpoint is useful for monitoring the performance of your email outreach campaigns and identifying areas for improvement.
- For more detailed information about individual sent emails and replies, use the "Retrieve Sent Emails for a Campaign" and "Retrieve Reply Emails for a Sent Email" endpoints.

---

# API Endpoint: Send LinkedIn Messages
---
## Description

This endpoint allows authenticated users to send LinkedIn messages to company representatives. The system sends the first unsent draft LinkedIn message for each specified company. The message sending process is asynchronous, with messages being sent in the background.

## HTTP Request

```
Method: POST
URL: https://roboman-gateway.intuicon.ai/api/campaign/linkedin/messages/send
```

## Headers

```
Accept: application/json
Content-Type: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request_user_id: string (required). The unique identifier of the authenticated user.
```

## Request Body

The request body should be a JSON object containing a list of company IDs:

```
{
  "company_id_list": ["f0d583db-7fac-4f6c-99f7-0d98964234e1", "d13a66b2-7f33-4209-b681-914fed38d498"]
}
```

### Request Fields

- `company_id_list` (array of strings, required): A list of company IDs for which draft LinkedIn messages should be sent.

## Response Body

On successful initiation of the LinkedIn message sending process, the API returns a success message with details:

```
{
  "status": "success",
  "detail": "2 messages are set to be sent out of 3 companies as requested"
}
```

### Response Fields

- `status` (string): Confirmation message indicating that the LinkedIn message sending process has been successfully initiated.
- `detail` (string): Additional information about how many messages will be sent compared to how many companies were requested.

## Example cURL Command

```
curl -X 'POST' \
'https://roboman-gateway.intuicon.ai/api/campaign/linkedin/messages/send' \
-H 'accept: application/json' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-H 'request_user_id: 550e8400-e29b-41d4-a716-************' \
-d '{
  "company_id_list": ["f0d583db-7fac-4f6c-99f7-0d98964234e1", "d13a66b2-7f33-4209-b681-914fed38d498"]
}'
```

## Status Codes

- `200 OK`: The request was successful, and the LinkedIn message sending process has been initiated.
- `400 Bad Request`: The request body is invalid or missing required fields.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to send LinkedIn messages for these companies.
- `500 Internal Server Error`: An unexpected error occurred on the server, such as the user's LinkedIn account not being connected to Unipile.

## Notes

- This endpoint initiates an asynchronous process. The LinkedIn messages are not sent immediately but are queued for background processing.
- The user must have their LinkedIn account connected to the Unipile service. If not, the endpoint will return an error.
- For each company in the `company_id_list`, the system:
  1. Finds the first unsent draft LinkedIn message (where `linkedin_sent` is `false`)
  2. Orders draft messages by `draft_type` in ascending order (typically sending initial messages before follow-ups)
  3. Queues the message for sending via the message queue
- The system will skip companies in the following cases:
  1. If the company has already replied to a previous LinkedIn message (linkedin_message_status_id = 4)
  2. If there are no unsent draft messages for the company
  3. If the company is already set for automatic email outreach
- The response includes a count of how many messages will actually be sent, which may be less than the number of companies requested due to the skipping conditions.
- The LinkedIn messages are sent using the authenticated user's LinkedIn account via the Unipile integration.
- If a LinkedIn message is too long to be sent directly, the system will automatically summarize it to fit within LinkedIn's message length limits.
- After a message is sent, its `linkedin_sent` status will be updated to `true` in the database.
- The system uses the "send_linkedin_outreach" task to handle the actual sending of the messages.
- If the `follow_up` parameter is set to `true` in the background task, the system will automatically schedule the next follow-up message based on the `day_to_send` value.

---

# API Endpoint: Set Up Automatic LinkedIn Outreach
---
## Description

This endpoint allows authenticated users to set up automatic LinkedIn outreach for companies in a campaign. The system either sends existing draft LinkedIn messages or generates and sends new messages based on campaign information and recipient profiles. The message generation and sending processes are asynchronous, with messages being processed in the background.

## HTTP Request

```
Method: POST
URL: https://roboman-gateway.intuicon.ai/api/campaign/linkedin/outreach/auto
```

## Headers

```
Accept: application/json
Content-Type: application/json
Authorization: Bearer token (required). This should be the access token obtained from the login endpoint.
request_user_id: string (required). The unique identifier of the authenticated user.
```

## Request Body

The request body should be a JSON object containing the following fields:

```
{
  "campaign_id": "b011627c-5576-478a-8899-8f956730ace5",
  "company_id_list": ["f0d583db-7fac-4f6c-99f7-0d98964234e1", "d13a66b2-7f33-4209-b681-914fed38d498"],
  "prompt_name": "sales_introduction"
}
```

### Request Fields

- `campaign_id` (string, required): The unique identifier of the campaign for which to set up automatic LinkedIn outreach.
- `company_id_list` (array of strings, required): A list of company IDs within the campaign for which to set up automatic LinkedIn outreach.
- `prompt_name` (string, optional): A specific prompt template name to use for message generation if new messages need to be generated. If not provided, the default template will be used.

## Response Body

On successful initiation of the automatic LinkedIn outreach, the API returns a simple success message:

```
{
  "status": "success"
}
```

### Response Fields

- `status` (string): Confirmation message indicating that the automatic LinkedIn outreach has been successfully initiated.

## Example cURL Command

```
curl -X 'POST' \
'https://roboman-gateway.intuicon.ai/api/campaign/linkedin/outreach/auto' \
-H 'accept: application/json' \
-H 'Content-Type: application/json' \
-H 'Authorization: Bearer <your_access_token>' \
-H 'request_user_id: 550e8400-e29b-41d4-a716-************' \
-d '{
  "campaign_id": "b011627c-5576-478a-8899-8f956730ace5",
  "company_id_list": ["f0d583db-7fac-4f6c-99f7-0d98964234e1", "d13a66b2-7f33-4209-b681-914fed38d498"],
  "prompt_name": "sales_introduction"
}'
```

## Status Codes

- `200 OK`: The request was successful, and the automatic LinkedIn outreach has been initiated.
- `400 Bad Request`: The request body is invalid or missing required fields.
- `401 Unauthorized`: Authentication failed or token is invalid.
- `403 Forbidden`: The authenticated user does not have permission to set up automatic LinkedIn outreach for this campaign.
- `404 Not Found`: The specified campaign ID does not exist.
- `500 Internal Server Error`: An unexpected error occurred on the server, such as the user's LinkedIn account not being connected to Unipile.

## Notes

- This endpoint initiates an asynchronous process. The LinkedIn messages are not generated or sent immediately but are queued for background processing.
- The user must have their LinkedIn account connected to the Unipile service. If not, the endpoint will return an error.
- For each company in the `company_id_list`, the system:
  1. Checks if automatic LinkedIn outreach is already enabled for the company (if so, it skips the company)
  2. Determines if draft messages already exist for the company
  3. If no drafts exist, it queues a task to generate and automatically send new LinkedIn messages
  4. If drafts exist, it finds the first unsent draft message and queues it for sending
  5. Updates the company's `auto_linkedin_outreach` flag to `true` to indicate that automatic outreach is enabled
- The message generation process uses the campaign information (core service, key benefits, problem solved, etc.) and the recipient's profile to create personalized LinkedIn messages.
- The `auto_send_linkedin` parameter is set to `true` for newly generated messages, indicating that they should be sent automatically after generation.
- The `follow_up` parameter is set to `true` for existing draft messages, indicating that they are part of a follow-up sequence.
- After setting up automatic outreach, the system will continue to send follow-up messages according to the predefined schedule without requiring further API calls.
- Companies with `auto_linkedin_outreach` already set to `true` will be skipped to avoid duplicate messages.
- This endpoint is particularly useful for setting up "set and forget" LinkedIn outreach campaigns that will automatically progress through the outreach sequence.
- If a LinkedIn message is too long to be sent directly, the system will automatically summarize it to fit within LinkedIn's message length limits.

---

