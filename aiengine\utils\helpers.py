from database.database import get_session
from database.models import Model, PromptType, SystemPrompt
from sqlalchemy.future import select
from sqlalchemy import update
from sqlalchemy.ext.asyncio import AsyncSession

async def get_email_gen_prompt(session: AsyncSession, prompt_name: str=None) -> str:
    if prompt_name is not None:
        query = select(SystemPrompt).filter(SystemPrompt.prompt_type==PromptType.EMAIL_GEN, SystemPrompt.prompt_name==prompt_name)
    else:
        query = select(SystemPrompt).filter(SystemPrompt.prompt_type==PromptType.EMAIL_GEN, SystemPrompt.is_disabled==False)
    result = await session.execute(query)
    prompt = result.scalars().first()
    return prompt.prompt_content