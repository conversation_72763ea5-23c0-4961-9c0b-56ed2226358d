from pydantic import BaseModel, Field
from pydantic.networks import EmailStr
from typing import Optional, List
from uuid import UUID

class CompanyResponse(BaseModel):
    company_id: UUID
    company_name: str
    industry: str
    rep_id: str
    rep_name: str
    rep_email: EmailStr
    rep_linkedin_address: str
    rep_ocean_label: List[str]
    rep_disc_label: List[str]

    class Config:
        orm_mode = True

class CompanyEmailUpdate(BaseModel):
    company_id: str
    content_subject : Optional[str] = None
    content : Optional[str] = None 
    email_confirmation_status_id : Optional[int] = Field(default=2, ge=1, le=5) 

    class Config:
        orm_mode = True


class CompanyLinkedInMsgUpdate(BaseModel):
    company_id : str
    linkedin_message : Optional[str] = None 
    
    class Config:
        orm_mode = True


class CompanyAddManual(BaseModel):
    company_name: str 
    company_email: Optional[EmailStr] = None
    company_linkedin: Optional[str] = None
    industry: str    
    rep_name: str
    rep_email: Optional[EmailStr] = None
    rep_linkedin_address: Optional[str] = None
    
