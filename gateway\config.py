from functools import lru_cache
import secrets
from pydantic import BaseSettings


# load_dotenv(".env")

class Settings(BaseSettings):
    SERVER_PORT: int = 8000
    
    API_V2_STR: str = ""
    SECRET_KEY: str = secrets.token_urlsafe(32)

    # 60 seconds * 60 minutes * 24 hours * 8 days = 8 days
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 60 * 24 * 8
    ACCESS_TOKEN_URL = "/api/token"
    BACKEND_CORS_ORIGINS: list = ["localhost:3000"]

    PROJECT_NAME: str

    #AWS
    # USER_SERVICE_URL = "http://user-svc"
    # CAMPAIGN_SERVICE_URL = "http://campaign-svc"
    # AI_SERVICE_URL = "http://aiengine-svc"

    # local devevlopment
    USER_SERVICE_URL = "http://roboman-user:8000"
    CAMPAIGN_SERVICE_URL = "http://roboman-campaign:8000"
    AI_SERVICE_URL = "http://roboman-aiservice:8000"    

    JWT_SECRET: str
    JWT_ALGORITHM: str
    GATEWAY_TIMEOUT: int = 99999999
    
    class Config:
        env_file = ".env"
        case_sensitive = True

@lru_cache()
def get_settings():
    return Settings()

settings = get_settings()