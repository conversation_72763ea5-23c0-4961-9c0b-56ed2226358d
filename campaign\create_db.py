from database.database import POSTGRES_DATABASEURL, async_engine, SessionFactory, get_session
from database.models import EmailConfirmationStatus, LinkedInMessageStatus
from sqlalchemy import insert, text, delete
from utils.migrate import run_migration
import asyncio
from sqlalchemy_utils import database_exists, create_database
from loguru import logger


async def init_data():
    async for session in get_session():
        # Create an insert statement
        reset_sequence_stmt = text("ALTER SEQUENCE email_confirmation_status_status_id_seq RESTART WITH 1")
        await session.execute(reset_sequence_stmt)

        stmt = delete(EmailConfirmationStatus)
        await session.execute(stmt)

        stmt = insert(EmailConfirmationStatus).values([
            {'status_value': "not started"},
            {'status_value': "waiting to review"},
            {'status_value': "reviewed"},
            {'status_value': "sent"},
            {'status_value': "opened"},
            {'status_value': "replied"},
            {'status_value': "responded"},     
            {'status_value': "generating"},
            {'status_value': "generate failed"},
            {'status_value': "sending"},
            {'status_value': "send failed"},
            {'status_value': "clicked"}

        ])
        # Execute the insert statement
        await session.execute(stmt)

        stmt = delete(LinkedInMessageStatus)
        await session.execute(stmt)
        reset_sequence_stmt = text("ALTER SEQUENCE linkedin_message_status_status_id_seq RESTART WITH 1")
        await session.execute(reset_sequence_stmt)
        stmt = insert(LinkedInMessageStatus).values([
            {'status_value': "not started"},
            {'status_value': "sent"},
            {'status_value': "opened"},
            {'status_value': "replied"},
            {'status_value': "responded"},
            {'status_value': "sending"},
            {'status_value': "send failed"}
        ])
        await session.execute(stmt)

        # Commit the transaction
        await session.commit()


if __name__=='__main__':
    if not database_exists(POSTGRES_DATABASEURL):
        create_database(POSTGRES_DATABASEURL)
    run_migration()
    # try:
    #     logger.info('Init email status value...')
    #     asyncio.run(init_data())
    #     logger.success('Done!')
    # except Exception as e:
    #     logger.error(f'{e}')
    #     pass
