*{
    font-family: "Jost", sans-serif;
  }
  .same-para-size{
      color: #ccc;
      font-size: 20px;
  }
  .process-cardmain{
      height: 100%;
      position: relative;
  }
  .process-card__btnsupermain{
      position: absolute;
      bottom: 18px;
  }
  
  .cta-section {
      background-color: #0046ba;
      color: white;
      text-align: center;
      padding: 40px 20px;
      position: relative;
      /*min-height: 100vh;*/
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .cta-container {
      max-width: 900px;
      margin: auto;
    }
    
    .cta-subheading {
      font-size: 20px;
      color: #c3d4f5;
      margin-bottom: 20px;
    }
    
    .cta-heading {
      font-size: 50px;
      font-weight: 400;
      line-height: 1.2em;
      max-width: 574px;
      letter-spacing: -1.5px;
      margin-bottom: 40px;
      color: white;
    }
    
    .cta-button {
      background-color: #ffbf47;
      color: #000;
      font-size: 18px;
      padding: 15px 30px;
      border: none;
      border-radius: 10px;
      cursor: pointer;
      font-weight: 500;
      transition: background 0.3s ease;
      text-decoration: none;
    }
    
    .cta-button:hover {
      background-color: #e6a52d;
    }
    
  
    @media (max-width: 768px) {
      .cta-heading {
        font-size: 28px;
      }
      .why-intuicon-section .intro h2{
          font-size: 28px !Important;
      }
      .heading-condi{
          font-size: 28px !Important;
      }
      .cta-button {
        font-size: 16px;
        padding: 12px 24px;
      }
      .cta-section{
         min-height: unset;
      }
    }
    
  
  
  
    .why-intuicon-section .intro {
      /* max-width: 600px; */
      text-align: center;
      margin-bottom: 2rem;
    }
  
    .why-intuicon-section .intro h2 {
      font-size: 48px;
      color: #FFFFFF;
      font-weight: bold;
      margin-bottom: 1rem;
    }
  
    .why-intuicon-section .intro p {
      color: #ccc;
      font-size: 20px;
    }
  
    .cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
      gap: 1.5rem;
    }
  
    .card {
      background-color: #111;
      padding: 1.5rem;
      border-radius: 10px;
      text-align: center;
      border-top: 4px solid transparent;
      transition: transform 0.3s ease;
    }
  
    .card:hover {
      transform: translateY(-5px) !important;
    }
  
    .card img {
      width: 100px;
      margin-bottom: 1rem;
    }
  
    .card h3 {
      font-size: 20px;
      margin-bottom: 0.5rem;
      color: #FFFFFF;
    }
  
    .card p {
      font-size: 20px;
      color: #ccc;
    }
  
    .green-border { border-top-color: #00c851; }
    .yellow-border { border-top-color: #ffbb33; }
    .red-border { border-top-color: #ff4444; }
    .blue-border { border-top-color: #33b5e5; }
  
  
  
  
  
  .terms-boxmain {
        background-color: #1a1a1a; /* Slight contrast from background */
        padding: 25px;
        border-radius: 10px;
        margin-bottom: 20px;
      }
      
      .logo-mainsuper {
              height: 100%;
      max-height: 61px;
      }
      
      .heading-condi{
          font-size: 42px;
      }
      
      
      .terms-boxmain p a{
          color: #044AAD;
          text-decoration: none;
      }
      
      .terms-boxmain p{
          font-size: 20px;
          text-align: justify;
      }
      
      .last-para-fix{
      margin-top: 30px;
      width: 100%;
      display: flex;
      justify-content: center;
      text-align: center;
      }
      
      .last-para-fix p{
      margin: 0;
      max-width: 800px;
      }
      .aux-sale-amount{
      text-decoration: line-through;
      font-size: 14px;
      }
      .navbarrobo{
          background-color: white;
      }
      
      .super-footer-rob{
          background: white;
      }
      
      .same-color-all{
          color: #FFFFFF ;
      }