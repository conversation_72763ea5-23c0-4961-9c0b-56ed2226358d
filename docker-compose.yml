version: '3.7'

services:
  roboman-db:
    container_name: roboman-db
    image: pgvector/pgvector:pg15
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}
      PGDATA: /data/postgres
    ports:
      - "8811:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  roboman-elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.5.1
    container_name: roboman-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=true
      - ELASTIC_PASSWORD=**********
    ports:
      - 9200:9200  

  roboman-servicebus:
    container_name: roboman-servicebus
    image: roboman-servicebus:latest
    build:
      context: ./rabbitmq_volumes
      dockerfile: Dockerfile
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_DEFAULT_USER:-admin}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_DEFAULT_PASS:-admin}
    ports:
      - 5673:5672
      - 15673:15672
    restart: unless-stopped
    volumes:
      - /absolute/path/to/rabbitmq_volumes:/var/lib/rabbitmq   
    depends_on:
      roboman-db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 10s
      retries: 5
      timeout: 5s

  roboman-gateway:
    image: roboman-gateway:latest
    command: sh -c "python main.py"
    build:
      context: ./gateway
      dockerfile: Dockerfile
    env_file:
        - ./gateway/.env
    volumes:
      - ./gateway:/app
    ports:
      - 8881:8000
    depends_on:
      roboman-aiservice:
        condition: service_healthy

  roboman-user:
    image: roboman-user:latest
    command: sh -c "python create_db.py && python main.py"
    build:
      context: ./user
      dockerfile: Dockerfile
    env_file:
        - ./user/.env
    volumes:
      - ./user:/app
    ports:
      - 8882:8000
    depends_on:
      roboman-aiservice:
        condition: service_healthy

  roboman-campaign:
    image: roboman-campaign:latest
    command: sh -c "python create_db.py && python main.py"
    build:
      context: ./campaign
      dockerfile: Dockerfile
    env_file:
        - ./campaign/.env
    volumes:
      - ./campaign:/app
    ports:
      - 8883:8000
    depends_on:
      roboman-aiservice:
        condition: service_healthy

  roboman-aiservice:
    image: roboman-aiservice:latest
    # command: sh -c "python main.py"
    command: sh -c "python create_db.py && python main.py"
    build:
      context: ./aiengine
      dockerfile: Dockerfile
    env_file:
        - ./aiengine/.env
    volumes:
      - ./aiengine:/app
    ports:
      - 8884:8000
    depends_on:
      roboman-servicebus:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/api/health || exit 1"]
      interval: 10s
      timeout: 15s
      retries: 3
      start_period: 120s
