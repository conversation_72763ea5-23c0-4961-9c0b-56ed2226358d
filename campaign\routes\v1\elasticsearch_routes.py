from database.database import get_session
from message_bus.rpc_client import R<PERSON><PERSON><PERSON>
from utils.elasticsearch_utils.get_elasticsearch import get_elastic
from utils.elasticsearch_utils.elastic_query_test import string_matching_search, semantic_search, search_primary_rep, search_matching_companies, test_string_matching_search, hybrid_search
from utils.unipile_utils.unipile_client import EmailClient, LinkedInClient
from message_bus.task_publisher import publish_task
from shared_state import app_state
from core.config import settings
from schema.campaign_schema import (CampaignCreate, 
                                    CampaignDetails, CampaignContentUpdate, 
                                    CampaignSendLinkedInMessage, CampaignDetailsUpdate,
                                    TestScheduleJob, CompanyMatching)
from schema.company_schema import (CompanyEmailUpdate, CompanyAddManual, 
                                   CompanyLinkedInMsgUpdate)
from schema.rep_schema import (RepUpdate)

from schema.webhooks_schema import (WebhooksEmailTracking, WebhooksNewEmail)

from schema.outreach_schema import (<PERSON>ailGeneration, EmailSend,
                                    CreateDraftEmail,EmailOutreach,
                                    UpdateDraftEmail, SendEmailRequest, 
                                    RespondEmail)

from database.models import (Campaign, Company, 
                             Representative, SentEmail, 
                             ReceivedEmail, Draft,
                             EmailConfirmationStatus, SenderEmail,
                             HumanticProfile)

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import update, delete, func, distinct
from sqlalchemy.future import select
from sqlalchemy.orm import aliased
from fastapi import APIRouter, Header, Depends, Query
from fastapi import HTTPException, status, Request
from fastapi import status, File, UploadFile
from sqlalchemy.future import select
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk, BulkIndexError
from typing import List
import uuid
import pandas as pd
from io import StringIO
import requests
from loguru import logger
import asyncio
import json

router = APIRouter(
    prefix = "/api",
    tags=['Elasticsearch Test'],
    responses={404: {'description': 'Not found'}},
)


#region MATCHING

@router.post("/elasticsearch-test/match/test")
async def test_matching_companies(
    uploaded_files: UploadFile = File(...),
    es: Elasticsearch = Depends(get_elastic)
):
    
    # Check if file is a JSON file
    if not uploaded_files.filename.endswith('.json'):
        raise HTTPException(status_code=415, detail="File must be a JSON")

    # Read file contents
    contents = await uploaded_files.read()

    try:
        # Convert bytes to dict
        data = json.loads(contents)
 
        search_results = test_string_matching_search(es, data)

        companies = []
        for company in search_results:
            companies.append({
                'company_name': company['Company Name'],
                'industry': company['Company Industry'],
                'company_linkedin': company['Company Linkedin Url'],
                'location_locality': company['Company Location Locality'],
                'location_region': company['Company Location Region'],
                'location_country': company['Company Location Country'],
                'location_continent': company['Company Location Continent']
            })

        return companies
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON format")       
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/elasticsearch-test/match/string-matching")
async def string_matching_companies(
    company_matching: CompanyMatching, 
    db: AsyncSession = Depends(get_session),
    es: Elasticsearch = Depends(get_elastic)
):
    try:
        search_results = string_matching_search(
            es, 
            industry_list=company_matching.industry,
            location=company_matching.location,
            search_size=100
            )

        companies = []
        for company in search_results:
            rep_name = company['Full name'].title()
            rep_emails = company['Emails']
            companies.append({
                'company_name': company['Company Name'],
                'industry': company['Company Industry'],
                'company_linkedin': company['Company Linkedin Url'],
                'location_locality': company['Company Location Locality'],
                'location_region': company['Company Location Region'],
                'location_country': company['Company Location Country'],
                'location_continent': company['Company Location Continent']
            })

        return companies
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/elasticsearch-test/match/hybrid/{embedding_dim}")
async def hybrid_matching_companies(
    company_matching: CompanyMatching,
    embedding_dim: int,
    db: AsyncSession = Depends(get_session),
    es: Elasticsearch = Depends(get_elastic)
):
    try:

        rpc = await RPCClient().connect()
        function_name = "get_text_embedding_test"
        texts_to_embed = company_matching.industry + [company_matching.location]
        texts_to_embed = [text_.lower() for text_ in texts_to_embed]

        response = await rpc.call(service="aiengine.*", function_name=function_name, params={"text_list": texts_to_embed, "embedding_dim": embedding_dim})
        response_body = json.loads(response)
        result = response_body.get("result")

        if result == "error":
            raise Exception(
                "Error from RPC server: " + response_body.get("detail")
            )

        industry_list_embedding = result["embeddings_list"][:-1]
        location_embedding = result["embeddings_list"][-1]

        search_results = hybrid_search(
            es_client=es, 
            industry_list=company_matching.industry,
            location=company_matching.location,
            location_embedding=location_embedding,
            search_size=company_matching.search_size,
            location_similarity_threshold=company_matching.location_similarity_threshold,
            existing_company_urls=[]
        )

        companies = []
        for company in search_results:
            rep_name = company['Full name'].title()
            rep_emails = company['Emails']
            companies.append({
                'company_name': company['Company Name'],
                'industry': company['Company Industry'],
                'company_linkedin': company['Company Linkedin Url'],
                'location_locality': company['Company Location Locality'],
                'location_region': company['Company Location Region'],
                'location_country': company['Company Location Country'],
                'location_continent': company['Company Location Continent']

            })
        return companies

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )    


@router.post("/elasticsearch-test/match/semantic/{embedding_dim}")
async def semantic_matching_companies(
    company_matching: CompanyMatching, 
    embedding_dim: int,
    db: AsyncSession = Depends(get_session),
    es: Elasticsearch = Depends(get_elastic)
):
    try:

        rpc = await RPCClient().connect()
        function_name = "get_text_embedding_test"
        texts_to_embed = company_matching.industry + [company_matching.location]
        texts_to_embed = [text_.lower() for text_ in texts_to_embed]

        response = await rpc.call(service="aiengine.*", function_name=function_name, params={"text_list": texts_to_embed, "embedding_dim": embedding_dim})
        response_body = json.loads(response)
        result = response_body.get("result")

        if result == "error":
            raise Exception(
                "Error from RPC server: " + response_body.get("detail")
            )

        industry_list_embedding = result["embeddings_list"][:-1]
        location_embedding = result["embeddings_list"][-1]

        search_results = semantic_search(
            es_client=es, 
            location=company_matching.location,
            industry_list_embedding=industry_list_embedding, 
            location_embedding=location_embedding,
            search_size=company_matching.search_size,
            industry_similarity_threshold=company_matching.industry_similarity_threshold,
            location_similarity_threshold=company_matching.location_similarity_threshold,
            industry_weight=company_matching.industry_weight,
            location_weight=company_matching.location_weight,
            existing_company_urls=[]
        )

        companies = []
        for company in search_results:
            rep_name = company['Full name'].title()
            rep_emails = company['Emails']
            companies.append({
                'company_name': company['Company Name'],
                'industry': company['Company Industry'],
                'company_linkedin': company['Company Linkedin Url'],
                'location_locality': company['Company Location Locality'],
                'location_region': company['Company Location Region'],
                'location_country': company['Company Location Country'],
                'location_continent': company['Company Location Continent']

            })
        return companies

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )    

@router.post("/elasticsearch-test/match/{embedding_dim}")
async def get_matching_companies(
    embedding_dim: int,
    company_matching: CompanyMatching, 
    db: AsyncSession = Depends(get_session),
    es: Elasticsearch = Depends(get_elastic)
):
    try:
        search_results = await search_matching_companies(
            es, 
            industry_list=company_matching.industry, 
            location=company_matching.location, 
            search_size=company_matching.search_size,
            industry_similarity_threshold=company_matching.industry_similarity_threshold,
            location_similarity_threshold=company_matching.location_similarity_threshold,
            industry_weight=company_matching.industry_weight,
            location_weight=company_matching.location_weight,
            embedding_dim = embedding_dim
        )

        companies = []
        for company in search_results:
            rep_name = company['Full name'].title()
            rep_emails = company['Emails']
            companies.append({
                'company_name': company['Company Name'],
                'industry': company['Company Industry'],
                'company_linkedin': company['Company Linkedin Url'],
                'rep_name': rep_name,
                'rep_email' : rep_emails.split(',')[-1].strip() if rep_emails else None
            })
        return companies
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )


#endregion   






#region ADMIN

    
@router.post("/elasticsearch-test/ingest/{embedding_dim}")
async def ingest_companies_test(
    embedding_dim: int,
    uploaded_files: UploadFile = File(...),
    db: AsyncSession = Depends(get_session),
    es: Elasticsearch = Depends(get_elastic),
):
    if not uploaded_files.filename.endswith('.csv'):
        return {"error": "File must be a CSV"}
    
    try:
        # Read the contents of the file
        contents = await uploaded_files.read()
        # Decode bytes to string
        str_file = contents.decode()
        # Create a StringIO object for pandas to read
        csv_file = StringIO(str_file)

        # df = pd.read_csv(csv_file, dtype=str, encoding='utf-8', keep_default_na=False)
        for chunk in pd.read_csv(csv_file, dtype=str, encoding='utf-8', keep_default_na=False, chunksize=2000):
            df_json_str = chunk.to_json(orient='records')
            await publish_task(task_type="ingest_data_test", message_body={"df_json_str": df_json_str, "embedding_dim": embedding_dim}, connection_pool=app_state["rabbitmq_conn_pool"])

        return {
            "status": "success"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str
        )       

@router.get("/elasticsearch-test/records/{page_size}/{from_}")
async def get_es_records(
    page_size: int,
    from_: int,
    es: Elasticsearch = Depends(get_elastic)
):
    try:
        query={
            "query": {"match_all": {}},
            "_source": {
                "excludes": [
                    "Company Industry embedding",
                    "Company Location Name embedding",
                    "Company Location Continent embedding"
                ]
            }            
        }
        search_result = es.search(index='test-index', body=query, from_=from_, size=page_size).body['hits']['hits']
        result = [item['_source'] for item in search_result]  
        return result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.delete("/elasticsearch-test/remove-index")
async def remove_es_index(
    es: Elasticsearch = Depends(get_elastic)
):
    try:
        response = es.indices.delete(index="test-index")
        return {
            'status': 'success',
            'detail': f"Index `test-index` deleted successfully."
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

#endregion




elasticsearch_routes = router
