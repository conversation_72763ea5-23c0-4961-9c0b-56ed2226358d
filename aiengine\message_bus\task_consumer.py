import asyncio
import aio_pika
from loguru import logger
from aio_pika.abc import AbstractIncomingMessage


from core.config import settings
from message_bus.tasks.nlp_tasks import generate_email, generate_email_v2
from message_bus.rabbitmq_connection import RabbitMQConnectionPool

# List of task types (queue names)
TASK_TYPES = ["generate_email", "generate_email_v2"]

PROCESS_TASK_FUNCTIONS = {
    "generate_email": generate_email,
    "generate_email_v2": generate_email_v2
}



RABBIT_MQ_DSN = f"amqp://{settings.RABBITMQ_DEFAULT_USER}:{settings.RABBITMQ_DEFAULT_PASS}@{settings.RABBITMQ_HOST}:{settings.RABBITMQ_PORT}/"

async def consume_task(task_type, connection_pool: RabbitMQConnectionPool):
    logger.info(f"[CONSUMER] Starting consumer for {task_type}...")
    try:
        # Acquire a channel from the pool using async with
        async with connection_pool.channel_pool.acquire() as channel:
            try:
                # Delete existing queue if it exists
                try:
                    await channel.queue_delete(task_type)
                    logger.info(f"[CONSUMER] Deleted existing queue: {task_type}")
                except Exception as e:
                    logger.warning(f"[CONSUMER] Queue {task_type} might not exist yet. Proceeding...")

                # Delete existing exchanges if they exist
                # try:
                #     await channel.exchange_delete("delayed_task_exchange")
                #     logger.info(f"[CONSUMER] Deleted existing exchange: delayed_task_exchange")
                # except Exception as e:
                #     logger.warning(f"[CONSUMER] Exchange delayed_task_exchange might not exist yet. Proceeding...")

                # Declare the exchange and queue
                exchange = await channel.declare_exchange(
                    name="delayed_task_exchange",
                    type="x-delayed-message",
                    durable=True,
                    arguments={"x-delayed-type": "direct"}
                )

                queue = await channel.declare_queue(task_type, durable=True)
                await queue.bind(exchange, routing_key=task_type)
                logger.info(f"[CONSUMER] Waiting for {task_type} tasks.")

                # Start consuming messages
                async with queue.iterator() as queue_iter:
                    async for message in queue_iter:
                        try:
                            async with message.process():
                                process_task_function = PROCESS_TASK_FUNCTIONS.get(task_type)
                                if process_task_function:
                                    await asyncio.shield(process_task_function(message))
                        except Exception as e:
                            logger.error(f"[CONSUMER] Error processing message in {task_type}: {e}")

            except Exception as e:
                logger.error(f"[CONSUMER] Unexpected error while consuming {task_type}: {e}")
                raise

    except asyncio.CancelledError:
        logger.info(f"[CONSUMER] Consumer for {task_type} was cancelled.")
    finally:
        # Close the connection pool
        # await connection_pool.close()
        logger.info(f"[CONSUMER] consumer for {task_type} is being shutdown")


async def start_task_consumers(connection_pool: RabbitMQConnectionPool):
    consumers = [consume_task(task_type=task_type, connection_pool=connection_pool) for task_type in TASK_TYPES]
    return await asyncio.gather(*consumers)