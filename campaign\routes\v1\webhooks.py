from database.database import get_session
from utils.elasticsearch_utils.get_elasticsearch import get_elastic
from utils.elasticsearch_utils.elastic_query import search_matching_companies, search_primary_rep
from utils.unipile_utils.unipile_client import EmailClient, LinkedInClient
from message_bus.task_publisher import publish_task
from message_bus.rpc_client import RPCClient
from shared_state import app_state
from core.config import settings
from schema.campaign_schema import (CampaignCreate, 
                                    CampaignDetails, CampaignContentUpdate, 
                                    CampaignSendLinkedInMessage, CampaignDetailsUpdate,
                                    CampaignSendLinkedInInvitation,
                                    TestScheduleJob)
from schema.company_schema import (CompanyEmailUpdate, CompanyAddManual, 
                                   CompanyLinkedInMsgUpdate)
from schema.rep_schema import (RepUpdate)

from schema.webhooks_schema import (WebhooksEmailTracking, WebhooksNewEmail, WebhooksNewMessage)

from schema.outreach_schema import (EmailGeneration, EmailSend,
                                    CreateDraftEmail,EmailOutreach,
                                    UpdateDraftEmail, SendEmailRequest, SendLinkedInRequest, 
                                    RespondEmail)

from database.models import (Campaign, Company, 
                             Representative, SentEmail, 
                             ReceivedEmail, Draft,
                             EmailConfirmationStatus, SenderEmail,
                             HumanticProfile, LinkedInConnection, SentMessage, ReceivedMessage)

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import update, delete, func, distinct
from sqlalchemy.future import select
from sqlalchemy.orm import aliased
from fastapi import APIRouter, Header, Depends, Query
from fastapi import HTTPException, status, Request
from fastapi import status, File, UploadFile
from sqlalchemy.future import select
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk, BulkIndexError
from typing import List
import uuid
import pandas as pd
from io import StringIO
import requests
from loguru import logger
import asyncio
import json
import re
from bs4 import BeautifulSoup

router = APIRouter(
    prefix = "/api",
    tags=['Outreach Management'],
    responses={404: {'description': 'Not found'}},
)

#region WEBHOOKS
@router.post("/campaign/emails/webhooks/sent", status_code=status.HTTP_200_OK)
async def webhooks_sent_email(
    webhooks_new_email: WebhooksNewEmail,
    db: AsyncSession = Depends(get_session)    
):
    try:
        # to_address = webhooks_new_email.to_attendees[0]["identifier"]
        stmt = (
            update(SentEmail)
            .where(SentEmail.tracking_id==webhooks_new_email.tracking_id)
            .values(
                unipile_id = webhooks_new_email.email_id,
                subject = webhooks_new_email.subject,
                from_address = webhooks_new_email.from_attendee["identifier"],
                from_info = webhooks_new_email.from_attendee,
                # to_addresses = [to_address],
                # to_info = webhooks_new_email.to_attendees,
                message_id = webhooks_new_email.message_id
            )
        )
        await db.execute(stmt)
        
        query = select(SentEmail.company_id).where(SentEmail.tracking_id==webhooks_new_email.tracking_id)
        results = await db.execute(query)
        company_id = results.scalars().first()


        stmt = update(Company).filter(Company.company_id==company_id, Company.email_confirmation_status_id.notin_([4,5,6])).values(email_confirmation_status_id=4)
        await db.execute(stmt)
        await db.commit()
        logger.info(f"Email {webhooks_new_email.email_id} was sent successfully")

    except Exception as e:
        await db.rollback()
        # logger.info(str(webhooks_new_email))
        # logger.error(str(e))
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )
    pass


@router.post("/campaign/emails/webhooks/received", status_code=status.HTTP_200_OK)
async def webhooks_received_email(
    webhooks_new_email: WebhooksNewEmail
):
    try:
        # logger.info("Received a new_email_received webhook")
        message = webhooks_new_email.dict()
        await publish_task(task_type="receive_reply_webhooks", message_body=message, connection_pool=app_state["rabbitmq_conn_pool"])
        return {
            "status": "success"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        ) 


@router.post("/campaign/emails/webhooks/tracking")
async def webhooks_track_email(
    webhook_email_tracking: WebhooksEmailTracking,
    db: AsyncSession = Depends(get_session)
):
    try:
        # logger.info("Received a email_tracking webhook")
        if webhook_email_tracking.event == "mail_opened":
            tracking_id = webhook_email_tracking.tracking_id
            query = select(SentEmail.company_id).where(SentEmail.tracking_id==tracking_id)
            results = await db.execute(query)
            company_id = results.scalars().first()

            stmt = update(SentEmail).where(SentEmail.tracking_id==tracking_id).values(unipile_id=webhook_email_tracking.email_id)
            await db.execute(stmt)

            if company_id:
                query = select(Company).where(Company.company_id==company_id)
                results = await db.execute(query)
                company = results.scalars().first()  
                if company.email_confirmation_status_id == 4:
                    stmt = update(Company).where(Company.company_id==company_id).values(email_confirmation_status_id=5)
                    await db.execute(stmt)
                    await db.commit()
                    
                stmt = update(SentEmail).where(SentEmail.tracking_id==tracking_id).values(email_opened=True, unipile_id=webhook_email_tracking.email_id)
                await db.execute(stmt)
                await db.commit()

                query = select(SentEmail).filter(SentEmail.tracking_id==tracking_id)
                results = await db.execute(query)
                sent_email = results.scalars().first()

                if sent_email.message_id == None:
                    email_client = EmailClient()
                    email_client.set_unipile_account_id(sent_email.account_id)
                    response = email_client.retrieve_email(sent_email.unipile_id)
                    if response.status_code == 200:
                        email_data = response.json()
                        message_id = email_data["message_id"]
                        from_info = email_data["from_attendee"]
                        stmt = update(SentEmail).where(SentEmail.tracking_id==tracking_id).values(
                            message_id=message_id, 
                            from_info=from_info
                        )
                        await db.execute(stmt)

                await db.commit()
                logger.info(f"Email sent to company_id: {company_id} has been opened")
        return {
            "status": "success"
        }
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )
#endregion


#region LINKEDIN WEBHOOKS
async def handle_webhooks_message_sent_v2(
    webhooks_new_message: WebhooksNewMessage,
    client: LinkedInClient,
    own_provider_id: str,
    own_profile: dict,
    db: AsyncSession = Depends(get_session)
):
    # logger.debug(webhooks_new_message.dict())

    recipient_public_id_list = []
    recipient_list = []
    for attendee in webhooks_new_message.attendees:
        profile = client.retrieve_profile(attendee["attendee_profile_url"]).json()
        public_id = profile["public_identifier"]
        if attendee["attendee_provider_id"] != own_provider_id:                
            recipient_public_id_list.append(public_id)
            recipient_list.append(attendee)

    if len(recipient_public_id_list) <= 0:
        logger.error("This message is not sent by the user")    
        return

    else:
        recipient_username = recipient_public_id_list[0]
        recipient = recipient_list[0]        
        recipient_provider_id = recipient["attendee_provider_id"]

        chat_id = webhooks_new_message.chat_id
        query = select(SentMessage).filter(SentMessage.chat_id==chat_id).order_by(SentMessage.created_at.desc())
        results = await db.execute(query)
        latest_sent_message = results.scalars().first()
        if latest_sent_message is not None: 
            logger.info("chat exists")
            # logger.info(str(latest_sent_message.internal_id))
            # if sent_message with chat_id exists
            query = select(SentMessage).filter(SentMessage.unipile_id==webhooks_new_message.message_id, SentMessage.chat_id==chat_id)
            results = await db.execute(query)
            sent_message_to_update = results.scalars().first()

            if sent_message_to_update is not None: # if the message is the same as the one in the database:                
                stmt = update(SentMessage).where(SentMessage.chat_id==chat_id, SentMessage.unipile_id==webhooks_new_message.message_id).values(                    
                    sender = webhooks_new_message.sender,
                    recipient = recipient,                        
                    attendees = webhooks_new_message.attendees,
                    timestamp = webhooks_new_message.timestamp
                )
                await db.execute(stmt)
                await db.commit()
                logger.info(f"Message {webhooks_new_message.message_id} was sent successfully")
                return
        
            else: # if the message is not the same as the one in the database:
                new_message = SentMessage(
                    company_id = latest_sent_message.company_id,
                    message_content = webhooks_new_message.message,
                    account_id = webhooks_new_message.account_id,
                    chat_id = webhooks_new_message.chat_id,
                    unipile_id = webhooks_new_message.message_id,
                    sender = webhooks_new_message.sender,
                    sender_username = own_profile["public_identifier"],
                    sender_provider_id = own_provider_id,
                    recipient = recipient,
                    recipient_provider_id = recipient_provider_id,
                    recipient_username = recipient_username,
                    attendees = webhooks_new_message.attendees,
                    timestamp = webhooks_new_message.timestamp,
                )

                db.add(new_message)
                await db.commit()
                logger.info(f"Message {webhooks_new_message.message_id} was sent from an external source successfully")
                return
                

        else: # if sent_message with chat_id does not exist
            #check if the message is sent to one of the prospects of the user
            logger.info("chat doesn't exist")
            query = (
                select(SentMessage)
                .filter(
                    SentMessage.sender_provider_id==own_provider_id, 
                    SentMessage.recipient_provider_id==recipient_provider_id, 
                    SentMessage.account_id==webhooks_new_message.account_id,
                    SentMessage.draft_type==0
                )
                .order_by(SentMessage.created_at.desc())
            )
            results = await db.execute(query)
            sent_message = results.scalars().first()
            
            if sent_message is not None:
                if sent_message.chat_id is None and sent_message.unipile_id is None:
                    stmt = update(SentMessage).where(SentMessage.company_id==sent_message.company_id).values(
                        chat_id = webhooks_new_message.chat_id,
                        unipile_id = webhooks_new_message.message_id,
                        sender = webhooks_new_message.sender,
                        recipient = recipient,                        
                        attendees = webhooks_new_message.attendees,
                        timestamp = webhooks_new_message.timestamp
                    )
                    await db.execute(stmt)

                    new_connection = LinkedInConnection(
                        provider_id = own_provider_id,
                        connected_provider_id = recipient_provider_id
                    )
                    db.add(new_connection)

                    await db.commit()
                    logger.info(f"Invitation to {recipient_username} was sent successfully")

                    query = select(Company).filter(Company.company_id==sent_message.company_id)
                    results = await db.execute(query)
                    company = results.scalars().first()

                    query = select(Campaign.user_id).filter(Campaign.campaign_id==company.campaign_id)
                    results = await db.execute(query)
                    request_user_id = results.scalars().first()

                    if company.auto_outreach == True:
                        message = {
                            "company_id": str(company.company_id),
                            "user_id": str(request_user_id)
                        }
                        await publish_task("send_linkedin_outreach", message, connection_pool=app_state["rabbitmq_conn_pool"])

                    return
            else:
                logger.warning("Sent message not found")
                return




async def handle_webhooks_message_received(
    webhooks_new_message: WebhooksNewMessage,
    client: LinkedInClient,
    own_provider_id: str,
    own_profile: dict,
    db: AsyncSession = Depends(get_session)
):
    sender = webhooks_new_message.sender
    sender_profile = client.retrieve_profile(sender["attendee_profile_url"]).json()
    sender_username = sender_profile["public_identifier"]    
    sender_provider_id = sender_profile["provider_id"]    
    own_first_name = own_profile["first_name"]
    own_last_name = own_profile["last_name"]
    own_name = own_first_name + " " + own_last_name
    # logger.debug(webhooks_new_message.dict())

    query = (
        select(SentMessage)
        .filter(
            SentMessage.chat_id==webhooks_new_message.chat_id,
            SentMessage.recipient_provider_id==sender_provider_id,
            SentMessage.account_id==webhooks_new_message.account_id,
            SentMessage.draft_type.in_([0,1,2,3])
        )
        .order_by(SentMessage.created_at.desc())
    )
    results = await db.execute(query)
    sent_message = results.scalars().first()

    if sent_message is None:                
        logger.warning("this is not a replied message")
    else:
        query = select(Company).filter(Company.company_id==sent_message.company_id)
        results = await db.execute(query)
        company = results.scalars().first()

        if company.linkedin_message_status_id in [2,3,4,5]:
            query = select(Representative).filter(Representative.company_id==sent_message.company_id)
            results = await db.execute(query)
            representative = results.scalars().first()
            personality_analysis = representative.personality_analysis
            email_personalization = representative.email_personalization     

            sent_subject = ""

            service = "aiengine.*"
            function_name = "generate_response"
            params = {
                "sent_email": {
                    "sender_info": {
                        "name": own_name
                    },                    
                    "subject": sent_subject,
                    "body": sent_message.message_content,
                }, 
                "reply_email": {
                    "subject": "Re: " + sent_subject,
                    "body": webhooks_new_message.message,
                },  
                "personality_analysis": personality_analysis,
                "email_personalization": email_personalization
            }       
            
            rpc = await RPCClient().connect()
            response = await rpc.call(service=service, function_name=function_name, params=params)
            response_body = json.loads(response)
            result = response_body.get("result")               

            # logger.debug(result)

            suggested_response = None
            explanation = None
            emotions = None
            sentiment = None
            key_takeaways = None

            if result == "error":
                logger.error(f"Failed to generate response for company_id: {sent_message.company_id}")

            else:
                suggested_subject = result["response_subject"]
                suggested_body = result["response_body"]
                explanation = result["explanation"]
                emotions = result["emotions"]
                sentiment = result["sentiment"]     
                key_takeaways = result["key_takeaways"]            
                    
                soup = BeautifulSoup(suggested_body, "html.parser")
                suggested_body_plain = soup.text

                pattern = r'(?<!\w\.\w.)(?<![A-Z][a-z]\.)(?<=\.|\?|!)\s'
                sentences = re.split(pattern, suggested_body_plain)
                sentences = [sentence.strip() for sentence in sentences if sentence.strip()]
                final_text = "\n".join(sentences).strip()
                suggested_body_plain = final_text            
                # function_name = "html_to_text"
                # params = {
                #     "html_content": suggested_body
                # }
                # rpc_response = await rpc.call(service="aiengine.*", function_name=function_name, params=params)
                # rpc_response_body = json.loads(rpc_response)
                # result = rpc_response_body.get("result")            
                # if result != "error":
                #     suggested_body_plain = result["plain_text"]

                suggested_response = {
                    "subject": suggested_subject,
                    "body": suggested_body_plain
                }            

            reply_message = ReceivedMessage(
                message_content = webhooks_new_message.message,            
                account_id = webhooks_new_message.account_id,
                company_id = sent_message.company_id,
                chat_id = webhooks_new_message.chat_id,
                unipile_id = webhooks_new_message.message_id,
                sender = webhooks_new_message.sender,
                sender_username = sender_username,
                sender_provider_id = sender_provider_id,
                recipient = sent_message.sender,
                recipient_username = own_profile["public_identifier"],
                recipient_provider_id = own_provider_id,
                attendees = webhooks_new_message.attendees,
                suggested_response = suggested_response,
                explanation = explanation,
                emotions = emotions,
                sentiment = sentiment,
                key_takeaways = key_takeaways,
                timestamp = webhooks_new_message.timestamp
            )

            db.add(reply_message)

            stmt = update(Company).filter(Company.company_id==sent_message.company_id).values(linkedin_message_status_id=4)
            await db.execute(stmt)

            if suggested_response is not None and company.auto_outreach == True:
                response = client.send_message(message_body=suggested_body_plain, recipient_profile_url=sender["attendee_profile_url"])
                if response.status_code not in [200,201]:
                    logger.error(f"Failed to send response to {sender_username}")
                else:
                    response_message = SentMessage(
                        message_content = suggested_body_plain,
                        account_id = webhooks_new_message.account_id,
                        company_id = sent_message.company_id,
                        chat_id = webhooks_new_message.chat_id,
                        unipile_id = response.json()["message_id"],
                        sender_username = own_profile["public_identifier"],
                        sender_provider_id = own_provider_id,
                        recipient_username = sender_username,
                        recipient_provider_id = sender_provider_id,
                        # sender = sent_message.recipient,
                        # recipient = sent_message.sender,
                        # attendees = webhooks_new_message.attendees,
                        timestamp = webhooks_new_message.timestamp
                    )
                    db.add(response_message)

                    stmt = update(Company).filter(Company.company_id==sent_message.company_id).values(linkedin_message_status_id=5)
                    await db.execute(stmt)
            logger.info(f"Message received successfully from {sender_username}")
        await db.commit()

@router.post("/campaign/linkedin/webhooks/message/new")
async def webhooks_new_linkedin_message(
    webhooks_new_message: WebhooksNewMessage,
    db: AsyncSession = Depends(get_session)
):
    try:

        # logger.debug(webhooks_new_message.dict())
        # return webhooks_new_message.dict()

        client = LinkedInClient()
        client.set_unipile_account_id(webhooks_new_message.account_id)
        own_profile = client.retrieve_own_profile().json()
        own_provider_id = own_profile["provider_id"]


        # logger.info("Sender provider id: " + webhooks_new_message.sender["attendee_provider_id"])
        # logger.info("Own    provider id: " + own_provider_id)

        # logger.debug(webhooks_new_message.dict())

        if own_provider_id == webhooks_new_message.sender["attendee_provider_id"]:
            logger.info("Received sent message webhooks")
            await handle_webhooks_message_sent_v2(
                client=client,
                own_provider_id=own_provider_id,
                own_profile=own_profile,                
                webhooks_new_message=webhooks_new_message,
                db=db
            )

        else:
            logger.info("Received received message webhooks")
            query = select(ReceivedMessage).filter(ReceivedMessage.unipile_id==webhooks_new_message.message_id)
            results = await db.execute(query)
            received_message = results.scalars().first()
            if received_message is not None:
                logger.info("This message has already been received")
            else:
                await handle_webhooks_message_received(
                    client=client, 
                    own_provider_id=own_provider_id, 
                    own_profile=own_profile,                
                    webhooks_new_message=webhooks_new_message, 
                    db=db
                )


        await db.commit()
        return {
            "status": "success"
        }        
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

#endregion

webhooks_routes = router
