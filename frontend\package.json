{"name": "rob<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "aos": "^2.3.4", "axios": "^1.7.7", "chart.js": "^4.4.7", "d3": "^7.9.0", "hls.js": "^1.5.17", "quill": "^2.0.3", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-markdown": "^9.0.1", "react-quill": "^2.0.0", "react-router-dom": "^6.26.2", "react-scripts": "5.0.1", "react-speech-recognition": "^3.10.0", "recharts": "^2.13.0", "simli-client": "^1.2.3", "sweetalert2": "^11.14.3", "sweetalert2-react-content": "^5.0.7", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.13"}}