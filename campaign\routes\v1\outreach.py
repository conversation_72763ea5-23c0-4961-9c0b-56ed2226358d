from database.database import get_session
from utils.elasticsearch_utils.get_elasticsearch import get_elastic
from utils.unipile_utils.unipile_client import EmailClient, LinkedInClient
from message_bus.task_publisher import publish_task
from message_bus.rpc_client import RPCClient
from shared_state import app_state
from core.config import settings
from schema.campaign_schema import (CampaignCreate, 
                                    CampaignDetails, CampaignContentUpdate, 
                                    CampaignSendLinkedInMessage, CampaignDetailsUpdate,
                                    CampaignSendLinkedInInvitation,
                                    TestScheduleJob)
from schema.company_schema import (CompanyEmailUpdate, CompanyAddManual, 
                                   CompanyLinkedInMsgUpdate)
from schema.rep_schema import (RepUpdate)

from schema.webhooks_schema import (WebhooksEmailTracking, WebhooksNewEmail, WebhooksNewMessage)

from schema.outreach_schema import (EmailGeneration, EmailSend,
                                    CreateDraftEmail,EmailOutreach, StartAutoOutreach,
                                    UpdateDraftEmail, SendEmailRequest, SendLinkedInRequest, 
                                    RespondEmail, RespondMessage)

from database.models import (Campaign, Company, 
                             Representative, SentEmail, 
                             ReceivedEmail, Draft,
                             EmailConfirmationStatus, SenderEmail,
                             HumanticProfile, LinkedInConnection, SentMessage, ReceivedMessage)

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import update, delete, func, distinct
from sqlalchemy.future import select
from sqlalchemy.orm import aliased
from fastapi import APIRouter, Header, Depends, Query
from fastapi import HTTPException, status, Request
from fastapi import status, File, UploadFile
from sqlalchemy.future import select
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk, BulkIndexError
from typing import List
import uuid
import pandas as pd
from io import StringIO
import requests
from loguru import logger
import asyncio
import json
from bs4 import BeautifulSoup

router = APIRouter(
    prefix = "/api",
    tags=['Outreach Management'],
    responses={404: {'description': 'Not found'}},
)

#region EMAIL OUTREACH
@router.post("/campaign/emails/generate")
async def generate_email(
    email_generation: EmailGeneration,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    try:
        company_id_list = email_generation.company_id_list
        # query = select(Campaign).where(Campaign.campaign_id==email_generation.campaign_id)
        # results = await db.execute(query)
        # campaign = results.scalars().first()
        count = 0
        for company_id in company_id_list:
            query = select(Representative).filter(Representative.company_id==company_id)
            results = await db.execute(query)
            representative = results.scalars().first()
            if representative.personality_analysis is not None:
                message = {
                    "campaign_id" : str(email_generation.campaign_id),
                    "company_id": str(company_id),
                    "prompt_name": email_generation.prompt_name if email_generation.prompt_name else None,         
                    # "auto_send_email": False,
                    # "auto_send_linkedin": False,   
                    "auto_outreach": False,
                    "user_id": str(request_user_id),
                }
                await publish_task("create_drafts", message, connection_pool=app_state["rabbitmq_conn_pool"])
                count += 1

        return {
            "status": "success",
            "detail": f"{count} out of {len(company_id_list)} prospects have personality analysis and are set to generate drafts"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/campaign/emails/draft/generate", status_code=status.HTTP_201_CREATED)
async def generate_email(
    email_generation: EmailGeneration,
    db: AsyncSession = Depends(get_session)
):
    company_id = email_generation.company_id_list[0]
    query = select(Company).filter(Company.company_id==company_id)
    results = await db.execute(query)
    company = results.scalars().first()
    query = select(Campaign).filter(Campaign.campaign_id==company.campaign_id)
    results = await db.execute(query)
    campaign = results.scalars().first()
    query = select(Representative).filter(Representative.company_id==company_id)
    results = await db.execute(query)
    representative = results.scalars().first()

    from_name = campaign.campaign_contact["user_nick_name"]
    to_name = representative.rep_name
    core_service = campaign.campaign_info["core_service"]
    key_benefits = campaign.campaign_info["key_benefits"]
    problem_solved = campaign.campaign_info["problem_solved"]
    must_have_info = campaign.campaign_info["must_have_info"]
    output_format = campaign.email_format
    personality_analysis = representative.personality_analysis
    email_personalization = representative.email_personalization
    communication_advice = representative.communication_advice
    industry = company.industry
    company_name = company.company_name

    params = {
        "from_name": from_name,
        "to_name": to_name,
        "core_service": core_service,
        "key_benefits": key_benefits,
        "problem_solved": problem_solved,
        "must_have_info": must_have_info,
        "output_format": output_format,
        "personality_analysis": personality_analysis,
        "email_personalization": email_personalization,
        "communication_advice": communication_advice,
        "industry": industry,
        "company_name": company_name,
        "prompt_name": email_generation.prompt_name if email_generation.prompt_name else None,
    }

    response = requests.post(
        url = settings.AI_SERVICE_URL + "/api/nlp/generate_drafts/v2",
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json"
        },
        json = params
    )

    if response.status_code not in [200, 201]:
        raise HTTPException(
            status_code=response.status_code,
            detail=response.text
        )
    
    drafts = response.json()["drafts_list"]

    return drafts




@router.post("/campaign/emails/draft/create", status_code=status.HTTP_201_CREATED)
async def create_draft_email(
    create_draft: CreateDraftEmail,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)    
):
    try:    
        draft = Draft(
            draft_type = create_draft.draft_type,
            company_id = create_draft.company_id,
            from_address = create_draft.from_address,
            from_name = create_draft.from_name,
            to_address = create_draft.to_address,
            to_name = create_draft.to_name,
            subject = create_draft.subject,
            body = create_draft.body,
            body_plain = create_draft.body_plain
        )
        db.add(draft)
        stmt = update(Company).where(Company.company_id==create_draft.company_id).values(email_confirmation_status_id=2)
        await db.execute(stmt)
        await db.commit()
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.put("/campaign/emails/draft/update")
async def update_draft_email(
    update_draft_payload: UpdateDraftEmail,
    db: AsyncSession = Depends(get_session)
):
    try:
        data = {
            "subject": update_draft_payload.subject,
            "body": update_draft_payload.body
        }

        stmt = (
            update(Draft)
            .filter(Draft.draft_id==update_draft_payload.draft_id)
            .values(**data)
            .execution_options(synchronize_session="fetch")
        )
        await db.execute(stmt)

        stmt = (
            update(Company)
            .filter(Company.company_id==update_draft_payload.company_id)
            .values(email_confirmation_status_id=3)
        )
        await db.execute(stmt)
        await db.commit()
        return {
            "status": "success"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )


@router.get("/campaign/emails/draft/{company_id}")
async def get_draft_emails(
    company_id: uuid.UUID,
    db: AsyncSession = Depends(get_session)
):
    try:
        query = select(Draft).where(Draft.company_id==company_id).order_by(Draft.draft_type.asc())
        results = await db.execute(query)
        draft_emails = results.scalars().all()
        return draft_emails
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/campaign/emails/send")
async def send_email(
    email_request: SendEmailRequest,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    try:
        email_sent = 0
        for company_id in email_request.company_id_list:
            query = select(Representative).filter(Representative.company_id==company_id)
            results = await db.execute(query)
            representative = results.scalars().first()

            if representative.rep_email is None:
                continue

            query = (
                select(Draft)
                .filter(Draft.company_id==company_id, Draft.email_sent==False)
                .order_by(Draft.draft_type.asc())
            )
            result = await db.execute(query)
            draft_list = result.scalars().all()
            query = select(Company).where(Company.company_id==company_id)
            result = await db.execute(query)
            company = result.scalars().first()

            if company.auto_outreach == True:
                logger.warning("company is already set to auto email outreach")
            elif company.email_confirmation_status_id == 6:
                logger.warning("the prospect has already replied to the outreach attempt")
            elif len(draft_list) <= 0:
                logger.warning("no more draft email to send for company")
            else:
                message = {
                    "company_id": str(company_id),
                }
                await publish_task("send_email_outreach", message, connection_pool=app_state["rabbitmq_conn_pool"])
                email_sent += 1

        return {
            "status": "success",
            "message": f"{email_sent} emails are set to be sent out of {len(email_request.company_id_list)} companies as requested"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )


@router.post("/campaign/{campaign_id}/outreach/auto")
async def start_auto_outreach(
    campaign_id: uuid.UUID,
    email_outreach: StartAutoOutreach,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)    
):
    try:
        headers = {
            "accept": "application/json",
            "request-user-id": str(request_user_id)
        }
        response = requests.get(
            f"{settings.USER_SERVICE_URL}/api/users/credits",
            headers=headers
        )
        if response.status_code not in [200, 201]:
            raise HTTPException(
                status_code=503,
                detail=f"Failed to retrieve user credits: {response.text}"
            )
        else:
            user_credit = response.json()
            if user_credit["current_credits"] < 1:
                raise HTTPException(
                    status_code=400,
                    detail="User does not have enough credits"
                )

        query = select(Campaign).filter(Campaign.campaign_id==campaign_id)
        results = await db.execute(query)
        campaign = results.scalars().first()
        if campaign.campaign_auto_outreach == True:
            return {
                "status": "success",
                "detail": "campaign is already set to auto outreach"
            }

        query = select(Company).filter(Company.campaign_id==campaign_id, Company.auto_outreach==False)
        results = await db.execute(query)
        companies = results.scalars().all()
        if len(companies) <= 0:
            return {
                "status": "success",
                "detail": "no more companies to set auto outreach"
            }

        # auto_send_linkedin = False

        # rpc_client = RPCClient()
        # rpc = await rpc_client.connect()
        # function_name = "get_user_info"
        # response = await rpc.call(service="user.*", function_name=function_name, params={"user_id": request_user_id})
        # response_body = json.loads(response)
        # result = response_body.get("result")
        # if result != "error":
        #     user_info = result
        #     if user_info.get("unipile_linkedin_id") is not None:
        #         linkedin_client = LinkedInClient()
        #         linkedin_client.set_unipile_account_id(user_info["unipile_linkedin_id"])        
        #         own_profile = linkedin_client.retrieve_own_profile().json()
        #         if "provider_id" in own_profile:
        #             auto_send_linkedin = True

        stmt = update(Campaign).where(Campaign.campaign_id==campaign_id).values(campaign_auto_outreach=True, prompt_name=email_outreach.prompt_name)
        await db.execute(stmt)
        await db.commit()

        query = (
            select(Company)
            .filter(Company.campaign_id==campaign_id, Company.auto_outreach!=True)
            .order_by(Company.company_name.asc())
            .limit(10)
        )
        results = await db.execute(query)
        companies = results.scalars().all()

        for company in companies:
            query = select(Representative).filter(Representative.company_id==company.company_id)
            results = await db.execute(query)
            representative = results.scalars().first()

            query = select(Draft).filter(Draft.company_id==company.company_id, Draft.draft_type==0)
            results = await db.execute(query)
            draft = results.scalars().first()

            # stmt = update(Company).where(Company.company_id==company.company_id).values(auto_outreach=True)
            # await db.execute(stmt) 
            # await db.commit()

            if representative.personality_analysis is None and representative.analysis_status != "NOT_FOUND":
                rep_linkedin_username = representative.rep_linkedin_address.split('/')[-2] if representative.rep_linkedin_address.endswith('/') else representative.rep_linkedin_address.split('/')[-1] 
                query = select(HumanticProfile).filter(HumanticProfile.linkedin_username==rep_linkedin_username)
                query_results = await db.execute(query)
                humantic_profile = query_results.scalars().first()

                if humantic_profile:
                    results = humantic_profile.results
                    metadata = humantic_profile.meta_data

                    profile_image = results['user_profile_image']
                    user_description = results['user_description']
                    work_history = results['work_history']
                    location = results['location']
                    skills = results['skills']
                    followers = results['followers']
                    prographics = results['prographics']
                    education = results['education']
                    cold_calling_advice = results['persona']['sales']['cold_calling_advice']
                    communication_advice = results['persona']['sales']['communication_advice']
                    email_personalization = results['persona']['sales']['email_personalization']
                    hiring_behavioural_factors = results['persona']['hiring']['behavioural_factors']
                    personal_analysis = results['personality_analysis']
                    sales_profile_url = results['persona']['sales']['profile_url']
                    analysis_status = metadata['analysis_status']
                    analysis_confidence = metadata['confidence']            

                    stmt = update(Representative).where(Representative.rep_id == representative.rep_id).values(
                        profile_image = profile_image,
                        user_description = user_description,
                        work_history = work_history,
                        location = location,
                        skills = skills,
                        followers = followers,
                        prographics = prographics,
                        education = education,                       
                        personality_analysis = personal_analysis,
                        cold_calling_advice = cold_calling_advice,
                        communication_advice = communication_advice,
                        email_personalization = email_personalization,
                        hiring_behavioural_factors = hiring_behavioural_factors,
                        sales_profile_url = sales_profile_url,
                        analysis_status = analysis_status,
                        analysis_confidence = analysis_confidence
                    )            
                    await db.execute(stmt)    
                    await db.commit()

                    message = {
                        "campaign_id" : str(campaign_id),
                        "company_id": str(company.company_id),
                        "prompt_name": email_outreach.prompt_name if email_outreach.prompt_name else None,
                        # "auto_send_email": True,
                        # "auto_send_linkedin": auto_send_linkedin,
                        "auto_outreach": True,
                        "user_id": str(request_user_id)
                    }
                    await publish_task("create_drafts", message, connection_pool=app_state["rabbitmq_conn_pool"])                    

                else:
                    message = {
                        "rep_linkedin": representative.rep_linkedin_address,
                        "company_id": str(company.company_id),
                        "auto_outreach": True,
                        # "auto_send_email": True,
                        # "auto_send_linkedin": auto_send_linkedin
                    }
                    await publish_task("update_humantic_profile", message, connection_pool=app_state["rabbitmq_conn_pool"])
            elif draft is None:
                message = {
                    "campaign_id" : str(campaign_id),
                    "company_id": str(company.company_id),
                    "prompt_name": email_outreach.prompt_name if email_outreach.prompt_name else None,
                    # "auto_send_email": True,
                    # "auto_send_linkedin": auto_send_linkedin,
                    "auto_outreach": True,
                    "user_id": str(request_user_id)
                }
                await publish_task("create_drafts", message, connection_pool=app_state["rabbitmq_conn_pool"])

            # else:
            #     # query = select(Draft).filter(Draft.company_id==company.company_id, Draft.email_sent==False).order_by(Draft.draft_type.asc())
            #     # results = await db.execute(query)
            #     # email_draft = results.scalars().first()
            #     # if email_draft is not None:
            #     if representative.rep_email is not None and draft.email_sent == False:
            #         email_outreach_message = {
            #             "company_id": str(company.company_id)
            #         }
            #         await publish_task("send_email_outreach", email_outreach_message, connection_pool=app_state["rabbitmq_conn_pool"])

            #     # query = select(Draft).filter(Draft.company_id==company.company_id, Draft.linkedin_sent==False).order_by(Draft.draft_type.asc())
            #     # results = await db.execute(query)
            #     # linkedin_draft = results.scalars().first()
            #     # if auto_send_linkedin == True and linkedin_draft is not None:
            #     if auto_send_linkedin == True and draft.linkedin_sent == False and representative.rep_linkedin_address is not None:
            #         linkedin_outreach_message = {
            #             "company_id": str(company.company_id),
            #             "user_id": str(request_user_id)
            #         }
            #         await publish_task("send_linkedin_outreach", linkedin_outreach_message, connection_pool=app_state["rabbitmq_conn_pool"])

        return {
            "status": "success"
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.put("/campaign/{campaign_id}/outreach/stop-auto")
async def stop_auto_outreach(
    campaign_id: uuid.UUID,
    db: AsyncSession = Depends(get_session)
):
    try:
        stmt = update(Campaign).where(Campaign.campaign_id==campaign_id).values(campaign_auto_outreach=False)
        await db.execute(stmt)
        await db.commit()
        return {
            "status": "success"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.get("/campaign/{campaign_id}/outreach/get-auto-status")
async def get_auto_outreach_status(
    campaign_id: uuid.UUID,
    db: AsyncSession = Depends(get_session)
):
    try:
        query = select(Campaign).filter(Campaign.campaign_id==campaign_id)
        results = await db.execute(query)
        campaign = results.scalars().first()
        auto_status = True if campaign.campaign_auto_outreach == True else False
        return {
            "status": "success",
            "auto_outreach": auto_status
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )


@router.post("/campaign/emails/reply-mails/respond")
async def respond_reply_email(
    respond_email: RespondEmail,
    db: AsyncSession = Depends(get_session)
):
    try:
        query = (
            select(ReceivedEmail)
            .filter(ReceivedEmail.company_id==respond_email.company_id)
            .order_by(ReceivedEmail.created_at.desc())
        )
        result = await db.execute(query)
        received_email = result.scalars().first()

        query = select(Company).filter(Company.company_id==received_email.company_id)
        result = await db.execute(query)
        company = result.scalars().first()

        if company.auto_outreach:
            raise Exception("The company is set to auto outreach. You cannot respond to a reply email manually.")
        
        if company.email_confirmation_status_id == 7:
            raise Exception("The prospect's reply has already been responded to.")

        query = select(Campaign).filter(Campaign.campaign_id==company.campaign_id)
        result = await db.execute(query)
        campaign = result.scalars().first()
        campaign_contact = campaign.campaign_contact

        if received_email:
            query = select(SenderEmail).filter(SenderEmail.remaining_emails>0).order_by(SenderEmail.remaining_emails.desc())
            results = await db.execute(query)
            sender = results.scalars().first()
            if sender is None:
                raise Exception("No more email senders available")

            from_name = "Robert Mandev"
            if campaign_contact is not None:
                if campaign_contact.get("user_nick_name") is not None:
                        from_name = campaign_contact["user_nick_name"]

            email_client = EmailClient()
            email_client.set_unipile_account_id(sender.unipile_account_id)            
            response = email_client.send_email(
                subject=respond_email.subject,
                content=respond_email.body,
                name_from=from_name,
                name_to=received_email.from_info["display_name"],
                email_to=received_email.from_address
            )
            if response.status_code not in [200,201]:
                raise Exception(f"failed to send response to reply email from {received_email.from_address}")   
            else:
                soup = BeautifulSoup(respond_email.body)
                body_plain = soup.text
                new_sent_email = SentEmail(
                    subject=respond_email.subject,
                    body=respond_email.body,
                    body_plain=body_plain,
                    to_info=[received_email.from_info],
                    from_address=sender.sender_address,
                    to_addresses=[received_email.from_address],
                    company_id=received_email.company_id,
                    tracking_id=response.json()["tracking_id"],
                    account_id=sender.unipile_account_id
                )
                db.add(new_sent_email)

                stmt = update(Company).filter(Company.company_id==received_email.company_id).values(email_confirmation_status_id=7)
                await db.execute(stmt)

                stmt = update(SenderEmail).filter(SenderEmail.sender_id==sender.sender_id).values(remaining_emails=SenderEmail.remaining_emails-1)
                await db.execute(stmt)
                await db.commit()
                return {
                    "status": "success"
                }
        else:
            raise Exception("no reply email found")

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        ) 
            
    

@router.get("/campaign/{campaign_id}/emails/sent-mails")
async def get_sent_emails(
    campaign_id : uuid.UUID,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)    
):
    try:
        query = select(Company).where(Company.campaign_id==campaign_id).order_by(Company.created_at.desc())
        results = await db.execute(query)
        company_list = results.scalars().all()

        result = []

        for company in company_list:
            query = select(Representative).filter(Representative.company_id==company.company_id)
            results = await db.execute(query)
            representative = results.scalars().first()

            query = (
                select(SentEmail)
                .filter(SentEmail.company_id==company.company_id)
                .order_by(SentEmail.created_at.desc())
            )
            results = await db.execute(query)
            sent_emails_list = results.scalars().all()
            if len(sent_emails_list) > 0:
                result.append({
                    "company_id": company.company_id,
                    "company_name": company.company_name,
                    "rep_name": representative.rep_name,
                    "rep_email": representative.rep_email,
                    "sent_emails": sent_emails_list
                })

        return result

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.get("/campaign/companies/{company_id}/emails/reply-mails")
async def get_reply_emails(
    company_id : uuid.UUID,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)        
):
    try:
        query = select(ReceivedEmail).where(ReceivedEmail.company_id==company_id)
        results = await db.execute(query)
        reply_emails_list = results.scalars().all()
        return reply_emails_list 

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )    
    

@router.get("/campaign/companies/{company_id}/emails/conversation")
async def get_email_conversation(
    company_id : uuid.UUID,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)        
):
    try:
        query = select(SentEmail.created_at).filter(SentEmail.company_id==company_id, SentEmail.draft_type==0)
        results = await db.execute(query)
        start_date = results.scalars().first()

        query = (
            select(SentEmail)
            .filter(SentEmail.company_id==company_id, SentEmail.created_at>=start_date)
            .order_by(SentEmail.created_at.asc())
        )
        results = await db.execute(query)
        sent_emails_list = results.scalars().all()

        query = (
            select(ReceivedEmail)
            .filter(ReceivedEmail.company_id==company_id, ReceivedEmail.created_at>start_date)
            .order_by(ReceivedEmail.created_at.asc())
        )
        results = await db.execute(query)
        received_emails_list = results.scalars().all()

        emails_list = []
        for sent_email in sent_emails_list:
            email = {
                "internal_id": sent_email.internal_id,
                "subject": sent_email.subject,
                "body": sent_email.body,
                "timestamp": sent_email.created_at,
                "from_address": sent_email.from_address,
                "from_info": sent_email.from_info,
                "to_addresses": sent_email.to_addresses,
                "to_info": sent_email.to_info,
                "role": "user"
            }
            emails_list.append(email)

        for received_email in received_emails_list:
            email = {
                "internal_id": received_email.internal_id,
                "sent_email_internal_id": received_email.sent_email_internal_id,
                "subject": received_email.subject,
                "body": received_email.body,
                "timestamp": received_email.created_at,
                "from_address": received_email.from_address,
                "from_info": received_email.from_info,
                "to_addresses": received_email.to_addresses,
                "to_info": received_email.to_info,
                "suggested_response": received_email.suggested_response,
                "explanation": received_email.explanation,
                "emotions": received_email.emotions,
                "sentiment": received_email.sentiment,
                "key_takeaways": received_email.key_takeaways,                
                "role": "prospect"
            }
            emails_list.append(email)

        emails_list = sorted(emails_list, key=lambda x: x["timestamp"])
        return emails_list

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.delete("/campaign/emails/{sent_email_id}/delete")
async def delete_sent_email(
    sent_email_id : uuid.UUID,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)        
):
    try:
        stmt = delete(SentEmail).where(SentEmail.internal_id==sent_email_id)
        await db.execute(stmt)
        await db.commit()
        return {
            "status": "success"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )


@router.get("/campaign/{campaign_id}/emails/outreach-stats")
async def email_outreach_statistic(
    campaign_id: uuid.UUID,
    db: AsyncSession = Depends(get_session)
):
    try:
        query = select(Company.company_id).filter(Company.campaign_id==campaign_id)
        results = await db.execute(query)
        companies = results.scalars().all()

        stats = {
            "campaign_id": str(campaign_id),
            "prospects_count": len(companies),
            "sent": {
                "count": 0,
                "rate": 0.0
            },
            "opened": {
                "count": 0,
                "rate": 0.0
            },
            "replied": {
                "count": 0,
                "rate": 0.0
            },
            "interested": {
                "count": 0,
                "rate": 0.0            
            }
        }

        query = select(func.count(distinct(SentEmail.company_id))).filter(SentEmail.company_id.in_(companies))
        result = await db.execute(query)
        stats["sent"]["count"] = result.scalar()
        stats["sent"]["rate"] = stats["sent"]["count"] / stats["prospects_count"] if stats["prospects_count"] > 0 else 0

        query = select(func.count(distinct(SentEmail.company_id))).filter(SentEmail.company_id.in_(companies), SentEmail.email_opened==True)
        result = await db.execute(query)
        stats["opened"]["count"] = result.scalar()
        stats["opened"]["rate"] = stats["opened"]["count"] / stats["sent"]["count"] if stats["opened"]["count"] > 0 else 0

        query = select(func.count(distinct(ReceivedEmail.company_id))).filter(ReceivedEmail.company_id.in_(companies))
        result = await db.execute(query)
        stats["replied"]["count"] = result.scalar()
        stats["replied"]["rate"] = stats["replied"]["count"] / stats["sent"]["count"] if stats["opened"]["count"] > 0 else 0

        return stats
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

#endregion



outreach_routes = router
