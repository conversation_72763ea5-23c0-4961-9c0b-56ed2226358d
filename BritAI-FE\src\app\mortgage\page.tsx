'use client';
import React, { useRef } from 'react';

export default function MortgageInfoPage() {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'application/pdf') {
      alert('Please upload a valid PDF file.');
      return;
    }

    const formData = new FormData();
    formData.append('file', file);
    console.log('Uploading file:', file.name);
    console.log("")

  //   try {
  //     const response = await fetch('http://127.0.0.1:8000/treatment_mortgage_doc', {
  //       method: 'POST',
  //       body: formData,
  //     });

  //     if (!response.ok) {
  //       throw new Error(`Upload failed: ${response.status}`);
  //     }

  //     const result = await response.json();
  //     console.log('API Result:', result);
  //     alert('PDF processed successfully! Check console for details.');
  //   } catch (error) {
  //     console.error('Error uploading file:', error);
  //     alert('Failed to upload and process the file.');
  //   }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col items-center justify-center p-8">
      <div className="max-w-3xl bg-white shadow-md rounded-lg p-6">
        <h1 className="text-4xl font-bold text-gray-800 mb-6 text-center">
          Mortgage Miscalculation
        </h1>
        <p className="text-gray-600 text-lg leading-relaxed mb-6">
          Please input your mortgage document in PDF format. Our AI will analyze the document to identify any potential miscalculations or discrepancies in your mortgage terms.
          <br />
        </p>
        <div className="text-center">
          <button
            onClick={handleUploadClick}
            className="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600"
          >
            Upload
          </button>
          <input
            type="file"
            accept="application/pdf"
            ref={fileInputRef}
            onChange={handleFileChange}
            style={{ display: 'none' }}
          />
        </div>
        <div className="mt-6 text-center">
          <h1 className="text-4xl font-bold text-gray-800 mb-6 text-center"> <strong>Mortgage Information</strong> </h1>
          <div className="w-full h-[300px] bg-gray-200 rounded-lg"></div>
        </div>
        <div className="mt-6 text-center">
          <h1 className="text-4xl font-bold text-gray-800 mb-6 text-center"> <strong>Mortgage Payment Estimation</strong> </h1>
          <div className="w-full h-[300px] bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    </div>
  );
}
