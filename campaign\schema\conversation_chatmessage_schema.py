from typing import List, Optional, Any
import uuid
from datetime import datetime
from pydantic import BaseModel
from enum import Enum


class MessageRole(str, Enum):
    SYSTEM = 0
    USER = 1
    ASSISTANT = 2


## Load this from file in the future
LLM = {
        1: {   
            "model": "gpt-3.5-turbo",
            "price_in": 0.001,
            "price_out": 0.002
        },
        3: {
            "model": "gpt-4-turbo",
            "price_in": 0.01,
            "price_out": 0.03
        },
        8: {
            "model": "claude-medium",
            "price_in": 0.003,
            "price_out": 0.015
        },
        7: {
            "model": "claude-fast",
            "price_in": 0.00025,
            "price_out": 0.00125
        },
    }

# Conversation
class ConversationBase(BaseModel):
    conversation_id: uuid.UUID
    conversation_name: str


class ConversationResponse(ConversationBase):
    created_at: datetime
    updated_at: datetime


class ConversationNew(BaseModel):
    system_prompt_id: str
    chosen_documents: Optional[List[str]] = None
    chosen_csvs: Optional[List[str]] = None

class ConversationDocumentUpdate(BaseModel):
    conversation_id: uuid.UUID
    chosen_documents: Optional[List[str]] = None


class ConversationUpdate(BaseModel):
    conversation_name: str


# Chat Message: Do not create chat message delete API
class ChatMessageBase(BaseModel):
    chat_message_id: uuid.UUID
    message_data: dict

class ChatMessageResponse(ChatMessageBase):
    created_at: datetime
    parent_message_id: uuid.UUID = None

class ChatMessageCreate(BaseModel):
    conversation_id: Optional[str] = None
    message_content: str
    model_id: int = 3

class ChatDocCreate(ChatMessageCreate):
    chosen_documents: Optional[List[str]] = None
    chosen_csvs: Optional[List[str]] = None

class ChatMessageUpdate(BaseModel):
    message_content: str

class Usage(BaseModel):
    total_token: float
    total_cost: float
    total_request: int

class UserUsage(BaseModel):
    start_date: datetime
    end_date: datetime
    usage_type: str

class AllUsage(UserUsage):
    show_all: bool 

class SystemPromptCreate(BaseModel):
    chosen_position: str
    prompt_content: str
    temperature: float = 0.0
    presence_penalty: float = 0.0
    frequency_penalty: float = 0.0