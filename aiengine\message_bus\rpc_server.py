import asyncio
import aio_pika
import json
from loguru import logger
from aio_pika.abc import AbstractIncomingMessage


from core.config import settings
from message_bus.procedures.nlp_functions import embed_company_info, get_text_embedding, embed_company_info_test, get_text_embedding_test
from message_bus.procedures.generation_procedures import generate_drafts, generate_response, summarize_draft, html_to_text

# async def test_function(test_number: int):
#     return {"result": f"This is a test function, test {test_number}"}

RPC_FUNCTIONS = {
    # "test_function": test_function,
    "generate_response": generate_response,
    "embed_company_info": embed_company_info,
    "get_text_embedding": get_text_embedding,
    "get_text_embedding_test": get_text_embedding_test,
    "generate_drafts": generate_drafts,
    "summarize_draft": summarize_draft,
    # "html_to_text": html_to_text, 
    "embed_company_info_test": embed_company_info_test  
}

async def process_request(message: aio_pika.IncomingMessage):
    try:
        # Decode the request
        request_data = json.loads(message.body.decode())
        function_name = request_data.get("function")
        params = request_data.get("params", {})
        logger.info(f"Received RPC request: {function_name} | Reply to: {message.reply_to} | Correlation ID: {message.correlation_id}")

        # Fetch and execute the function
        rpc_func = RPC_FUNCTIONS.get(function_name)
        if rpc_func is not None:
            result = await rpc_func(**params)
        else:
            result = {"error": f"Unknown function: {function_name}"}
            # logger.error(result)

        # Ensure reply_to queue is available
        if not message.reply_to:
            logger.error("No reply_to queue specified, unable to send response")
            return

        # Prepare the response message
        response_body = json.dumps({"result": result}).encode()
        logger.info(f"Processed RPC: {function_name}")
        return response_body
    except Exception as e:
        logger.error(f"Error processing RPC request: {e}")
        return json.dumps({"result": "error", "detail": str(e)}).encode()

async def rpc_server(worker_id: int):
    logger.info(f"[RPC SERVER] Starting RPC worker {worker_id}")
    try:
        connection = await aio_pika.connect_robust(
            f"amqp://{settings.RABBITMQ_DEFAULT_USER}:{settings.RABBITMQ_DEFAULT_PASS}@{settings.RABBITMQ_HOST}:{settings.RABBITMQ_PORT}/"
        )
    except Exception as e:
        logger.error(f"[RPC SERVER] Worker {worker_id} Failed to connect to RabbitMQ: {e}")
        return
    

    async with connection:
        channel = await connection.channel()
        # try:
        #     await channel.queue_delete("aiengine_rpc_queue")
        #     logger.info(f"[RPC SERVER] Deleted existing queue: aiengine_rpc_queue")
        # except Exception as e:
        #     logger.warning(f"[RPC SERVER] Queue aiengine_rpc_queue might not exist yet. Proceeding...")    

        rpc_exchange = await channel.declare_exchange("rpc_exchange", type="direct", durable=True)
        queue = await channel.declare_queue("aiengine_rpc_queue", durable=True)
        await queue.bind(rpc_exchange, routing_key="aiengine.*")
 
        try:
            logger.info(f"[RPC SERVER] RPC worker {worker_id} is waiting for request")
            async with queue.iterator() as queue_iter:
                message: AbstractIncomingMessage
                async for message in queue_iter:
                    async with message.process(requeue=False):
                        response_body = await process_request(message)
                        await rpc_exchange.publish(
                            aio_pika.Message(
                                body=response_body,
                                correlation_id=message.correlation_id,
                            ),
                            routing_key=message.reply_to,
                        )
                        logger.info(f"[RPC SERVER] RPC response returned successfully.")
        except asyncio.CancelledError:
            logger.info(f" [RPC SERVER] RPC worker {worker_id} of service has been shutdown.")
        finally:
            await channel.close()
        
async def start_rpc_server(worker_count: int = 10):
    server = [rpc_server(i+1) for i in range(worker_count)]
    return await asyncio.gather(*server) 
