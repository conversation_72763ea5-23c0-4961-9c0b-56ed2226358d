import React from "react";

const claims = [
  {
    title: "Mortgage Miscalculation",
    icon: (
      // Simple house SVG
      <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
        <rect x="16" y="32" width="32" height="24" rx="2" fill="#e6efe3"/>
        <polygon points="32,16 12,32 52,32" fill="#d3e2d0"/>
        <rect x="28" y="44" width="8" height="12" rx="1" fill="#bfcdbb"/>
      </svg>
    ),
    href: "/mortgage",
  },
  {
    title: "Plevin PPI",
    icon: (
      // Simple document SVG
      <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
        <rect x="16" y="12" width="32" height="40" rx="2" fill="#e6efe3"/>
        <rect x="20" y="20" width="24" height="4" rx="1" fill="#f26c2a"/>
        <rect x="20" y="28" width="20" height="4" rx="1" fill="#bfcdbb"/>
        <rect x="20" y="36" width="16" height="4" rx="1" fill="#bfcdbb"/>
      </svg>
    ),
    href: "#plevin",
  },
  {
    title: "Mis-Sold Vehicle Finance",
    icon: (
      // Simple car SVG
      <svg width="64" height="64" viewBox="0 0 64 64" fill="none">
        <rect x="12" y="36" width="40" height="12" rx="4" fill="#e6efe3"/>
        <rect x="20" y="28" width="24" height="12" rx="4" fill="#f26c2a"/>
        <circle cx="20" cy="52" r="4" fill="#bfcdbb"/>
        <circle cx="44" cy="52" r="4" fill="#bfcdbb"/>
      </svg>
    ),
    href: "#vehicle",
  },
];

export function Hero() {
  return (
    <section className="w-full min-h-[60vh] flex flex-col items-center justify-center bg-gradient-to-br from-[#b7cdb2] to-[#a3b89e] py-12">
      <h1 className="text-4xl md:text-5xl font-bold text-[#f8f8f8] drop-shadow-lg mb-10 text-center">
        The Overpayment Recovery Specialists
      </h1>
      <div className="flex flex-col md:flex-row gap-8">
        {claims.map((claim) => (
          <div
            key={claim.title}
            className="bg-white rounded-xl shadow-lg flex flex-col items-center p-8 w-80 min-h-[320px] transition-transform hover:scale-105"
          >
            <div className="mb-6">{claim.icon}</div>
            <h2 className="text-xl font-semibold text-[#3d4b3d] mb-6 text-center">
              {claim.title}
            </h2>
            <a
              href={claim.href}
              className="mt-auto bg-[#6ca16e] hover:bg-[#5a8c5c] text-white font-medium px-6 py-2 rounded-lg transition-colors"
            >
              Start my claim
            </a>
          </div>
        ))}
      </div>
    </section>
  );
}
