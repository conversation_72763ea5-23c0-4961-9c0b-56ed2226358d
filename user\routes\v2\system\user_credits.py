from database.database import get_db
from routes.v2.system_error import BAD_REQUEST, USER_NOT_FOUND
from database.models import User, UserCredit, CreditTransaction, UsedCredit
from schema.credit_schemas import UsedCreditCreate
from utils.unipile_utils.unipile_client import LinkedInClient


from sqlalchemy import update
from sqlalchemy.future import select
from sqlalchemy.orm import Session
from fastapi import Header, Depends, status, APIRouter, HTTPException
from loguru import logger

# Create an API router
router = APIRouter(
    prefix="/api",
    tags=['User Credits Management'],
    responses={404: {'description': 'Not found'}},
)

@router.get("/users/credits", status_code=status.HTTP_200_OK)
async def get_user_credits(
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    try:
        query = select(UserCredit).filter(UserCredit.user_id==request_user_id)
        results = db.execute(query)
        user_credit = results.scalars().first()
        return user_credit
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.get("/users/credits/transactions", status_code=status.HTTP_200_OK)
async def get_user_credit_transactions(
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    try:
        query = select(CreditTransaction).filter(CreditTransaction.user_id==request_user_id).order_by(CreditTransaction.created_at.desc())
        results = db.execute(query)
        user_credit_transactions = results.scalars().all()

        bought_credits = 0
        for transaction in user_credit_transactions:
            bought_credits += transaction.credit_amount

        response = {
            "bought_credits": bought_credits,            
            "transactions": user_credit_transactions
        }
        return response
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.get("/users/{user_id}/credits/transactions", status_code=status.HTTP_200_OK)
async def get_user_credit_transactions(
    user_id: str,
    db: Session = Depends(get_db)
):
    try:
        query = select(CreditTransaction).filter(CreditTransaction.user_id==user_id).order_by(CreditTransaction.created_at.desc())
        results = db.execute(query)
        user_credit_transactions = results.scalars().all()

        bought_credits = 0
        for transaction in user_credit_transactions:
            bought_credits += transaction.credit_amount

        response = {
            "bought_credits": bought_credits,            
            "transactions": user_credit_transactions
        }
        return response
    
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )


@router.get("/users/credits/usage", status_code=status.HTTP_200_OK)
async def get_user_credit_usage(
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    try:
        query = select(UsedCredit).filter(UsedCredit.user_id==request_user_id).order_by(UsedCredit.created_at.desc())
        results = db.execute(query)
        user_credit_usage = results.scalars().all()
        return user_credit_usage
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )


@router.put("/users/credits/update-usage", status_code=status.HTTP_200_OK)
async def use_user_credits(
    used_credit: UsedCreditCreate,
    request_user_id: str = Header(None),
    db: Session = Depends(get_db)
):
    try:
        query = select(UserCredit).filter(UserCredit.user_id==request_user_id)
        results = db.execute(query)
        user_credit = results.scalars().first()

        used_credit = UsedCredit(
            user_id = request_user_id,
            campaign_id = used_credit.campaign_id,
            company_id = used_credit.company_id,
            campaign_name = used_credit.campaign_name,
            company_name = used_credit.company_name
        )
        db.add(used_credit)

        stmt = (
            update(UserCredit)
            .where(UserCredit.user_id==request_user_id)
            .values(current_credits=UserCredit.current_credits - 1, used_credits=UserCredit.used_credits + 1)
        )
        db.execute(stmt)
        db.commit()
        return {
            "status": "success"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )


# Export the router
user_credits_routes = router
