from functools import lru_cache
import secrets
from pydantic import BaseSettings


class Settings(BaseSettings):
    # SERVER_NAME: str
    # SERVER_HOST: str = "digitalocean.com"
    SERVER_PORT: int = 8000
    
    API_V2_STR: str = ""
    SECRET_KEY: str = secrets.token_urlsafe(32)

    # 60 minutes * 24 hours * 8 days = 8 days
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8

    ACCESS_TOKEN_URL = "/api/token"

    BACKEND_CORS_ORIGINS: list = ["*"]

    PROJECT_NAME: str

    DATABASE_NAME: str
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    DATABASE_HOST: str
    DATABASE_PORT: str

    RABBITMQ_DEFAULT_USER: str
    RABBITMQ_DEFAULT_PASS: str
    RABBITMQ_HOST: str
    RABBITMQ_PORT: int
    EXCHANGE_NAME: str

    JWT_SECRET: str
    JWT_ALGORITHM: str
    
    UNIPILE_SUBDOMAIN : str
    UNIPILE_PORT : str
    UNIPILE_API_KEY : str

    STRIPE_SECRET_KEY: str
    STRIPE_PUBLISHABLE_KEY: str

    # STRIPE_SECRET_KEY: str = "pk_test_51RPGBMRoVw66mhYyOsOg2dl5hYz3RSYwveIlUors410K49YgXcHLUg6PA5yn9nvsQ4TaK5p7QhDwhnBYFbgehnyo00bKzOdF94"
    # STRIPE_PUBLISHABLE_KEY: str = "sk_test_51RPGBMRoVw66mhYyZUi2okZEUUM8e3jTwpcfd8qoSvsCktOHkbpPRUhSskL7XopqmWODtXA8LXfPquhgEDywj98800MaKo9YPS"    

    class Config:
        env_file = ".env"
        case_sensitive = True

@lru_cache()
def get_settings():
    return Settings()

settings = get_settings()