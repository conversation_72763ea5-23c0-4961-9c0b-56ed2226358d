import requests
import json
from loguru import logger
from sqlalchemy import update, delete
from sqlalchemy.future import select
from sqlalchemy.orm import aliased
import asyncio
from sqlalchemy.ext.asyncio import  AsyncSession
from datetime import datetime, timedelta, time

from utils.unipile_utils.unipile_client import EmailClient, LinkedInClient
from database.database import SessionFactory, get_session
from database.models import SentEmail, ReceivedEmail, Company, Draft, Representative, SenderEmail, Campaign
from message_bus.task_publisher import publish_task
from message_bus.rpc_client import RPCClient
from shared_state import app_state


#region WEBHOOKS
async def receive_reply_webhooks(message):
    await asyncio.sleep(1)
    try:
        async for db in get_session():   
            try:
                json_string = message.body.decode()
                message_body = json.loads(json_string)        
                webhooks_new_email = message_body      
                unipile_account_id = webhooks_new_email["account_id"]
                from_address = webhooks_new_email["from_attendee"]["identifier"]
                in_reply_to = webhooks_new_email["in_reply_to"]
                received_body = webhooks_new_email["body"].replace("\n", "")
                received_body_plain = webhooks_new_email["body_plain"].replace(">", "")
                logger.info(f"[CONSUMER] Processing receive_reply_webhooks from {from_address}")
                # logger.info("email_id: " + webhooks_new_email["email_id"])
                # logger.info("account_id: " + unipile_account_id)
                # logger.info("provider_id: "+webhooks_new_email["provider_id"])
                # logger.info("message_id: "+webhooks_new_email["message_id"])
                # logger.info("in_reply_to: "+str(in_reply_to))
                # logger.info("origin: "+webhooks_new_email["origin"])
                query = select(ReceivedEmail).where(ReceivedEmail.message_id==webhooks_new_email["message_id"])
                results = await db.execute(query)
                existing_reply_email = results.scalars().first()
                if existing_reply_email is None and in_reply_to is not None:    
                    reply_to_id = in_reply_to["id"]
                    reply_to_message_id = in_reply_to["message_id"]
                    query = (
                        select(SentEmail).filter(SentEmail.message_id==reply_to_message_id)
                    )

                    results = await db.execute(query)
                    sent_email = results.scalars().first()
                    if sent_email:
                        company_id = sent_email.company_id
                        query = select(Company).filter(Company.company_id==company_id)
                        results = await db.execute(query)
                        company = results.scalars().first()

                        query = select(Campaign).filter(Campaign.campaign_id==company.campaign_id)
                        results = await db.execute(query)
                        campaign = results.scalars().first()

                        own_name = campaign.campaign_contact["user_nick_name"]

                        if company.email_confirmation_status_id == 7:
                            logger.info(f"[CONSUMER] The prospect's reply has already been responded to.")
                            return

                        query = select(Representative).filter(Representative.company_id==company_id)
                        results = await db.execute(query)
                        representative = results.scalars().first()
                        personality_analysis = representative.personality_analysis
                        email_personalization = representative.email_personalization
                        #rpc to generate response
                        service = "aiengine.*"
                        function_name = "generate_response"
                        params = {
                            "sent_email": {
                                "sender_info": {
                                    "name": own_name
                                },
                                "subject": sent_email.subject,
                                "body": sent_email.body_plain,
                            }, 
                            "reply_email": {
                                "subject": webhooks_new_email["subject"],
                                "body": webhooks_new_email["body_plain"],
                            },  
                            "personality_analysis": personality_analysis,
                            "email_personalization": email_personalization
                        }

                        rpc = await RPCClient().connect()
                        response = await rpc.call(service=service, function_name=function_name, params=params)
                        response_body = json.loads(response)
                        result = response_body.get("result")   

                        to_address = webhooks_new_email["to_attendees"][0]["identifier"]
                        new_received_email = ReceivedEmail(
                            account_id = unipile_account_id,
                            company_id = company_id,
                            unipile_id = webhooks_new_email["email_id"],
                            subject = webhooks_new_email["subject"],
                            body = received_body,
                            body_plain = received_body_plain,
                            from_address = from_address,
                            from_info = webhooks_new_email["from_attendee"],
                            to_addresses = [to_address],
                            to_info = webhooks_new_email["to_attendees"],
                            message_id = webhooks_new_email["message_id"],
                            reply_to_message_id = reply_to_message_id,
                            reply_to_email_id = reply_to_id,
                            sent_email_internal_id = sent_email.internal_id,
                            sent_email_unipile_id = sent_email.unipile_id,
                            sentiment = result["sentiment"],
                            emotions = result["emotions"],
                            suggested_response = {
                                "subject": result["response_subject"],
                                "body": result["response_body"]
                            },
                            explanation = result["explanation"],
                            key_takeaways = result["key_takeaways"]
                        )
                        db.add(new_received_email)
                        stmt = update(Company).where(Company.company_id==company_id).values(email_confirmation_status_id=6)
                        await db.execute(stmt)

                        stmt = update(SentEmail).where(SentEmail.internal_id==sent_email.internal_id).values(email_replied=True)
                        await db.execute(stmt)

                        logger.info(f"[CONSUMER] Received a reply from {from_address} successfully")

                        query = select(Company).filter(Company.company_id==company_id)
                        result = await db.execute(query)
                        company = result.scalars().first()

                        query = select(Campaign).where(Campaign.campaign_id==company.campaign_id)
                        result = await db.execute(query)
                        campaign = result.scalars().first()
                        campaign_contact = campaign.campaign_contact
                        from_name = "Robert Mandev"
                        if campaign_contact is not None:
                            if campaign_contact.get("user_nick_name") is not None:
                                    from_name = campaign_contact["user_nick_name"]

                        await db.commit()
                        if company.auto_outreach == True:
                            message = {
                                "subject": new_received_email.suggested_response["subject"],
                                "body": new_received_email.suggested_response["body"],
                                "from_name": from_name,
                                "to_name": new_received_email.from_info["display_name"],
                                "to_address": new_received_email.from_address,
                                "company_id": str(company_id)
                            }
                            await publish_task("send_response", message_body=message, delay_seconds=10, connection_pool=app_state["rabbitmq_conn_pool"])
                    else:
                        logger.warning(f"[CONSUMER] Sent email not found")                       
                else:   
                    logger.info(f"[CONSUMER] The email wasn't a reply or it was received already")                
            except Exception as e:
                await db.rollback()
                raise e
    except Exception as e:
        logger.error(f"[CONSUMER] Failed to received email: {str(e)}")

async def receive_sent_webhooks(message):
    await asyncio.sleep(1)
    try:
        async for db in get_session():
            try:
                json_string = message.body.decode()
                message_body = json.loads(json_string)        
                webhooks_new_email = message_body       
                # to_address = webhooks_new_email["to_attendees"][0]["identifier"]        
                email_id = webhooks_new_email["email_id"]
                logger.info(f"[CONSUMER] Processing receive_sent_webhooks of sender "+webhooks_new_email["from_attendee"]["identifier"])
                # logger.info("email_id: " + webhooks_new_email["email_id"])
                # logger.info("account_id: " + webhooks_new_email["account_id"])
                # logger.info("provider_id: "+webhooks_new_email["provider_id"])
                # logger.info("message_id: "+webhooks_new_email["message_id"])
                # logger.info("in_reply_to: "+str(webhooks_new_email["in_reply_to"]))
                # logger.info("tracking_id: "+webhooks_new_email["tracking_id"])
                # logger.info("origin: "+webhooks_new_email["origin"])                
                stmt = (
                    update(SentEmail)
                    .where(SentEmail.tracking_id==webhooks_new_email["tracking_id"])
                    .values(
                        unipile_id = webhooks_new_email["email_id"],
                        subject = webhooks_new_email["subject"],
                        from_address = webhooks_new_email["from_attendee"]["identifier"],
                        from_info = webhooks_new_email["from_attendee"],
                        # to_addresses = [to_address],
                        # to_info = webhooks_new_email["to_attendees"],
                        message_id = webhooks_new_email["message_id"]
                    )
                )
                await db.execute(stmt)
                
                query = select(SentEmail.company_id).where(SentEmail.tracking_id==webhooks_new_email["tracking_id"])
                results = await db.execute(query)
                company_id = results.scalars().first()


                stmt = update(Company).filter(Company.company_id==company_id, Company.email_confirmation_status_id.notin_([4,5,6])).values(email_confirmation_status_id=4)
                await db.execute(stmt)
                await db.commit()
                logger.info(f"[CONSUMER] Email {email_id} was sent successfully")

            except Exception as e:
                await db.rollback()
                # logger.info(str(webhooks_new_email))
                # logger.error(str(e))
                raise e
    except Exception as e:
        logger.error(f"[CONSUMER] failed to process task receive_sent_webhooks: {str(e)}")

async def receive_tracking_webhooks(message):
    await asyncio.sleep(1)
    try:
        json_string = message.body.decode()
        message_body = json.loads(json_string)        
        webhooks_email_tracking = message_body        
        async for db in get_session():
            try:
                tracking_id = webhooks_email_tracking["tracking_id"]
                query = select(SentEmail.company_id).where(SentEmail.tracking_id==tracking_id)
                results = await db.execute(query)
                company_id = results.scalars().first()

                if company_id:
                    query = select(Company).where(Company.company_id==company_id)
                    results = await db.execute(query)
                    company = results.scalars().first()  
                    if company.email_confirmation_status_id == 4:
                        stmt = update(Company).where(Company.company_id==company_id).values(email_confirmation_status_id=5)
                        await db.execute(stmt)
                        
                        if webhooks_email_tracking["event"] == "mail_opened":
                            stmt = update(SentEmail).where(SentEmail.tracking_id==tracking_id).values(email_opened=True, unipile_id=webhooks_email_tracking["email_id"])
                            await db.execute(stmt)
                            await db.commit()                            
                            logger.info(f"[CONSUMER] Email sent to company_id: {company_id} has been opened")
                            
                        elif webhooks_email_tracking["event"] == "link_clicked":
                            stmt = update(SentEmail).where(SentEmail.tracking_id==tracking_id).values(email_link_clicked=True, unipile_id=webhooks_email_tracking["email_id"])
                            await db.execute(stmt)
                            await db.commit()
                            logger.info(f"[CONSUMER] link in email sent to company_id: {company_id} has been clicked")
            except Exception as e:
                await db.rollback()
                raise e
    except Exception as e:
        logger.error(f"[CONSUMER] failed to process task receive_tracking_webhooks: {str(e)}")

#endregion