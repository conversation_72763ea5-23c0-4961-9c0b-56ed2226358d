from database.database import get_session
from utils.elasticsearch_utils.get_elasticsearch import get_elastic
from utils.unipile_utils.unipile_client import EmailClient, LinkedInClient
from message_bus.task_publisher import publish_task
from message_bus.rpc_client import RPCClient
from shared_state import app_state
from core.config import settings
from schema.campaign_schema import (CampaignCreate, 
                                    CampaignDetails, CampaignContentUpdate, 
                                    CampaignSendLinkedInMessage, CampaignDetailsUpdate,
                                    CampaignSendLinkedInInvitation,
                                    TestScheduleJob)
from schema.company_schema import (CompanyEmailUpdate, CompanyAddManual, 
                                   CompanyLinkedInMsgUpdate)
from schema.rep_schema import (RepUpdate)

from schema.webhooks_schema import (WebhooksEmailTracking, WebhooksNewEmail, WebhooksNewMessage)

from schema.outreach_schema import (EmailGeneration, EmailSend,
                                    CreateDraftEmail,EmailOutreach,
                                    UpdateDraftEmail, SendEmailRequest, SendLinkedInRequest, 
                                    RespondEmail, RespondMessage)

from database.models import (Campaign, Company, 
                             Representative, SentEmail, 
                             ReceivedEmail, Draft,
                             EmailConfirmationStatus, SenderEmail,
                             HumanticProfile, LinkedInConnection, SentMessage, ReceivedMessage)

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import update, delete, func, distinct
from sqlalchemy.future import select
from sqlalchemy.orm import aliased
from fastapi import APIRouter, Header, Depends, Query
from fastapi import HTTPException, status, Request
from fastapi import status, File, UploadFile
from sqlalchemy.future import select
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk, BulkIndexError
from typing import List
import uuid
import pandas as pd
from io import StringIO
import requests
from loguru import logger
import asyncio
import json
from bs4 import BeautifulSoup

router = APIRouter(
    prefix = "/api",
    tags=['Linkedin Outreach Management'],
    responses={404: {'description': 'Not found'}},
)


#region LINKEDIN OUTREACH
@router.post("/campaign/linkedin/messages/send")
async def send_linked_message(
    linkedin_message: SendLinkedInRequest,
    request_user_id : str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    try:
        rpc_client = RPCClient()
        rpc = await rpc_client.connect()
        function_name = "get_user_info"
        response = await rpc.call(service="user.*", function_name=function_name, params={"user_id": request_user_id})
        response_body = json.loads(response)
        result = response_body.get("result")
        if result == "error":
            raise Exception(
                "Error from RPC server: " + response_body.get("detail")
            )
        if result.get("unipile_linkedin_id") is None:
            raise Exception("The account appears to be disconnected from the unipile service.")

        messages_sent = 0
        # company_id = linkedin_message.company_id_list[0]
        for company_id in linkedin_message.company_id_list:
            query = select(Representative).filter(Representative.company_id==company_id)
            results = await db.execute(query)
            representative = results.scalars().first()

            if representative.rep_linkedin_address is None:
                continue

            query = select(Draft).filter(Draft.company_id==company_id, Draft.linkedin_sent==False).order_by(Draft.draft_type.asc())
            result = await db.execute(query)
            drafts = result.scalars().all()

            query = select(Company).where(Company.company_id==company_id)
            result = await db.execute(query)
            company = result.scalars().first()
            if company.linkedin_message_status_id == 4:
                logger.warning(f"prospect has already replied to the outreach attempt")
                continue

            if len(drafts) <= 0:
                logger.warning(f"no more draft message to send for company {company_id}")
                continue
            
            if company.auto_outreach == True:
                logger.warning(f"company is already set to auto linkedin outreach")
                continue

            message = {
                "company_id": str(company_id),
                "user_id": str(request_user_id),
            }
            await publish_task("send_linkedin_outreach", message, connection_pool=app_state["rabbitmq_conn_pool"])
            messages_sent += 1                
        return {
            "status": "success",
            "detail": f"{messages_sent} messages are set to be sent out of {len(linkedin_message.company_id_list)} companies as requested"
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.post("/campaign/linkedin/message/respond")
async def respond_linkedin_message(
    respond_message: RespondMessage,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)    
):
    try:
        rpc_client = RPCClient()
        rpc = await rpc_client.connect()
        function_name = "get_user_info"
        response = await rpc.call(service="user.*", function_name=function_name, params={"user_id": request_user_id})
        response_body = json.loads(response)
        result = response_body.get("result")
        if result == "error":
            raise Exception(
                "Error from RPC server: " + response_body.get("detail")
            )
        if result.get("unipile_linkedin_id") is None:
            raise Exception("The account appears to be disconnected from the unipile service.")
        
        query = select(Company).filter(Company.company_id==respond_message.company_id)
        results = await db.execute(query)
        company = results.scalars().first()

        if company.auto_outreach:
            raise Exception("The company is set to auto outreach. You cannot respond to a message manually.")

        if company.linkedin_message_status_id == 5:
            raise Exception("The prospect's reply has already been responded to.")

        query = select(ReceivedMessage).where(ReceivedMessage.company_id==respond_message.company_id).order_by(ReceivedMessage.created_at.desc())
        results = await db.execute(query)
        received_message = results.scalars().first()
        if received_message is None:
            raise Exception("The prospect hasn't replied to the outreach attempt.")
        
        query = select(Representative).filter(Representative.company_id==respond_message.company_id)
        results = await db.execute(query)
        representative = results.scalars().first()

        client = LinkedInClient()
        client.set_unipile_account_id(result["unipile_linkedin_id"])
        own_profile = client.retrieve_own_profile().json()
        prospect_profile = client.retrieve_profile(representative.rep_linkedin_address).json()
        response = client.send_message(message_body=respond_message.message_content, recipient_profile_url=representative.rep_linkedin_address)
        if response.status_code not in [200,201]:
            raise Exception("Failed to send response to linkedin message")
        
        else:
            new_message = SentMessage(
                message_content = respond_message.message_content,
                account_id = result["unipile_linkedin_id"],
                company_id = respond_message.company_id,
                chat_id = received_message.chat_id,
                unipile_id = response.json()["message_id"],
                sender_username = own_profile["public_identifier"],
                sender_provider_id = own_profile["provider_id"],
                recipient_username = prospect_profile["public_identifier"],
                recipient_provider_id = prospect_profile["provider_id"],
                recipient = received_message.sender
            )
            db.add(new_message)
            await db.commit()
            return {
                "status": "success"
            }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )
        

@router.get("/campaign/{campaign_id}/linkedin/message/sent-messages")
async def get_sent_messages(
    campaign_id : uuid.UUID,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)    
):
    try:
        query = select(Company).where(Company.campaign_id==campaign_id).order_by(Company.created_at.desc())
        results = await db.execute(query)
        company_list = results.scalars().all()

        result = []

        for company in company_list:
            query = select(Representative).filter(Representative.company_id==company.company_id)
            results = await db.execute(query)
            representative = results.scalars().first()

            query = (
                select(SentMessage)
                .filter(SentMessage.company_id==company.company_id)
                .order_by(SentMessage.created_at.asc())
            )
            results = await db.execute(query)
            sent_messages_list = results.scalars().all()

            if len(sent_messages_list) > 0:
                result.append({
                    "company_id": company.company_id,
                    "company_name": company.company_name,
                    "rep_name": representative.rep_name,
                    "rep_linkedin_address": representative.rep_linkedin_address,
                    "sent_messages": sent_messages_list
                })
        return result

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )
    
@router.get("/campaign/companies/{company_id}/linkedin/reply-messages")
async def get_reply_messages(
    company_id : uuid.UUID,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)        
):
    try:
        query = select(ReceivedMessage).where(ReceivedMessage.company_id==company_id).order_by(ReceivedMessage.created_at.asc())
        results = await db.execute(query)
        reply_messages_list = results.scalars().all()
        return reply_messages_list

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )

@router.get("/campaign/companies/{company_id}/linkedin/messages/chat")
async def get_chat_messages(
    company_id : uuid.UUID,
    request_user_id: str = Header(None),
    db: AsyncSession = Depends(get_session)
):
    try:
        messages_list = []

        query = (
            select(SentMessage)
            .filter(SentMessage.company_id==company_id, SentMessage.draft_type==0)
        )
        results = await db.execute(query)        
        initial_sent_message = results.scalars().first()

        if initial_sent_message is None:
            return messages_list

        start_date = initial_sent_message.created_at

        # query = (
        #     select(SentMessage)
        #     .filter(SentMessage.chat_id==initial_sent_message.chat_id, SentMessage.created_at>=start_date)
        #     .order_by(SentMessage.created_at.asc())
        # )       
        # results = await db.execute(query)
        # sent_messages_list = results.scalars().all()

        # query = (
        #     select(ReceivedMessage)
        #     .filter(ReceivedMessage.chat_id==initial_sent_message.chat_id, ReceivedMessage.created_at>=start_date)
        #     .order_by(ReceivedMessage.created_at.asc())
        # )
        # results = await db.execute(query)
        # received_messages_list = results.scalars().all()

        query = (
            select(SentMessage)
            .filter(SentMessage.company_id==initial_sent_message.company_id, SentMessage.created_at>=start_date)
            .order_by(SentMessage.created_at.asc())
        )       
        results = await db.execute(query)
        sent_messages_list = results.scalars().all()

        query = (
            select(ReceivedMessage)
            .filter(ReceivedMessage.company_id==initial_sent_message.company_id, ReceivedMessage.created_at>=start_date)
            .order_by(ReceivedMessage.created_at.asc())
        )
        results = await db.execute(query)
        received_messages_list = results.scalars().all()        

        for sent_message in sent_messages_list:
            message = {
                "message_content": sent_message.message_content,
                "timestamp": sent_message.created_at,
                "sender": sent_message.sender,
                "role": "user"
            }
            messages_list.append(message)

        for received_message in received_messages_list:
            message = {
                "message_content": received_message.message_content,
                "timestamp": received_message.created_at,
                "sender": received_message.sender,
                "suggested_response": received_message.suggested_response,
                "explanation": received_message.explanation,
                "emotions": received_message.emotions,
                "sentiment": received_message.sentiment,
                "key_takeaways": received_message.key_takeaways,
                "role": "prospect"
            }
            messages_list.append(message)

        #sort messages_list on timestamp
        messages_list = sorted(messages_list, key=lambda x: x["timestamp"])

        return messages_list

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        )


#endregion

linkedin_outreach_routes = router
