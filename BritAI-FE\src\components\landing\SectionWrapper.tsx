import React from "react";
import { cn } from "@/lib/utils";

interface SectionWrapperProps {
  id?: string;
  full?: boolean;
  className?: string;
  children: React.ReactNode;
}

export function SectionWrapper({
  id,
  full = false,
  className,
  children,
}: SectionWrapperProps) {
  return (
    <section
      id={id}
      className={cn(
        "flex flex-col px-4",
        full && "min-h-screen",
        className
      )}
    >
      <div className="flex flex-col max-w-[1400px] mx-auto w-full">
        {children}
      </div>
    </section>
  );
}
