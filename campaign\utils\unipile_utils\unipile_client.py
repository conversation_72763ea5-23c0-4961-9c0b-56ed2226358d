import requests
import json

from core.config import settings
from utils.unipile_utils.unipile_request_wrapper import unipile_get, unipile_post, unipile_delete


class UnipileClient:
    unipile_subdomain: str = settings.UNIPILE_SUBDOMAIN
    unipile_port: str = settings.UNIPILE_PORT    
    unipile_api_key: str = settings.UNIPILE_API_KEY
    unipile_account_id: str = None

    def __init__(self):
        pass    

    def set_unipile_account_id(self, value: str) -> None:
        self.unipile_account_id = value    

    def retrieve_account(self) -> requests.Response:
        endpoint_path = f"/accounts/{self.unipile_account_id}"
        response = unipile_get(endpoint_path=endpoint_path)
        return response

    def delete_account(self, unipile_account_id: str) -> requests.Response:
        endpoint_path = f"/accounts/{unipile_account_id}"
        response = unipile_delete(endpoint_path=endpoint_path)
        return response    
    
    def connect_imap_account(
        self,
        imap_user: str,
        smtp_user: str,
        imap_password: str,
        smtp_password: str,
        imap_host: str,
        smtp_host: str,
        imap_port: int,
        smtp_port: int
    ) -> requests.Response:
        
        endpoint_path = f"/accounts"
        payload = {
            "provider": "MAIL",
            "smtp_port": smtp_port,
            "smtp_host": smtp_host,
            "imap_port": imap_port,
            "imap_host": imap_host,
            "smtp_password": smtp_password,
            "imap_password": imap_password,
            "smtp_user": smtp_user,
            "imap_user": imap_user,
            "imap_encryption": "SSL"
        }
        response = unipile_post(endpoint_path=endpoint_path, json=payload)
        return response

    def reconnect_imap_account(
        self,
        account_id: str,
        imap_user: str,
        smtp_user: str,
        imap_password: str,
        smtp_password: str,
        imap_host: str,
        smtp_host: str,
        imap_port: int,
        smtp_port: int
    ) -> requests.Response:
        
        endpoint_path = f"/accounts/{account_id}"
        payload = {
            "provider": "MAIL",
            "smtp_port": smtp_port,
            "smtp_host": smtp_host,
            "imap_port": imap_port,
            "imap_host": imap_host,
            "smtp_password": smtp_password,
            "imap_password": imap_password,
            "smtp_user": smtp_user,
            "imap_user": imap_user,
            "imap_encryption": "SSL"
        }
        response = unipile_post(endpoint_path=endpoint_path, json=payload)
        return response

class LinkedInClient(UnipileClient):
    def __init__(self):
        pass

    def retrieve_profile(self, profile_url: str) -> requests.Response:
        profile_id = profile_url.split('/')[-2] if profile_url.endswith('/') else profile_url.split('/')[-1]
        endpoint_path = f"/users/{profile_id}?account_id={self.unipile_account_id}"

        response = unipile_get(endpoint_path=endpoint_path)
        return response

    def retrieve_own_profile(self) -> requests.Response:
        endpoint_path = f"/users/me?account_id={self.unipile_account_id}"
        response = unipile_get(endpoint_path=endpoint_path)
        return response

    def retrieve_connections(self) -> requests.Response:
        endpoint_path = f"/users/relations?account_id={self.unipile_account_id}"
        response = unipile_get(endpoint_path=endpoint_path)
        return response

    def send_message(self, message_body: str, recipient_profile_url: str) -> requests.Response:
        recipient_profile = self.retrieve_profile(recipient_profile_url).json()
        recipient_id = recipient_profile["provider_id"]
        
        endpoint_path = f"/chats"
        boundary = "-----011000010111000001101001"
        payload = (
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"attendees_ids\"\r\n\r\n"
            f"{recipient_id}\r\n"
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"account_id\"\r\n\r\n"
            f"{self.unipile_account_id}\r\n"
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"text\"\r\n\r\n"
            f"{message_body}\r\n"
            f"{boundary}--"
        )
        response = unipile_post(endpoint_path=endpoint_path, form_data=payload)
        return response

    def send_invitation(self, message_body: str, recipient_profile_url: str) -> requests.Response:
        endpoint_path = "/users/invite"
        recipient_profile = self.retrieve_profile(recipient_profile_url).json()
        recipient_id = recipient_profile["provider_id"]        
        payload = {
            "provider_id": recipient_id,
            "account_id": self.unipile_account_id,
            "message": message_body
        }   
        response = unipile_post(endpoint_path=endpoint_path, json=payload)
        return response

class EmailClient(UnipileClient):
    def __init__(self):
        pass

    def send_email(self, subject: str, content: str, name_from: str, name_to: str, email_to: str) -> requests.Response:

        resp = self.retrieve_account()     

        if resp.status_code != 200:
            return resp

        email_account = resp.json()
        email_from = email_account['name']

        boundary = "-----011000010111000001101001"
        recipient_info = [
            {
                "display_name": name_to.title(),
                "identifier": email_to
            }            
        ]

        sender_info = {
            "display_name": name_from,
            "identifier": email_from            
        }
        
        tracking_options = {
            "opens" : True,
            "label" : "new_email_sent"
        }

        endpoint_path = "/emails"
        
        payload = (
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"account_id\"\r\n\r\n"
            f"{self.unipile_account_id}\r\n"
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"subject\"\r\n\r\n"
            f"{subject}\r\n"
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"body\"\r\n\r\n"
            f"{content}\r\n"
            f"{boundary}\r\n"
            "Content-Disposition: form-data; name=\"from\"\r\n\r\n"
            f"{json.dumps(sender_info)}\r\n"
            f"{boundary}\r\n"            
            "Content-Disposition: form-data; name=\"to\"\r\n\r\n"
            f"{json.dumps(recipient_info)}\r\n"
            f"{boundary}\r\n"              
            "Content-Disposition: form-data; name=\"tracking_options\"\r\n\r\n"
            f"{json.dumps(tracking_options)}\r\n"
            f"{boundary}--"
        )             
        response = unipile_post(endpoint_path=endpoint_path, form_data=payload)
        return response
    
    def retrieve_email(self, email_id: str) -> requests.Response:
        endpoint_path = f"/emails/{email_id}"
        response = unipile_get(endpoint_path=endpoint_path)
        return response