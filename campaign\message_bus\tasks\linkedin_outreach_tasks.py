import requests
import json
import re
from loguru import logger
from sqlalchemy import update, delete, distinct
from sqlalchemy.future import select
from sqlalchemy.orm import aliased
import asyncio
from sqlalchemy.ext.asyncio import  AsyncSession
from datetime import datetime, timedelta, time, date

from utils.unipile_utils.unipile_client import Email<PERSON>lient, LinkedInClient
from database.database import SessionFactory, get_session
from database.models import SentEmail, ReceivedEmail, Company, Draft, Representative, SenderEmail, Campaign, LinkedInConnection, SentMessage
from message_bus.task_publisher import publish_task
from message_bus.rpc_client import RPCClient
from shared_state import app_state



#region LINKEDIN OUTREACH
async def update_message_db(
    db: AsyncSession, 
    draft_message: Draft, 
    account_id: str, 
    message_content: str, 
    own_profile: dict,
    prospect_profile: dict,
    message_id: str = None, 
    chat_id: str = None):

    stmt = update(Draft).where(Draft.draft_id==draft_message.draft_id).values(linkedin_sent=True)
    await db.execute(stmt)

    new_message = SentMessage(
        message_content = message_content,
        account_id = account_id,
        company_id = draft_message.company_id,
        chat_id = chat_id,
        unipile_id = message_id,
        draft_type = draft_message.draft_type,
        sender_username = own_profile["public_identifier"],
        sender_provider_id = own_profile["provider_id"],
        recipient_username = prospect_profile["public_identifier"],
        recipient_provider_id = prospect_profile["provider_id"]
    )
    db.add(new_message)
    stmt = update(Company).filter(Company.company_id==draft_message.company_id).values(
        linkedin_message_status_id=2, 
        linkedin_outreach_progress=draft_message.draft_type,
        last_date_sent_linkedin = date.today(),
        last_time_sent_linkedin = datetime.now()
    )
    await db.execute(stmt)
    
async def get_user_info(user_id: str):
    """
    return value struct.\n
    {
        "user_id": str
        "user_info": dict
        "user_linkedin_info": dict
        "unipile_linkedin_id": str
        "is_active": bool
        "birth_year": int
        "email": str
        "verified": bool
        "linkedin_connection_status": str
    }
    """
    rpc_client = RPCClient()
    rpc = await rpc_client.connect()
    response = await rpc.call(service="user.*", function_name="get_user_info", params={"user_id": user_id})
    response_body = json.loads(response)
    result = response_body.get("result")
    if result == "error":
        raise Exception(
            "Error from RPC server: " + response_body.get("detail")
        )
    if result.get("unipile_linkedin_id") is None:
        raise Exception("The account appears to be disconnected from the unipile service.") 
    
    if result.get("linkedin_connection_status") != "CONNECTED":
        raise Exception("The account appears to be disconnected from the unipile service.")
    
    return result 

async def send_linkedin_outreach(message):
    """
    Send an email to the recipient.
    message_body structure:\n
        {
            "company_id": str
            "user_id": str
        }
    """
    await asyncio.sleep(1)
    try:
        json_string = message.body.decode()
        message_body = json.loads(json_string)
        company_id = message_body["company_id"]
        user_id = message_body["user_id"]
        logger.info(f"[CONSUMER] Processing send_linkedin_outreach task")

        user_info = await get_user_info(user_id=user_id)   
        async for db in get_session():
            query = select(Company).filter(Company.company_id==company_id)
            result = await db.execute(query)
            company = result.scalars().first()
            if company is None:
                raise Exception("Company not found")

            if company.linkedin_message_status_id in [4,5]:
                logger.info(f"[CONSUMER] stopped sending linkedin message to company {company_id}")
                return

            query = select(Campaign.campaign_contact).filter(Campaign.campaign_id==company.campaign_id)
            results = await db.execute(query)
            campaign_contact = results.scalars().first()
            calendly_link = campaign_contact.get("calendly_link")

            query = select(Draft).filter(Draft.company_id==company_id, Draft.linkedin_sent==False).order_by(Draft.draft_type.asc())
            result = await db.execute(query)
            drafts = result.scalars().all()
            if len(drafts) <= 0:
                logger.info(f"[CONSUMER] no more messages to company {company_id}")
            else:
                draft = drafts[0]
                linkedin_client = LinkedInClient()
                linkedin_client.set_unipile_account_id(user_info["unipile_linkedin_id"])        
                own_profile = linkedin_client.retrieve_own_profile().json()
                if "provider_id" not in own_profile:
                    raise Exception("Failed to retrieve own profile to send linkedin message")
                prospect_profile = linkedin_client.retrieve_profile(draft.to_linkedin_address).json()
                message_content = draft.body_plain
                message_content = message_content.strip()
                message_content = message_content.replace("email","message")
                message_content = message_content.replace("Email","Message")

                pattern = r'(?<!\w\.\w.)(?<![A-Z][a-z]\.)(?<=\.|\?|!)\s'
                sentences = re.split(pattern, message_content)
                sentences = [sentence.strip() for sentence in sentences if sentence.strip()]
                final_text = "\n".join(sentences)
                message_content = final_text.strip()

                if calendly_link is not None:
                    message_content = message_content + f"\nBook a meeting on Calendly: {calendly_link}"

                rpc = await RPCClient().connect()
                # function_name = "html_to_text"
                # params = {
                #     "html_content": draft.body
                # }
                # rpc_response = await rpc.call(service="aiengine.*", function_name=function_name, params=params)
                # rpc_response_body = json.loads(rpc_response)
                # result = rpc_response_body.get("result")
                # if result != "error":
                #     message_content = result["plain_text"]


                response = linkedin_client.send_message(message_body=message_content, recipient_profile_url=draft.to_linkedin_address)
                if response.status_code == 422:
                    function_name = "summarize_draft"
                    params = {
                        "draft_content": draft.body_plain
                    }
                    rpc_response = await rpc.call(service="aiengine.*", function_name=function_name, params=params)
                    rpc_response_body = json.loads(rpc_response)
                    result = rpc_response_body.get("result")
                    if result == "error":
                        raise Exception(
                            "Can't summarize draft. Error from RPC server: " + rpc_response_body.get("detail")
                        )
                    summarized_draft = result["summarized_content"].replace("\n"," ").strip()
                    logger.info(f"summarized draft contains: {len(summarized_draft)} characters")
                    response = linkedin_client.send_invitation(message_body=str(summarized_draft), recipient_profile_url=draft.to_linkedin_address)                

                if response.status_code not in [200,201]:
                    raise Exception(f"Failed to send linkedin message to {draft.to_linkedin_address}\n {response.text}")
                
                message_id = None
                chat_id = None
                if "message_id" in response.json() and "chat_id" in response.json():
                    message_id = response.json()["message_id"]
                    chat_id = response.json()["chat_id"]
                    message_content = draft.body_plain
                    query = (
                        select(LinkedInConnection).select_from(LinkedInConnection)
                        .where(
                            LinkedInConnection.provider_id==own_profile["provider_id"],
                            LinkedInConnection.connected_provider_id==prospect_profile["provider_id"]
                        )
                               
                    )
                    
                    result = await db.execute(query)
                    linkedin_connection = result.scalars().first()
                    if linkedin_connection is None:
                        new_connection = LinkedInConnection(
                            provider_id = own_profile["provider_id"],
                            connected_provider_id = prospect_profile["provider_id"]                            
                        )
                        db.add(new_connection)   
                else:
                    message_content = summarized_draft

                await update_message_db(
                    db=db, 
                    draft_message=draft, 
                    account_id=user_info["unipile_linkedin_id"], 
                    message_id=message_id, 
                    message_content=message_content,
                    chat_id=chat_id,                    
                    own_profile=own_profile,
                    prospect_profile=prospect_profile,
                )

                query = (
                    select(LinkedInConnection).select_from(LinkedInConnection)
                    .where(
                        LinkedInConnection.provider_id==own_profile["provider_id"],
                        LinkedInConnection.connected_provider_id==prospect_profile["provider_id"]
                    )
                )
                result = await db.execute(query)
                linkedin_connection = result.scalars().first()

                await db.commit()
                logger.info(f"[CONSUMER] Successfully sent linkedin draft {draft.draft_type} to {draft.to_linkedin_address}")

    except Exception as e:
        logger.error(f"[CONSUMER] Failed to process send_linkedin_outreach task\n {str(e)}")



#endregion