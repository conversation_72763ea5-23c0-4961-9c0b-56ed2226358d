from core.config import settings 
import requests


def create_humantic_profile(rep_linkedin) -> requests.Response:
    url = f"{settings.HUMANTIC_AI_BASE_URL}/create"
    params = {
        "apikey": settings.HUMANTIC_AI_API_KEY,
        "id": rep_linkedin
    }

    response = requests.get(url, params=params)

    return response

def fetch_humantic_profile(rep_linkedin) -> requests.Response:
    url = f"{settings.HUMANTIC_AI_BASE_URL}"
    params = {
        "apikey": settings.HUMANTIC_AI_API_KEY,
        "id": rep_linkedin,
        "override": True
    }

    response = requests.get(url, params=params)
    return response