from __future__ import annotations

from database.database import Base

import uuid
from sqlalchemy import String, Boolean, Column, Text, ForeignKey, Table, TIMESTAMP, SmallInteger, Float, Integer
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func


########## ROLE PERMISSION MODELS ##########
role_permission = Table(
    'systemrole_permission',
    Base.metadata,
    Column('role_id', SmallInteger, ForeignKey('system_role.role_id')),
    Column('permission_id', UUID(as_uuid=True), ForeignKey('permission.permission_id')),
)

class SystemRole(Base):
    __tablename__ = 'system_role'

    role_id = Column(
        SmallInteger,
        primary_key=True,
        index=True,
        autoincrement=True
    )
    role_name = Column(String(32))
    role_description = Column(String(128))

    # Relationship
    user_account = relationship("User", secondary='user_role', back_populates="system_role")
    permission = relationship("Permission", secondary=role_permission, back_populates="system_role")

class Permission(Base):
    __tablename__ = 'permission'

    permission_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True
    )
    permission_name = Column(
        String, nullable=False
    )
    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )

    # Relationship
    system_role = relationship('SystemRole', secondary=role_permission, back_populates='permission')

########## USER AUTH MODELS ##########

user_role = Table(
    "user_role",
    Base.metadata,
    Column("user_id", UUID(as_uuid=True), ForeignKey("user.user_id", ondelete="CASCADE")),
    Column("role_id", SmallInteger, ForeignKey("system_role.role_id")),
)

class User(Base):
    __tablename__ = 'user'

    user_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True
    )
    user_info = Column(JSONB, default=None)

    user_linkedin_info = Column(JSONB, default=None)

    unipile_linkedin_id = Column(String, default=None)

    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    
    is_active = Column(Boolean, default=True)
    birth_year = Column(SmallInteger, nullable=True)
    email = Column(
        String(255),
        unique=True,
        nullable=False
    )
    password = Column(String(255), nullable=False)
    verified = Column(
        Boolean,
        nullable=False,
        server_default='False'
    )
    pwd_updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    token_generated_time = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    email_confirmation_status_id = Column(
        SmallInteger,
        ForeignKey('email_confirmation_status.status_id'),
        nullable=False,
        default=1
    )

    linkedin_connection_status = Column(
        String(255), 
        default="NOT_CONNECTED",
        nullable=True
    )

    # Relationships
    system_role = relationship("SystemRole", secondary=user_role, back_populates="user_account")
    email_confirmation_status = relationship("EmailConfirmationStatus", back_populates="users")

class EmailConfirmationStatus(Base):
    __tablename__ = 'email_confirmation_status'

    status_id = Column(
        SmallInteger,
        primary_key=True,
        index=True,
        autoincrement=True
    )
    status_value = Column(String(32))

    # Relationship
    users = relationship("User", back_populates="email_confirmation_status")

########## CREDIT MODELS ##########
class UserCredit(Base):
    __tablename__ = 'user_credits'

    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey('user.user_id', ondelete="CASCADE"),
        primary_key=True,
        nullable=False,
        index=True
    )

    bought_credits = Column(
        SmallInteger,
        nullable=False,
        default=0
    )

    current_credits = Column(
        SmallInteger,
        nullable=False,
        default=0
    )

    used_credits = Column(
        SmallInteger,
        nullable=False,
        default=0
    )

    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )


class UsedCredit(Base):
    __tablename__ = 'used_credits'

    used_credit_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True
    )

    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey('user.user_id', ondelete="CASCADE"),
        nullable=False,
        index=True
    )

    campaign_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True
    )

    company_id = Column(
        UUID(as_uuid=True),
        nullable=False,
        index=True
    )

    campaign_name = Column(
        String(255),
        nullable=False
    )

    company_name = Column(
        String(255),
        nullable=False
    )

    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.current_timestamp()        
    )


class CreditTransaction(Base):
    __tablename__ = 'credit_transactions'

    transaction_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True
    )

    stripe_checkout_session_id = Column(
        String(255),
        nullable=False,
        index=True,
        unique=True
    )

    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey('user.user_id', ondelete="CASCADE"),
        nullable=False,
        index=True
    )

    credit_amount = Column(
        SmallInteger,
        nullable=False
    )

    currency = Column(
        String(10),
        nullable=False
    )

    amount_subtotal = Column(
        Float,
        nullable=False
    )

    amount_tax = Column(
        Float,
        nullable=False,
        default=0
    )

    amount_total = Column(
        Float,
        nullable=False
    )    

    transaction_description = Column(
        String(255),
        nullable=True,
        default=None
    )

    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.current_timestamp()        
    )


class StripeProduct(Base):
    __tablename__ = 'products'

    product_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True
    )

    stripe_product_id = Column(
        String(255),
        nullable=False,
        index=True,
        unique=True
    )

    product_name = Column(
        String(255),
        nullable=False
    )

    product_description = Column(
        String(255),
        nullable=True,
        default=None
    )

    product_details = Column(
        JSONB,
        nullable=False
    )

    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.current_timestamp()        
    )

class StripePrice(Base):
    __tablename__ = 'prices'

    price_id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        nullable=False,
        default=uuid.uuid4,
        index=True
    )

    stripe_price_id = Column(
        String(255),
        nullable=False,
        index=True,
        unique=True
    )

    product_id = Column(
        UUID(as_uuid=True),
        ForeignKey('products.product_id', ondelete="CASCADE"),
        nullable=False,
        index=True
    )

    stripe_product_id = Column(
        String(255),
        ForeignKey('products.stripe_product_id', ondelete="CASCADE"),
        nullable=False,
        index=True
    )

    price_name = Column(
        String(255),
        nullable=False
    )

    price_description = Column(
        String(255),
        nullable=True,
        default=None
    )

    price_currency = Column(
        String(10),
        nullable=False
    )

    price_amount = Column(
        Float,
        nullable=False
    )

    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now()
    )

    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        server_default=func.now(),
        onupdate=func.current_timestamp()        
    )
