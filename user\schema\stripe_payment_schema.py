from typing import Optional, List, Dict, Any
from pydantic import BaseModel


class CheckoutSessionCreate(BaseModel):
    price_id: str
    cancel_url: str
    success_url: str
    

class CheckoutSessionObject(BaseModel):
    id: Optional[str]
    object: Optional[str]
    after_expiration: Optional[Any]
    allow_promotion_codes: Optional[Any]
    amount_subtotal: Optional[int]
    amount_total: Optional[int]
    automatic_tax: Optional[Dict[str, Any]]
    billing_address_collection: Optional[str]
    cancel_url: Optional[str]
    client_reference_id: Optional[str]
    consent: Optional[Any]
    consent_collection: Optional[Any]
    created: Optional[int]
    currency: Optional[str]
    custom_fields: Optional[List[Any]]
    custom_text: Optional[Dict[str, Any]]
    customer: Optional[str]
    customer_creation: Optional[str]
    customer_details: Optional[Any]
    customer_email: Optional[str]
    expires_at: Optional[int]
    invoice: Optional[str]
    invoice_creation: Optional[Dict[str, Any]]
    livemode: Optional[bool]
    locale: Optional[str]
    metadata: Optional[Dict[str, Any]]
    mode: Optional[str]
    payment_intent: Optional[str]
    payment_link: Optional[str]
    payment_method_collection: Optional[str]
    payment_method_options: Optional[Dict[str, Any]]
    payment_method_types: Optional[List[str]]
    payment_status: Optional[str]
    phone_number_collection: Optional[Dict[str, Any]]
    recovered_from: Optional[Any]
    setup_intent: Optional[str]
    shipping_address_collection: Optional[Any]
    shipping_cost: Optional[Any]
    shipping_details: Optional[Any]
    shipping_options: Optional[List[Any]]
    status: Optional[str]
    submit_type: Optional[str]
    subscription: Optional[str]
    success_url: Optional[str]
    total_details: Optional[Dict[str, Any]]
    url: Optional[str]

    class Config:
        orm_mode = True


class EventObject(BaseModel):
    id: Optional[str]
    object: Optional[str]
    api_version: Optional[str]
    created: Optional[int]
    data: Dict[str, Any]
    livemode: bool
    pending_webhooks: Optional[int]
    request: Optional[Dict[str, Optional[str]]]
    type: Optional[str]
    class Config:
        orm_mode = True
