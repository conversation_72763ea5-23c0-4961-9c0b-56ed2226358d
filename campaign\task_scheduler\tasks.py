import asyncio
import json
from loguru import logger

from database.models import Sender<PERSON>mail, Company, Representative, Draft, LinkedInConnection, Campaign
from database.database import get_session
from message_bus.task_publisher import publish_task
from shared_state import app_state
from message_bus.rpc_client import RPCClient
from utils.unipile_utils.unipile_client import LinkedInClient

from datetime import timedelta, date, datetime
from sqlalchemy.future import select
from sqlalchemy import update
from sqlalchemy.ext.asyncio import  AsyncSession

async def reset_remaining_emails():
    await asyncio.sleep(1)
    async for db in get_session():
        try:
            stmt = update(SenderEmail).values(remaining_emails=21)
            await db.execute(stmt)
            await db.commit()
            logger.info("[CRON JOB] Remaining emails reset successfully")
        except Exception as e:
            await db.rollback()
            raise e

async def get_sender(db: AsyncSession):
    try:
        query = select(SenderEmail).filter(SenderEmail.remaining_emails>0).order_by(SenderEmail.remaining_emails.desc(), SenderEmail.updated_at.asc())
        results = await db.execute(query)
        senders = results.scalars().all()
        if len(senders) == 0:
            return None
        else:
            sender = senders[0]
            return {
                "sender_address": sender.sender_address,
                "unipile_account_id": sender.unipile_account_id
            }
        # return {
        #     "sender_address": "<EMAIL>",
        #     "unipile_account_id": "DVuozmRlTw-my9ix8ZTJmg"
        # }
    except Exception as e:
        raise e

async def start_auto_outreach():
    await asyncio.sleep(1)
    try:
        today = date.today()
        if today.weekday() in [5,6]:
            return
        async for db in get_session():
            #get campaign with auto_outreach
            query = (
                select(Campaign)
                .filter(Campaign.campaign_auto_outreach==True)
                .order_by(Campaign.created_at.asc())
            )
            results = await db.execute(query)
            campaigns = results.scalars().all()

            for campaign in campaigns:
                query = select(Company).filter(Company.campaign_id==campaign.campaign_id, Company.auto_outreach==False)
                results = await db.execute(query)
                companies = results.scalars().all()
                if len(companies) > 0:
                    # auto_send_linkedin = False

                    # rpc_client = RPCClient()
                    # rpc = await rpc_client.connect()
                    # function_name = "get_user_info"
                    # response = await rpc.call(service="user.*", function_name=function_name, params={"user_id": str(campaign.user_id)})
                    # response_body = json.loads(response)
                    # result = response_body.get("result")
                    # if result != "error":
                    #     user_info = result
                    #     if user_info.get("unipile_linkedin_id") is not None:
                    #         linkedin_client = LinkedInClient()
                    #         linkedin_client.set_unipile_account_id(user_info["unipile_linkedin_id"])        
                    #         own_profile = linkedin_client.retrieve_own_profile().json()
                    #         if "provider_id" in own_profile:
                    #             auto_send_linkedin = True
                    query = (
                        select(Company)
                        .filter(Company.campaign_id==campaign.campaign_id, Company.auto_outreach==False)
                        .order_by(Company.company_name.asc())
                        .limit(10)
                    )
                    results = await db.execute(query)
                    companies = results.scalars().all()

                    for company in companies:
                        query = select(Representative).filter(Representative.company_id==company.company_id)
                        results = await db.execute(query)
                        representative = results.scalars().first()

                        query = select(Draft).filter(Draft.company_id==company.company_id, Draft.draft_type==0)
                        results = await db.execute(query)
                        draft = results.scalars().first()

                        if representative.personality_analysis is None:                        
                                message = {
                                    "rep_linkedin": representative.rep_linkedin_address,
                                    "company_id": str(company.company_id),
                                    "auto_outreach": True,
                                    # "auto_send_email": True,
                                    # "auto_send_linkedin": auto_send_linkedin
                                }
                                await publish_task("update_humantic_profile", message, connection_pool=app_state["rabbitmq_conn_pool"])

                        elif draft is None:
                            message = {
                                "campaign_id" : str(campaign.campaign_id),
                                "company_id": str(company.company_id),
                                "prompt_name": campaign.prompt_name,
                                # "auto_send_email": True,
                                # "auto_send_linkedin": auto_send_linkedin,
                                "auto_outreach": True,
                                "user_id": str(campaign.user_id)
                            }
                            await publish_task("create_drafts", message, connection_pool=app_state["rabbitmq_conn_pool"])

                        elif draft is not None:
                            stmt = update(Company).where(Company.company_id==company.company_id).values(auto_outreach=True)
                            await db.execute(stmt) 
                            await db.commit()

                        # else:
                        #     if draft.email_sent == False:
                        #         email_outreach_message = {
                        #             "company_id": str(company.company_id)
                        #         }
                        #         await publish_task("send_email_outreach", email_outreach_message, connection_pool=app_state["rabbitmq_conn_pool"])

                        #     if auto_send_linkedin == True and draft.linkedin_sent == False:
                        #         linkedin_outreach_message = {
                        #             "company_id": str(company.company_id),
                        #             "user_id": str(campaign.user_id)
                        #         }
                        #         await publish_task("send_linkedin_outreach", linkedin_outreach_message, connection_pool=app_state["rabbitmq_conn_pool"])
    except Exception as e:
        logger.error(f"[CRON JOB] Failed to run handle_auto_outreach job: {str(e)}")


async def continue_auto_linkedin_outreach():
    await asyncio.sleep(1)
    try:
        today = date.today()
        if today.weekday() in [5,6]:
            return        
        async for db in get_session():
            #get campaign with auto_outreach
            query = (
                select(Campaign)
                .filter(Campaign.campaign_auto_outreach==True)
                .order_by(Campaign.created_at.asc())
            )
            results = await db.execute(query)
            campaigns = results.scalars().all()

            for campaign in campaigns:
                auto_send_linkedin = False                
                rpc_client = RPCClient()
                rpc = await rpc_client.connect()
                function_name = "get_user_info"
                response = await rpc.call(service="user.*", function_name=function_name, params={"user_id": str(campaign.user_id)})
                response_body = json.loads(response)
                result = response_body.get("result")
                if result != "error":
                    user_info = result
                    if user_info.get("unipile_linkedin_id") is not None:
                        linkedin_client = LinkedInClient()
                        linkedin_client.set_unipile_account_id(user_info["unipile_linkedin_id"])        
                        own_profile = linkedin_client.retrieve_own_profile().json()
                        if "provider_id" in own_profile and user_info.get("linkedin_connection_status") == "CONNECTED":
                            auto_send_linkedin = True
                        else:
                            logger.warning(f"[CRON JOB] User {campaign.user_id} is not connected to linkedin")
                    else:
                        logger.warning(f"[CRON JOB] User {campaign.user_id} is not connected to linkedin")
                else:
                    logger.warning(f"[CRON JOB] Failed to get user info: {result}")

                if auto_send_linkedin == True:
                    # query = (
                    #     select(Company)
                    #     .filter(Company.campaign_id==campaign.campaign_id, Company.auto_outreach==True, Company.linkedin_message_status_id.notin_([4,5]))
                    #     .order_by(Company.company_name.asc(), Company.outreach_progress.asc())
                    # )
                    query = (
                        select(Company)
                        .join(Draft, Draft.company_id == Company.company_id)
                        .filter(
                            Draft.linkedin_sent == False,
                            Company.campaign_id == campaign.campaign_id,
                            Company.auto_outreach == True,
                            Company.linkedin_message_status_id.notin_([4, 5])
                        )
                        .distinct(Company.company_id)
                        .order_by(Company.company_id.asc(), Company.company_name.asc(), Draft.draft_type.asc())
                    )
                    
                    results = await db.execute(query)
                    companies = results.scalars().all()
                    logger.info(f"[CRON JOB] Number of companies: {len(companies)}")

                    for company in companies:
                        logger.info(f"[CRON JOB] Processing company: {company.company_name}")
                        query = select(Representative).filter(Representative.company_id==company.company_id)
                        results = await db.execute(query)
                        representative = results.scalars().first()

                        if representative.rep_linkedin_address is None:
                            continue

                        query = select(Draft).filter(Draft.company_id==company.company_id, Draft.linkedin_sent==False).order_by(Draft.draft_type.asc())
                        results = await db.execute(query)
                        draft = results.scalars().first()

                        if draft is not None:
                            today = date.today()
                            now = datetime.now()

                            to_send = False

                            # last_time_sent = company.last_time_sent_linkedin
                            # if last_time_sent == None:
                            #     to_send = True   
                            # else:                        
                            #     time_to_send = last_time_sent + timedelta(minutes=draft.day_to_send)
                            #     if time_to_send <= now:
                            #         to_send = True

                            last_time_sent = company.last_date_sent_linkedin
                            if last_time_sent == None:
                                to_send = True
                            else:
                                time_to_send = last_time_sent + timedelta(days=draft.day_to_send)
                                if time_to_send <= today:
                                    to_send = True     

                            if to_send:
                                message = {
                                    "company_id": str(company.company_id),
                                    "user_id": str(campaign.user_id)
                                }
                                await publish_task("send_linkedin_outreach", message, connection_pool=app_state["rabbitmq_conn_pool"])
    except Exception as e:
        logger.error(f"[CRON JOB] Failed to run continue_auto_outreach job: {str(e)}")


async def continue_auto_email_outreach():
    await asyncio.sleep(1)
    try:
        today = date.today()
        if today.weekday() in [5,6]:
            return
        async for db in get_session():
            #check if there are any senders available
            sender = await get_sender(db=db)
            if sender is None:
                logger.warning("[CRON JOB] No more email senders available")
                return

            #get campaign with auto_outreach
            query = (
                select(Campaign)
                .filter(Campaign.campaign_auto_outreach==True)
                .order_by(Campaign.created_at.asc())
            )
            results = await db.execute(query)
            campaigns = results.scalars().all()

            for campaign in campaigns:
                # query = (
                #     select(Company)
                #     .filter(Company.campaign_id==campaign.campaign_id, Company.auto_outreach==True, Company.email_confirmation_status_id.notin_([6,7]))
                #     .order_by(Company.company_name.asc(), Company.outreach_progress.asc())
                #     .limit(1)
                # )
                query = (
                    select(Company)
                    .join(Draft, Draft.company_id == Company.company_id)
                    .filter(
                        Draft.email_sent == False,
                        Company.campaign_id == campaign.campaign_id,
                        Company.auto_outreach == True,
                        Company.email_confirmation_status_id.notin_([6, 7])
                    )
                    .distinct(Company.company_id)
                    .order_by(Company.company_id.asc(), Company.company_name.asc(), Draft.draft_type.asc())
                    .limit(10)
                )

                results = await db.execute(query)
                companies = results.scalars().all()
                logger.info(f"[CRON JOB] Number of companies: {len(companies)}")

                for company in companies:
                    logger.info(f"[CRON JOB] Processing company: {company.company_name}")
                    query = select(Representative).filter(Representative.company_id==company.company_id)
                    results = await db.execute(query)
                    representative = results.scalars().first()

                    if representative.rep_email is None:
                        continue

                    query = select(Draft).filter(Draft.company_id==company.company_id, Draft.email_sent==False).order_by(Draft.draft_type.asc())
                    results = await db.execute(query)
                    draft = results.scalars().first()
                    # logger.info(f"[CRON JOB] Draft: {draft}")
                    if draft is not None:
                        today = date.today()
                        now = datetime.now()

                        to_send = False

                        # last_time_sent = company.last_time_sent_email
                        # if last_time_sent == None:
                        #     to_send = True
                        # else:
                        #     time_to_send = last_time_sent + timedelta(minutes=draft.day_to_send)
                        #     if time_to_send <= now:
                        #         to_send = True

                        last_time_sent = company.last_date_sent_email                        
                        if last_time_sent == None:
                            to_send = True
                        else:
                            time_to_send = last_time_sent + timedelta(days=draft.day_to_send)
                            if time_to_send <= today:
                                to_send = True   

                        if to_send:
                            message = {
                                "company_id": str(company.company_id)
                            }
                            await publish_task("send_email_outreach", message, connection_pool=app_state["rabbitmq_conn_pool"])
    except Exception as e:
        logger.error(f"[CRON JOB] Failed to run continue_auto_email_outreach job: {str(e)}")


