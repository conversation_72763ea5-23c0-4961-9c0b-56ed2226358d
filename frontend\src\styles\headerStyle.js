import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const Header = () => {
  const navigate = useNavigate();
  const [showCampaignMenu, setShowCampaignMenu] = useState(false);
  const [showProfileMenu, setShowProfileMenu] = useState(false);

  // Handling Functions
  const handleLogout = () => {
    localStorage.removeItem('isLoggedIn');
    navigate('/login');
  };

  const handleViewInformation = () => {
    navigate('/situation');
  };

  const findData = () => {
    console.log("Find Data function called");
    // Implementation to be added later
  };

  const newCampaign = () => {
    navigate('/new-campaign');
  };

  const myCampaign = () => {
    console.log("My Campaign function called");
    // Implementation to be added later
  };

  const dataLibrary = () => {
    console.log("Data Library function called");
    // Implementation to be added later
  };

  return (
    <header style={headerStyle}>
      <div style={logoStyle}>
        {/* Image placeholder */}
        <div style={{ width: 100, height: 40, backgroundColor: 'gray' }}></div>
      </div>

      <div style={navStyle}>
        <button onClick={findData} style={buttonStyle}>Find Data</button>
        <div style={{ position: 'relative' }}>
          <button onClick={() => setShowCampaignMenu(!showCampaignMenu)} style={buttonStyle}>
            Campaign <span style={{ marginLeft: '5px' }}>▼</span>
          </button>
          {showCampaignMenu && (
            <div style={dropdownStyle}>
              <button onClick={newCampaign} style={dropdownItemStyle}>New Campaign</button>
              <button onClick={myCampaign} style={dropdownItemStyle}>My Campaign</button>
            </div>
          )}
        </div>
        <button onClick={dataLibrary} style={buttonStyle}>Data Library</button>
      </div>

      <div style={profileStyle}>
        <div style={profileBoxStyle} onClick={() => setShowProfileMenu(!showProfileMenu)}>
          <div style={profileImageStyle}></div>
          <span style={arrowStyle}>▼</span>
        </div>
        {showProfileMenu && (
          <div style={profileMenuStyle}>
            <button style={profileMenuItemStyle} onClick={handleViewInformation}>View Information</button>
            <button style={profileMenuItemStyle} onClick={handleLogout}>Logout</button>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;


const headerStyle = {
    display: 'flex',
    width: '100%',
    height: '80px',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '0 50px',
    backgroundColor: '#223F9E',
    color: 'white',
    boxSizing: 'border-box',
};
  
const logoStyle = {
    flex: '0 0 150px',
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'center',
};
  
const navStyle = {
    flex: '1 1 auto',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    gap: '20px',
};

const profileStyle = {
    flex: '0 0 150px',
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    position: 'relative',
};
  
const buttonStyle = {
    backgroundColor: 'transparent',
    padding: "15px",
    border: 'none',
    color: 'white',
    cursor: 'pointer',
    fontSize: '16px',
};
  
const dropdownStyle = {
    position: 'absolute',
    top: '100%',
    backgroundColor: 'white',
    border: '1px solid #ddd',
    borderRadius: '4px',
    zIndex: 1000,
};
  
const dropdownItemStyle = {
    display: 'block',
    width: '150px',
    padding: '15px',
    textAlign: 'left',
    border: 'none',
    backgroundColor: 'transparent',
    cursor: 'pointer',
    color: '#333',
};
  
  
const profileBoxStyle = {
    display: 'flex',
    alignItems: 'center',
    cursor: 'pointer',
    backgroundColor: 'transparent',
};
  
const profileImageStyle = {
    width: '40px',
    height: '40px',
    borderRadius: '50%',
    backgroundColor: '#ddd',
    marginRight: '5px',
};
  
const arrowStyle = {
    fontSize: '12px',
    marginLeft: '5px',
};
  
const profileMenuStyle = {
    position: 'absolute',
    top: '100%',
    right: 0,
    backgroundColor: 'white',
    border: '1px solid #ddd',
    borderRadius: '4px',
    zIndex: 1000,
};
  
const profileMenuItemStyle = {
    display: 'block',
    width: '150px',
    padding: '10px',
    textAlign: 'left',
    border: 'none',
    backgroundColor: 'transparent',
    cursor: 'pointer',
    color: '#333',
};