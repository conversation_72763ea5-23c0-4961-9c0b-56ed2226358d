import aio_pika
from aio_pika.pool import Pool
from aio_pika.abc import AbstractRobustConnection, AbstractChannel
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RabbitMQConnectionPool:
    def __init__(self, dsn: str, max_connections: int = 200, max_channels: int = 300):
        """
        Initialize the RabbitMQ connection pool.

        :param dsn: RabbitMQ connection string (e.g., "amqp://guest:guest@localhost/")
        :param max_connections: Maximum number of connections in the pool
        :param max_channels: Maximum number of channels per connection
        """
        self.dsn = dsn
        self.max_connections = max_connections
        self.max_channels = max_channels

        # Connection pool
        self.connection_pool = Pool(self.create_connection, max_size=max_connections)
        # Channel pool
        self.channel_pool = Pool(self.create_channel, max_size=max_channels)

    async def create_connection(self) -> AbstractRobustConnection:
        """Create a new robust RabbitMQ connection."""
        try:
            logger.info("Creating a new RabbitMQ connection...")
            return await aio_pika.connect_robust(
                self.dsn,
                reconnect_interval=5,  # Retry every 5 seconds
                timeout=10,           # Connection timeout
            )
        except Exception as e:
            logger.error(f"Failed to create RabbitMQ connection: {e}")
            raise

    async def create_channel(self) -> AbstractChannel:
        """Create a channel from a pooled connection."""
        try:
            logger.info("Creating a new RabbitMQ channel...")
            async with self.connection_pool.acquire() as connection:
                return await connection.channel()
        except Exception as e:
            logger.error(f"Failed to create RabbitMQ channel: {e}")
            raise

    async def get_channel(self) -> AbstractChannel:
        """Acquire a channel from the pool."""
        try:
            logger.info("Acquiring a RabbitMQ channel...")
            return await self.channel_pool.acquire()
        except Exception as e:
            logger.error(f"Failed to acquire RabbitMQ channel: {e}")
            raise

    async def close(self):
        """Close the pools and all connections."""
        try:
            logger.info("Closing RabbitMQ connection pool...")
            await self.channel_pool.close()
            await self.connection_pool.close()
        except Exception as e:
            logger.error(f"Error while closing RabbitMQ connection pool: {e}")
            raise

    async def release_channel(self, channel: AbstractChannel):
        """Release a channel back to the pool."""
        try:
            logger.info("Releasing RabbitMQ channel back to the pool...")
            await self.channel_pool.release(channel)
        except Exception as e:
            logger.error(f"Error while releasing RabbitMQ channel: {e}")
            raise